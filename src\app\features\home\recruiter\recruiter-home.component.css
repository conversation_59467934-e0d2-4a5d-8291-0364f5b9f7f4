/* New Theme */
.NewTheme {
  padding:70px 100px;
  background: #14C59C;
}
.NewTheme .heading {
  margin: 0;
  padding: 0;
  color: #000000;
  font-size: 60px;
  text-align: left;
  font-weight: bold !important;
}
.NewTheme h2 {
  margin-top: 41px;
  color: #000000;
  font-size: 60px;
  text-align: left;
  font-weight: 400 !important;
}
.NewThemeSearch h3 {
  padding: 0;
  color: #434343;
  font-size: 34px;
  text-align: center;
  margin: 0 0 15px 0;
  font-weight: bold !important;
}
.NewThemeSearch h3 span {
  color: #434343;
  text-align: left;
  font-weight: 500 !important;
}
.registerBtn{
  position: relative;
  top:-35px;
}

.NewTheme span {
  font-weight: 700 !important;
}
.NewTheme .btn{
  margin-top: 30px;
  font-weight: 500!important;
  font-size:24px!;
}
.btn-primary {
  background-color: #0e1b5c;
  border-color: #0e1b5c;
  margin-bottom: 10px;
}
.btn-primary:hover {
  background-color: #0e1b5c!important;
  border-color: #14c59c!important;
  color:#f2f2f2!important;
}

.TrustedBy{
  padding:0;
  margin: 0;
  color:#0e1b5c;
  font-size: 28px;
  font-weight: 600!important;
}
.text-dark {
  color:#000000!important;
}
.carousel-indicators li{
  width:12px!important;
  height:12px!important;
  border-radius: 100%;
}
/* New Theme */
.NewTheme .card {
  border: 1 px solid;
  background-color: #0e1b5c;
  border-color: #0e1b5c;
}
/* NewThemeSearch */
.NewThemeSearch{
  padding:70px 100px;
  background: #f9f9f9;
  border-bottom: 1px solid #e4e4e4;
}
.NewThemeSearch .card h4{
  font-weight: bold!important;
  margin-top: 10px;
  color: #000;
  }
  
.NewThemeSearch .card {
  border:  1px solid;
  border-color: #ccc;
  border-radius: 8px;
  margin-bottom: 20px;
}
.img-thumbnail {
  padding: 25px;
  border-radius: 25px;
}
.NewThemeSearch .card .card-header-morejobs {
  background-color: #0e1b5c!important;
  padding: 10px 0!important;
  border-top-left-radius: 7px;
  border-top-right-radius:7px;
  width: 100%;
}
.NewThemeSearch .card .card-header-morejobs .title{
  color:#ffffff!important;
  float: left;
  font-size: 24px;
  font-weight: bold;
  margin-left: 20px;
}
.mobile-view .NewThemeSearch .card .card-header-morejobs a {
  color: #ffffff !important;
  float: right;
  font-size: 15px;
  margin-right: 15px;
}
.NewThemeSearch .card .card-header-morejobs a{
  color:#ffffff!important;
  float: right;
  font-size: 15px;
  margin-right: 15px;
  padding-top: 4px;
}
.NewThemeSearch .card .card-header-morejobs a:hover{
  color: #14c59c!important;
  text-decoration: underline;
}
.HelpAdvice .card {
  border:  1px solid;
  margin-bottom: 20px !important;
}
.NewThemeSearch h1{
  margin: 0;
  padding: 0;
  color:#0e1b5c;
  font-size: 34px;
  text-align: center;
  font-weight: bold!important;
}
.NewThemeSearch h2{
  padding: 0;
  color:#434343;
  margin: 30px 0px 15px 0px;
  font-size: 30px;
  text-align: left;
  text-align: center;
  font-weight: 500!important;
}
.NewThemeSearch h2 span{
  text-align: left;
  font-weight: bold;
}
.NewThemeSearch h3{
  padding: 0;
  color:#434343;
  font-size: 34px;
  text-align: center;
  margin: 0 0 15px 0;
  font-weight: bold!important;
}
.NewThemeSearch h3 span{
  color:#434343;
  text-align: left;
  font-weight: 500!important;
}
.NewThemeSearch .MobileSPC{
  float: right;
}
.NewThemeSearch .TxtLink a{
  color:#545454;
  font-size: 16px;
  text-decoration: none;
}
.NewThemeSearch .TxtLink a:hover{
  color:#0e1b5c;
  text-decoration: none;
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  color: #fff!important;
  background-color: #0e1b5c!important;
}
#accordion .panel {
  border: none;
  border-radius: 3px;
  box-shadow: none;
}
.searchmore .panel-body {
 background: #0e1b5c !important;
 min-height: 300px;
}
.Registration .panel-body {
 background: #0e1b5c !important;
}
.HelpAdvice .VideoCard{
 min-height:370px;
}
.searchmore .panel-body {
 background: #0e1b5c !important;
 min-height: 300px;
}
.Registration .panel-body {
 background: #0e1b5c !important;
}
.HelpAdvice .VideoCard{
 min-height:370px;
}
.panel-body p{
  color:#ffffff;
}
.panel-body p span{
  color:#ffffff!important;
  font-weight: 700!important;
}
.panel-body .btn-primary {
  background-color:#14C59C;
  color:#0e1b5c;
  font-weight: 500!important;
}
#accordion .panel-body {
  border: none;
  padding: 15px;
  line-height: 29px;
  /* margin-bottom: 10px; */
}
.accordion .card-header .title:hover {
  cursor: auto;
}

#accordion .panel-body ul {
  margin-top: 2px;
  margin-bottom: 0px;
  padding: 10px;
  list-style: none;
}
.btn-success {
  border-color: #14C59C;
  background-color: #14C59C;
}
#accordion .panel-body ul li a{
  color:#14C59C;
  font-size: 15px;
  text-decoration: none;
  padding: 10px !important;
}
#accordion .panel-body ul li a:hover{
  color:#fff;
  text-decoration: underline;
}
.jobs {
  padding-top: 10px;
  padding-bottom: 10px;
}
/* NewThemeSearch */

/*Registration*/
.Registration{
  padding:50px 100px;
  background: #ffffff;
}
.Registration h1{
  padding: 0;
  margin: 0 0 15px 0;
  color:#434343;
  font-size: 28px;
  text-align: center;
}
.Registration h1 span{
  color:#434343;
  font-weight: bold!important;
}
.Registration h2{
  padding: 0;
  margin: 0 0 15px 0;
  color:#0e1b5c;
  font-size: 30px;
  text-align: center;
}
.Registration h2 span{
  color:#0e1b5c;
  font-weight: bold!important;
}
.Registration p span{
  color:#545454;
  font-weight: bold;
}
/*Registration*/
.accordion .card-header .title {
  color: #fff;
  float: left;
  padding: 6px 15px;
  padding-right: 0;
  font-weight: 500 !important;
}

.accordion .card-header .accicon {
  font-size: 20px;
  width: 1.2em;
  text-align: center;
  color: #14C59C;
  margin-right: 2px;
}

.accordion .card-header {
  cursor: pointer;
  padding: 0px !important;
}

.accordion .card {
  border-radius: 10px;
}
.accordion .customDesign {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.accordion .customDesign2 {
 border-radius: 0;
}
.accordion .customDesign3 {
  border-top-left-radius:  0;
  border-top-right-radius: 0;
}
.accordion .card-body {
  padding: 10px;
}

.accordion .card-header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

span.accicon {
  float: right;
  position: relative;
}

.accordion .card-body {
  padding: 0px !important;
  border-top: 0px !important;
}
/******************* Accordion Demo - 8 *****************/
#accordion8 .panel {
  border: none;
  box-shadow: none;
  border-radius: 0;
  /* margin-bottom: 15px; */
}

#accordion8 .panel-body {
  border: none;
  color: #545454;
  padding: 15px;
  font-size: 15px;
  line-height: 25px;
  background: #fff;
  transition: all 0.5s ease 0s;
}


/******************* Accordion Demo - 8 *****************/
#accordion8 .panel{
  border: none;
  box-shadow: none;
  border-radius: 0;
  /* margin-bottom: 15px; */
}
#accordion8 .panel-heading{
  padding: 0;
  border-radius: 0;
  border: none;
}
#accordion8 .panel-title{
  margin: 0px;
}
#accordion8 .panel-title a{
  display: block;
  padding: 15px;
  background: #f9f9f9;
  font-size: 16px;
  font-weight: 700;
  color: #545454;
  position: relative;
  transition: all 0.5s ease 0s;
}
#accordion8 .panel-title a.collapsed{
  color: #545454;
  background: #f9f9f9;
}
#accordion8 .panel-title a:after,
#accordion8 .panel-title a.collapsed:after{
  top: 25%;
  right: 15px;
  width: 25px;
  height: 25px;
  content: "\f106";
  font-weight: 900;
  line-height: 30px;
  border-radius: 5px;
  font-size: 20px;
  color: #545454;
  text-align: center;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}
#accordion8 .panel-title a.collapsed:after{
  content: "\f107";
}
#accordion8 .panel-title a.collapsed:hover{
  color: #545454;
  background: #f9f9f9;
  text-decoration: none;
}
#accordion8 .panel-body{
  border: none;
  color: #545454;
  padding: 15px;
  font-size: 15px;
  line-height: 25px;
  background: #fff;
  transition: all 0.5s ease 0s;
}
/*Help & Advice */
.HelpAdvice{
  padding:50px 100px!important;
  background: #14C59C;
}
.HelpAdvice .h1{
  padding: 0;
  color:#060E37;
  font-size: 36px;
  margin: 0 0 15px 0;
  text-align: center;
}
.HelpAdvice .h1 span{
  color:#060E37;
  font-weight: bold!important;
}
.HelpAdvice h2{
  padding: 0;
  margin: 0 0 15px 0;
  color:#060E37;
  font-size: 36px;
  text-align: center;
  font-weight: 400!important;
}
.HelpAdvice h2 span{
  color:#060E37;
  font-weight: bold!important;
}
.HelpAdvice p a{
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
}
.HelpAdvice p a:hover{
  color: #fff;
  text-decoration: underline;
}
.HelpAdvice .indicators {
  position: relative!important;
  width: 300px;
}
#accordion2 .panel-title{
  margin: 0px;
}
#accordion2 .panel-title a{
  color: #fff;
  padding: 15px;
  display: block;
  font-size: 18px;
  font-weight: 500;
  position: relative;
  background: #060E37;
}
#accordion2 .panel-title a:before{
  content: "\f106";
  font-family: "Font Awesome 5 Free";
  width: 25px;
  height: 25px;
  line-height: 25px;
  font-size: 20px;
  font-weight: 900;
  color: #fff;
  text-align: center;
  position: absolute;
  top: 15px;
  right: 15px;
}
#accordion2 .panel-title a.collapsed:before{content: "\f107";}
#accordion2 .panel-body{
  padding: 15px 15px;
  font-size: 15px;
  color: #fff;
  line-height: 27px;
  border: none;
  background:#0e1b5c;
}
#accordion2 .panel-body p{ margin-bottom: 0;}
#accordion2 .panel-title a.collapsed:hover{
  color: #fff;
  background: #060E37;
  text-decoration: none;
}
.smallest {
  color:#0e1b5c;
  padding-top: 20px;
  font-weight: 600!important;
}
/* .PartnerBox .item{left: 0;top: 0;position: relative;overflow: hidden;}
.PartnerBox .item img{-webkit-transition: 0.6s ease;transition: 0.6s ease;}
.PartnerBox .item img:hover{-webkit-transform: scale(1.2);transform: scale(1.2);} */
.PartnerBox .carousel-indicators{bottom: -80px;}
.PartnerBox .btn{margin-top: 80px;}
.PartnerBox .img-thumbnail{border:0px;border-radius:50%;}

/*Help & Advice */
:host ::ng-deep app-browse-by-industry .IndustriesListing ul li a {
  color: #14C59C !important;
  font-weight: normal !important;
}
:host ::ng-deep app-browse-by-industry .IndustriesListing ul li a:hover {
  color: #fff !important;
  text-decoration: underline!important;
}

:host ::ng-deep app-browse-by-location-item .JobsListing ul li a {
  color: #14C59C !important;
  font-weight: normal !important;
}
:host ::ng-deep app-browse-by-location-item .JobsListing ul li a:hover {
  color: #fff !important;
  text-decoration: underline;
}

:host ::ng-deep app-browse-by-location-item .JobsListing ul li {
  width: 100% !important;
  line-height: 30px;
  margin-bottom: 0px;
}

:host ::ng-deep app-browse-by-location-item .JobsListing ul {
  padding: 0px 10px 0px 10px !important;
}

:host ::ng-deep app-browse-by-location-item .JobsListing ul h3 {
  font-weight: normal !important;
}

:host ::ng-deep app-browse-by-industry .IndustriesListing ul h1 {
  font-weight: normal !important;
}

:host ::ng-deep app-browse-by-industry .IndustriesListing ul li {
  margin-bottom: 0px !important;
  line-height: 30px !important;
}

:host ::ng-deep app-browse-by-industry .row {
  padding: 0px !important;
}
.btnshowmore {
  color: #ffff !important;
  padding: 0 19px;
}
.VideoCard{
  padding:10px;
  text-align: center;
  background:#0e1b5c;
  color:#ffff;
}
.VideoCard .card-body{
  padding:0px;
}
.VideoCard h2{
  color:#fff;
  font-size: 16px;
  font-weight: 600;
}
.VideoCard p{
  font-size: 15px;
  text-align: center;
  color:#ffff;
  min-height: 135px;
  text-decoration: none;
}
.VideoCard p a{
  text-decoration: none;
}
.VideoCard p a:hover{
  color:black;
  text-decoration: none;
}
.VideoCard img{
  width:100%;
  height:auto;
  margin-bottom: 10px;
}
.VideoCard .card-body a:hover{
  text-decoration: none;
}

.VideoCard .card-body a h2:hover{
  text-decoration: underline!important;
}
.NewThemeSearch .LatestCVs{
  margin:0 0px!important;
}
.NewThemeSearch .FeaturedCVs{
  margin:0 0px!important;
}
.btn:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}
.card-header2 .title {
  color: #fff;
  font-size: 15px;
}
.card-header2 .accicon {
  float: right;
  font-size: 20px;
  width: 1.2em;
}
.card-header2 {
  color: #fff;
  cursor: pointer;
  border-bottom: none;
  background: #3465b7;
  padding: 6px 0 6px 10px;
}
.card {
  border: 1px solid #9ac1ff;
}
.card-body2 {
  padding: 10px;
  font-size: 14px;
  border-top: 1px solid #9ac1ff;
}
.card-header2:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}
.CardBox a { 
  text-decoration: none;
  font-size: 14px !important;
}
.CardBox a:hover {  
  text-decoration: none;
}
.card-header {
  background: #0e1b5c !important;
  padding: 35px 0px !important;
  text-align: center;
}

.card-header2 {
  background: #f9f9f9 !important;
  padding: 35px 0px !important;
  text-align: center;
}
.card-header img {
  width: 100% !important;
  height: auto;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
} 
.ShowSlideDown {
  visibility: visible;
  max-height: 20000px;
  opacity: 1;
  transition: all 0.5s ease-in;
}
.HideSlideDown {
  visibility: hidden;
  max-height: 0px;
  opacity: 0;
  overflow: hidden;
  max-height: 0px;
  transition: all 0.9s ease-out;
}
.FastHide {
  transition: all 0.5s ease-out;
}

:host ::ng-deep app-smart-location-autocomplete .target-icon{
margin-top: -37px;
}
.LatestCVs .carousel-indicators{
  position: relative;
}
.LatestCVs .carousel-indicators li{
  background-color: #0e1b5c;
}
.blog .carousel-indicators{
  position: relative;
}
.blog .carousel-indicators li{
  background-color: #0e1b5c;
}
.blog .allBlogs{
  background-color: #14C59C !important;
  border-color: #14C59C !important; 
  font-weight: 600 !important;
  margin-bottom: 10px;
  margin-top: 34px;
}

/**/
.card {
	margin: 0 auto;
	border: none;
}
.card .carousel-item {
	min-height: 190px;
}
.card2 .carousel-item {
	min-height: 120px;
}
.CardBorder {
  padding: 40px 20px;
  text-align: center;
  border-radius: 6px;
  background: #0e1b5c;
}
.card .carousel-caption {
	padding: 0;
	right: 15px;
	left: 15px;
	top: 15px;
	color: #000;
	border: 1px solid #14C59C;
  background-color: #14C59C;
	min-height:175px;
	padding: 15px;
  border-radius: 10px;
}
.card .carousel-caption .col-sm-3 {
	display: flex;
	align-items: center;
}
.card .carousel-caption .col-sm-9 {
	text-align: left;
}
.card .carousel-caption h2 {
  color: #0e1b5c !important;
  font-size: 25px;
  font-weight: 600!important;
  margin-bottom: 10px;
}
.card .carousel-control-prev, .card .carousel-control-next {
	color: #3d3d3d !important;
	opacity: 1 !important;
}
.carousel-control-prev-icon, .carousel-control-next-icon {
	background-image: none;
	color: #0e1b5c;
	font-size: 14px;
	background-color: #14C59C;
	height: 32px;
	line-height: 32px;
	width: 32px;
  border-radius: 50%;
}
.carousel-control-prev-icon:hover, .carousel-control-next-icon:hover {
	opacity: 0.85;
}
.carousel-control-prev {
	left: 40%;
	top: 110%;
}
.carousel-control-next {
	right: 40%;
	top: 110%;
}
.midline {
	width: 60px;
	border-top: 2px solid #0e1b5d;
}
.midline2 {
	width: 60px;
	border-top: 2px solid #fff;
}
.carousel-caption h2 {
  margin: 0;
	font-size: 16px;
  font-weight: 800!important;
}
.carousel-caption h2 span {
	color: #0e1b5d;
}
.CarouselCard { 
  margin: 0 30px;
}

.FeaturedCVs .carousel-indicators{
  position: relative;
}
.FeaturedCVs .carousel-indicators li{
  background-color: #0e1b5c;
}
.MobileSearchForm .MobileFix a{
color: #fff;
font-weight: 500!important;
}
.MobileSearchForm .MobileFix a:hover{
  color: #14C59C!important;
  text-decoration: underline;
}

.about{
  padding: 30px 0;
  background: #f2f5f9;
}
.about img{
  margin-bottom: 15px;
}
.CareerAdv{
  padding:30px 0;
  background: #f2f5f9;
}
.CareerAdv article img{
  width: 100%;
  height: auto;
}
.CareerAdv article h3{
  padding: 0;
  font-size: 18px;
  color: #223a6b;
  font-weight: 600!important;
  text-align: center;
  margin: 15px 0 15px 0;
}
.CareerAdv article p{
  color: #333;
  font-size: 14px;
  padding: 0 20px;
}
.CareerAdv a{
  text-decoration: none;
}
.CarouselNav{
  margin-top:15px;
  position: relative;
}
.blog h2 {
  margin: 15px 0px;
  color: #0e1b5c !important;
  font-weight: bolder !important;
}
.blog .btn-text{
  color: #0e1b5c !important;
  font-weight: 600!important;
}
.blog .row .btn-text{
  color: #0e1b5c !important;
  font-size: 40px;
}
.blog a:hover{
  text-decoration: none;
  color:#f2f2f2!important;
}
.blog .row h2 a {
  color: #14C59C !important;
}

.blog .row .card {
  padding: 10px;
  background: #0e1b5c;
  border-radius: 10px;
  border: 1px solid #ccc;
}

.blog .card-body h3 {
  text-align: center;
  margin-top: 15px;
  font-weight: 600 !important;
}

.card-body {
  padding: 10px;
  min-height: 55px;
}

.BoxListing h2 a {
  color: #223a6b !important;
}

.content-elipsis {
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.title-elipsis {
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.height {
  height: 100px;
  color: #ffffff;
}

.BoxListing {
  padding: 8px;
  min-height: 320px;
  border-radius: 10px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
}

.BoxListing img {
  width: 100%;
  height: auto;
  border-radius: 0px;
  margin-bottom: 10px;
}

.BoxListing h2 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 15px;
  color: #223a6b !important;
  font-weight: 600 !important;
}

.BoxListing .btn-primary {
  background-color: #14C59C;
  color: #0e1b5c;
  display: flex;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  margin: 0px auto 0px;
  width: 100px;
  font-weight: 600 !important;
}

.Partners{
  background: #dbe9ff!important;
}
.CTABox .card-header{
  color:#fff!important;
  padding:10px 0px !important;
  background: #0e1b5d!important;
}
.card-title a {
  color:#0e1b5d !important;
  text-decoration: none;
}
.card-body{
  padding:10px;
  min-height: 55px;
}

.listInScroll{
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

.listInScroll::-webkit-scrollbar {
  width: 4px;
}

.listInScroll::-webkit-scrollbar-thumb {
  background-color: #d3cece;
}

.maxhight{
  max-height: 487px !important;
}

.w-100 {
  width: 100%;
}

@media (min-width: 0px) and (max-width: 400px) {
  .NewThemeSearch .card .card-header-morejobs .title{
    font-size: 18px;
    width: 100%;
    text-align: left;
  }
  .NewThemeSearch .card .card-header-morejobs .MoreJobs{
    font-size: 18px;
    display: inline-block;
    text-align: end;
  }
}

@media (min-width: 320px) and (max-width: 575px) {
.carousel-caption {
  position: relative;
}
.card .carousel-caption {
  left: 0;
  top: 0;
  margin-bottom: 15px;
}
.carousel-control-prev {
  left: 35%;
  top: 105%;
}
.carousel-control-next {
  right: 35%;
  top: 105%;
}
.card .carousel-caption h3 {
  margin-top: 0;
  font-size: 16px;
  font-weight: 700;
}
.btnsearchcand {
  margin-top: 0px!important;
}
.registerBtn{
  margin: 20px 0;
}
}
@media (min-width: 576px) and (max-width: 767px) {
  .registerBtn{
    position: relative;
    top:-35px;
  }
  .onlyHeading{
    margin-top: -13px;
  }
.carousel-caption {
  position: relative;
}
.card .carousel-caption {
  left: 0;
  top: 0;
  margin-bottom: 15px;
}
.card .carousel-caption img {
  margin: 0 auto;
}
.card .carousel-caption h3, .card .carousel-caption small {
  text-align: center;
}
.carousel-control-prev {
  left: 35%;
  top: 105%;
}
.carousel-control-next {
  right: 35%;
  top: 105%;
}
}
@media (min-width: 767px) and (max-width: 991px) {
.card .carousel-caption h3 {
  margin-top: 0;
  font-size: 16px;
  font-weight: 700!important;
}
.onlyHeading{
  margin-top: -13px;
}
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .registerBtn{
    position: relative;
    top:-16px;
  }
  .btnHotFix{
    margin-top:27px;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .customSize2{
    max-width: 325px;
  }
  .customSize{
    max-width: 358px;
  }
  .NewTheme .searchHeading{
    margin-top: -12px;
  }
  #accordion8{
    margin: 0 auto;
    width: 285px;
  }
  .R-Mobile{
    margin: 0 auto;
    width: 319px;
  }
  .SliderBox1{
    height:100%;
  }
  .btnMobile{
    margin-top: -11px;
  }
  .SliderWrapper {
    padding: 0px!important;
  }
  .accordion .customDesign {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .accordion .customDesign2 {
   border-radius: 0;
  }
  .accordion .customDesign3 {
    border-top-left-radius:  0;
    border-top-right-radius: 0;
  }
  .accordion .cardDesign {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .accordion .cardDesign2 {
   border-radius: 0;
  }
  .accordion .cardDesign3 {
    border-top-left-radius:  0;
    border-top-right-radius: 0;
  }
  .NewThemeSearch h2 {
    margin: 0px 0px 20px 0px!important;
  }
  .MadeEasy {
    margin-top: 30px!important;
  }
  .NewTheme{
    padding:20px 10px;
  }
  .NewTheme .heading{
    font-size: 45px;
  }
  .NewTheme h2{
    font-size: 56px;
    font-weight: 780!important;
    margin: 0 !important;
  }
  .NewThemeSearch .card .card-header-morejobs .title{
    font-size: 18px;
  }
  .NewTheme .btn {
    margin-bottom: 30px;
  }
  .NewTheme .card a {
    color: #fff;
    font-size: 15px !important;
    margin-left: 5px;
  }
  .NewThemeSearch .FeaturedCVs{
    margin:0px!important;
  }
  .NewThemeSearch .LatestCVs{
    margin:0 0px!important;
  }
  .NewTheme .card .btn{
    margin-top: 0!important;
    background: #14c59c;
    color: #0e1b5c;
    margin-bottom: 0px;
  }
  .NewTheme span.Two{
    color:#0e1b5c;
    font-size: 125px;
    position: absolute;
    top: -36px;
    left: 220px;
    transform: rotate(5deg);
  }

 .NewTheme h2.CallUsHeading{
   margin-top: 7px;
 }
 .NewTheme h2.CallUs a{
  color:#0e1b5c;
  font-size: 48px;
  font-weight: 700!important;
}
  .NewTheme .slide2 {
    left: 245px!important;
  }
  .MobileSearchForm {
    padding: 0px 15px!important;
  }
  .TrustedBy{
    font-size: 18px;
    margin-top: 0px!important;
  }
  .NewTheme span{
    font-weight: 800!important;
    font-size: 42px;
    color:#000000;
  }
  .NewTheme h3.NewJobs{
    color:#000000;
    font-size: 27px;
  }
  .NewTheme h3.NewJobs2{
    color:#0e1b5c;
    font-size: 30px;
    font-weight: 700!important;
    margin-top: 12px;
  }
  .NewTheme .card label {
    font-size: 15px;
    font-weight: 700 !important;
    color: #fff;
    margin-top: 0px;
    margin-bottom: 5px!important;
  }
  .NewTheme .carousel-indicators{
    bottom: -20px;
  }
  .NewTheme .carousel-item h2 img{
    width: 90%;
    height: auto;
    margin-bottom: 25px;
  }
  .NewTheme img{
    width: 90%;
    height: auto;
    padding-top:15px;
  }
  .BannerTxt1 img{
    width: 100%;
    height: auto;
  }
  .BannerTxt2 img{
    width: 100%;
    height: auto;
  }
  .NewThemeSearch{
    padding:40px 10px;
  }
  .NewThemeSearch .searchmore{
    padding-top: 40px;
  }
  .MobileSearchForm {
    padding: 10px 0px!important;
  }
  .MobileSearchForm .MobileFix {
   margin-bottom: 0px!important;
  }
  .MobileSearchForm .MobileFix a {
    margin-bottom: 0px!important;
    color: #fff!important;
  }
  .NewThemeSearch input{
    margin: 10px 0;
  }
  .NewThemeSearch .btn{
    display: block;
    margin-top: 10px;
  }
  .NewThemeSearch h3{
    margin-top: 50px;
  }
  .Registration{
    padding:40px 10px 0px 10px;
  }
  .HelpAdvice{
    padding:20px 10px!important;
    margin-top: 40px;
  }
  .blog {
    margin-top: 40px;
  }
  .Testi{
    padding:50px 50px;
  }
  .Registration h1{
    margin: 50px 0 15px 0;
  }
  .searchBg {
    padding: 15px 0;
  }
  .searchBg input {
    margin-bottom: 8px;
  }
  .searchBg button {
    margin-bottom: 8px;
  }
  .searchBg h1 {
    font-size: 18px;
    margin: 0px 0 20px 20px;
  }
  .searchBg .P-Web {
    text-align: center;
  }  
  .JobsList ul li {
    width: 100%;
  }
  .JobSearch h2 {
    padding: 0 20px;
  }
  .trustedPartner .card-header {
    margin-bottom: 10px;
  }
  ul.nav-pills {
    margin-top: 15px;
    width: 100%;
  }
  li.nav-item {
    width: 100%;
  }
  .nav-pills .nav-link.active,
  .nav-pills .show > .nav-link {
    border-radius: 5px;
  }
  section.jobs {
    overflow: hidden;
    background: #ffffff;
    padding: 30px 0px !important;
  }
  .jobs h1, h2 {
    flex: auto;
    padding: 0;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    text-align: center;
    margin: 15px 0 15px 0;
  }
  .jobs h2 {
    flex: auto;
    padding: 0;
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
    text-align: center;
    margin: 15px 0;
  }
  .trustedPartner .card-header {
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  .JobSearch {
    width: 100%;
    padding: 30px 0 30px 0;
    background: #f2f5f9;
  }
  .Trustpilot {
    padding:25px 0px!important;
  }
  .JobSearch h2 {
    padding: 0;
    width: 100%;
    font-size: 16px;
    color: #223a6b;
    font-weight: 600!important;
    margin: 0 20px 20px 20px;
    text-align: center;
  }
  .well button {
    border: none;
    color: #fff;
    font-size: 14px;
    padding: 6px 15px;
    border-radius: 20px;
    background: #3465b7;
  }
  .well button:hover {
    border: none;
    color: #fff;
    font-size: 14px;
    padding: 6px 15px;
    border-radius: 20px;
    background: #1e3f76;
  }
  .tab-content .btn-primary {
    display: block;
    margin-bottom: 10px;
  }
  .searchBg select {
    margin-bottom: 8px;
  }
  .container {
    max-width: 550px!important;
    margin-bottom: 18px;
  }
  :host ::ng-deep app-browse-by-industry .IndustriesListing ul li  {
    margin: 0px 20px!important;
   }
   :host ::ng-deep app-browse-by-industry .IndustriesListing ul li  a{
    line-height: 1.0!important;
   }
   :host ::ng-deep app-browse-by-industry .IndustriesListing ul li a.heading {
    margin: 0px!important;
   }
   :host ::ng-deep app-browse-by-location-item .JobsListing ul li {
    margin-bottom: 0px!important;
   }
   :host ::ng-deep app-browse-by-location-item .JobsListing ul li a{
    line-height: 1.0!important;
   }
}
/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .registerBtn{
    margin: 20px 0;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .btnHotFix{
    margin-top:49px;
  }
  .SliderBox1{
    height:100%;
  }
  .Heightfix{
    height: 280px;
  }
  .NewTheme{
    padding:20px 10px;
  }
  .NewTheme .heading{
    font-size: 48px;
  }
  .NewTheme h2{
    font-size: 70px;
    font-weight: 700!important;
  }
  .SliderBox1 h2 img {
    width: 50%;
    height: auto;
  }
  .NewTheme span.Search4u{
    font-size:69px;
    color:#000000;
    position: relative;
    top: -18px;
  }
  .NewTheme span.Two{
    color:#0e1b5c;
    font-size: 140px;
    position: absolute;
    top: -45px;
    left: 240px;
    transform: rotate(5deg);
  }
  .NewTheme .card a {
    color: #fff;
    font-size: 15px !important;
    text-decoration: none;
    margin-left: 5px;
  }
  .NewTheme h3.NewJobs{
    color:#000000;
    font-size: 41px;
    top: -23px;
    position: relative;
    font-weight: 500!important;
  }
  .NewTheme h2.CallUs a{
    color:#0e1b5c;
    font-size: 60px;
    font-weight: 700!important;
  }
  .NewTheme h3.NewJobs2{
    color:#0e1b5c;
    font-size: 29px;
    top: 12px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme .btn{
    margin-top: 0!important;
    margin-bottom: 30px;
  }
  .MobileSearchForm {
    padding: 0px 15px!important;
  }
  .NewTheme .card label{
    font-size: 15px;
    font-weight: 700 !important;
    color: #fff!important;
    margin-top: 0px;
    margin-bottom: 5px!important;
  }
  .NewTheme .card .btn{
    margin-top: 0!important;
    background: #14c59c;
    color: #0e1b5c;
    margin-bottom: 0px;
  }
  .Trustpilot {
    padding:50px 0px!important;
  }
  .NewTheme .carousel-indicators{
    bottom: -19px;
  }
  .NewThemeSearch{
    padding:50px 20px;
  }
  .NewThemeSearch input{
    margin-bottom: 10px;
  }
  .NewThemeSearch h3{
    margin-top: 50px;
  }
  .Registration{
    padding:50px 20px;
  }
  .Registration h1{
    margin: 50px 0 15px 0;
  }
  .HelpAdvice{
    padding:50px 20px!important;
  }

  .MobileSearchForm {
    padding: 0px!important;
  }
  .MobileSearchForm .MobileFix {
   margin-bottom: 0px!important;
  }
  .trustedPartner .card-header {
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
  }
  .JobSearch {
    width: 100%;
    padding: 30px 0 30px 0;
    background: #f2f5f9;
  }
  .JobSearch h2 {
    padding: 0;
    width: 100%;
    font-size: 16px;
    color: #223a6b;
    font-weight: 600!important;
    margin: 0 20px 20px 20px;
    text-align: center;
  }
  .well button {
    font-size: 13px;
    color: #858585;
    background: #dbe9ff;
    border: none;
  }
  .well button:hover {
    font-size: 13px;
    color: #103169;
    background: #dbe9ff;
    border: none !important;
  }
  .tab-content .btn-primary {
    display: block;
    margin-bottom: 10px;
  }
  .searchBg select {
    margin-bottom: 8px;
  } 
  .container {
    max-width: 600px!important;
  } 
}
/* Medium devices (landscape tablets, 768px and down) */
@media only screen and (max-width: 768px) {
  .TrustedBy {
    margin-top: 20px;
  }
  .registerBtn{
    margin: 20px 0;
  }
  .onlyHeading{
    margin-top: -13px;
  }
}
/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .registerBtn{
    margin: 20px 0;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .TrustedBy{
    margin-top: 220px;
    margin-left: 80px;
  }
  /* .btnfix{ */
    /* margin-top: 220px; */
    /* margin-left: 32px; */
  /* } */
  .btnsearchcan{
    margin-left: 0px!important;
  }
  .NewTheme img {
    width: 32%;
    height: auto;
    margin-bottom: 10px;
  }
.MobileSearchForm{
 padding: 0px 15px!important;
}
.NewTheme .card .btn-sm {
  position: relative;
  top:10px;
}
.MobileSearchForm .MobileFix a {
  color:#fff;
}
.MobileSearchForm {
  padding: 10px 15px!important;
}
.NewTheme .card .btn-sm {
  position: relative;
  top:28px;
  font-size: 21px;
}
}
/* Large devices (laptops/desktops, 992px and down) */
@media only screen and (max-width: 992px) {
  .banner {    
    left: -200px;
  }
  .registerBtn{
    margin-top: 48px;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .VideoCard p{
    font-size: 15px;
    text-align: center;
    color:#ffff;
    min-height: 71px;
    text-decoration: none;
  }
}
/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .JobsList ul li {
    width: 25% !important;
  } 
  .container {
    max-width: 1000px!important;
    display: contents;
  }
   .registerBtn{
    margin: 40px 0;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .NewTheme span.Two {
    margin-left: 17px;
  }
}
/* Extra large devices (large laptops and desktops, 1200px and up) */
@media (min-width: 1200px) {
  .registerBtn{
    margin: 20px 0;
  }
  .customSize{
    max-width: 1171px;
  }
  .customSize2{
    max-width: 1112px;
  }
  .customSize3{
    max-width: 1174px;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .NewTheme {
    padding: 0px 0px 50px 0px;
  }
  .NewTheme .heading {
  font-size: 60px;
}
.NewTheme h2{
  font-size: 70px;
}
  .SliderBox1{
    height:338px;
    /* background:#000; */
  }
  .Heightfix{
    height:280px;
  }
  .padfix {
    margin-top: -15px;
  }
  .SliderBox1 img {
    width: 60%;
    height: auto;
  }
  .SliderBox1 .BannerTxt1 img{
    width: 100%;
    padding: 20px 0!important;
  }
  .SliderBox1 .BannerTxt2 img{
    width: 100%;
    padding: 20px 0!important;
  }
  .NewTheme .card {
    margin: 0 0px;
    border-radius: 20px;
  }
  .NewTheme .card label {
    font-size: 20px;
    font-weight: 700 !important;
    color: #fff;
  }
  .NewTheme .card .btn-sm {
    background-color: #0e1b5d;
    color: fff !important;
    margin-top: 33px !important;
    padding: 5px 15px;
    top:0px;
    font-size: 20px !important;
  }
  .NewTheme .card a {
    color: #0e1b5d;
    font-size: 15px !important;
    text-decoration: underline;
    margin-left: 5px;
  }
  .Registration h1{
    margin-top: 0;
  }
  .Trustpilot {
    padding:50px 100px!important;
  }
  .HelpAdvice{
    padding:40px 100px !important;
  }
  .blog{
    padding:40px 100px !important;
  }
  .JobSearch {
    width: 100%;
    padding: 30px 0 30px 0;
    background: #f2f5f9;
  }
  .JobSearch h2 {
    padding: 0;
    width: 100%;
    font-size: 16px;
    color: #223a6b;
    font-weight: 600!important;
    margin: 0 0 10px 0;
    text-align: center;
  }
  .trustedPartner .card-header {
    border: none;
    border-radius: 8px;
    border: 1px solid #e7e7e7;
    height: 120px;
  }
  .trustedPartner .btn-primary {
    width: 160px;
    margin-top: 20px;
  }
  /* .searchmore{
    margin: 0px 0px!important;
  } */
  .well button {
    font-size: 13px;
    color: #5f5a5a;
    background: #dbe9ff;
  }
  .well button:hover {
    font-size: 13px;
    color: #306fd9;
    background: #dbe9ff;
    border: none !important;
  }
  .btn-primary:not(:disabled):not(.disabled).active,
  .btn-primary:not(:disabled):not(.disabled):active,
  .show > .btn-primary.dropdown-toggle {
    color: #3465b7;
    background-color: #dbe9ff;
  }
  .btn-primary.focus,
  .btn-primary:focus {
    box-shadow: none !important;
  }
  .JobSearch button {
    width: 160px;
  }
  .jobs button {
    width: 160px;
  }
  .P-Web {
    margin-left: 15px !important;
  } 
  .HideDesktop {
    display: none !important;
  }
  .HideMobile {
    display: block;
  } 
  .container {
    max-width: 1140px!important;
    display: block;
  }
  .NewTheme .card label {
    font-size: 20px;
    font-weight: 700 !important;
    color: #fff;
  }
  .NewTheme .card .btn-sm {
    background-color: #14C59C;
    color: #0e1b5c;
    margin-top: 33px !important;
    padding: 5px 15px;
    top:0px;
    font-size: 20px !important;
  }
  .NewTheme .card a {
    color: #fff;
    font-size: 15px !important;
    text-decoration: none;
    margin-left: 5px;
  }
  .NewTheme .carousel-indicators{
    bottom: -16px;
  }
  .MobileSearchForm {
    padding: 10px 15px!important;
  }
  .Search4u{
    top: -30px;
    color: #0e1b5c;
    font-size: 84px;
    position: relative;
    font-weight: 700!important;
  }
  .btnfix{
    position: relative;
    top:-35px;
  }
  .btnHotFix{
    position: relative;
    top:-6px;
  }
  .btnMobile{
    margin-top: 18px;
  }
  .latest-img{
    border: 4px solid #0e1b5c;
    border-radius: 100%;
  }
}
 /* Extra extra large devices (extra large desktops, 1400px and up)  */
@media (min-width: 1400px) {
  .registerBtn{
    margin: 20px 0;
  }
  .onlyHeading{
    margin-top: -13px;
  }
  .NewTheme .heading {
    font-size: 60px;
  }
  .NewTheme span.Two{
    color:#0e1b5c;
    font-size: 150px;
    position: absolute;
    top: -42px;
    left: 251px;
    transform: rotate(5deg);
  }
  .NewTheme h2{
    font-size: 72px;
  }
  .NewTheme h3{
    top: -20px;
    color:#fff;
    font-size: 36px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme h3.NewJobs{
    color:#000000;
    font-size: 36px;
    top: -19px;
    position: relative;
    font-weight: 600!important;
    margin-bottom: 30px;
  }
  .NewThemeSearch{
    padding:70px 100px;
  }
  .HelpAdvice{
    padding:70px 100px;
  }
  .Registration{
    padding:70px 100px;
  }
  .TrustedBy{
    margin-top: 170px;
    float: right;
    margin-right: 220px;
  }
  .NewThemeSearch h3{
    margin-top: 0;
  }
  .R-desktop{
    text-align: right;
  }
  .searchBg {
    padding: 0px 75px;
  }
  .SliderBox1{
    height:338px;
    /* background:#000; */
  }
}

