<div class="accordion mt-3">
  <div *ngFor="let package of PackageDetails;let i = index" class="card" [ngClass]="{'Subscription': !IsAccountSettingPage}">
    <div class="collapsed" [ngClass]="{'card-header': IsAccountSettingPage, 'Header': !IsAccountSettingPage}" data-toggle="collapse" [attr.href]="'#Subscription' + package.PackageId+i">
      <h2>
        <div class="row m-0" [ngClass]="{'mt-2': IsAccountSettingPage}">
          <div class="col-10 p-0">
            <i class="fas fa-star"></i>&nbsp;
            <span class="pr-1">
              <span class="font-weight-bolder" [ngClass]="{'yourSubscription': package?.PackageName == 'Free Forever!'|| package?.PackageName == 'Free','title': IsAccountSettingPage}">{{IsAccountSettingPage ? package?.PackageName : 'Your'}} Subscription <span  *ngIf="package?.DateFrom !=null && package?.PackageName != 'Free Forever!' && package?.PackageName !='Free'">(From: {{package?.DateFrom | date}} To: {{package.DateTo | date}})</span></span>
            </span>
          </div>
          <div class="col-2 p-0">
            <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
          </div>
        </div>
      </h2>
    </div>
    <div [id]="'Subscription' + package.PackageId+i" class="collapse">
      <div class="card-body Subscript p-0">
        <ul>
          <div *ngFor="let category of package?.AddOnCategories">
            <li class="bg-blue text-white" *ngIf="category?.AddOns && category?.AddOns.length > 0">
              <i class="fas fa-star"></i> {{ category.Description }}
            </li>
            <div *ngFor="let addOn of category.AddOns">
              <li *ngIf="!addOn.IsAdditional && addOn.IsAddOnApplicable">
                <div class="row m-0">
                  <div class="col-md-12 p-0">
                    <i [ngClass]="{'fas fa-check': addOn.IsAddOnApplicable, 'fa fa-times': !addOn.IsAddOnApplicable}"></i>                    
                    <span class="adOn-text" [attr.data-tooltip]="addOn.Description">{{ addOn.Limit | packageLimit }} {{ addOn.Title }}</span>
                    <span *ngIf="addOn.Limit != 0"
                          class="float-right font-weight-bold text-black" title="{{addOn.Consumed}} consumed out of {{addOn.Limit}}">{{addOn.Consumed}}/{{addOn.Limit}}</span>
                  </div>
                </div>
              </li>
              <li *ngIf="addOn?.IsAdditional && addOn?.IsAddOnApplicable">
                <i [ngClass]="{'fas fa-check': addOn.IsAddOnApplicable, 'fa fa-times': !addOn.IsAddOnApplicable}"></i>
                <span>{{ addOn.Limit | packageLimit }} {{ addOn.Title }}</span>
                <span *ngIf="addOn.Limit != 0" class="float-right font-weight-bold text-black">{{ addOn.Consumed }}/{{ addOn.Limit }}</span>
              </li>
            </div>
          </div>
          <div class="bg-blue text-white AddOns"*ngIf="UserFeatures?.length > 0"> <i class="fas fa-star star-icon"></i>Add-Ons</div>
           <div *ngIf="UserFeatures?.length > 0">
           <div *ngFor="let feature of UserFeatures">
              <ul>
                <li>
                  <div class="feature-wrapper">
                    <span class="adOn-text" [attr.data-tooltip]="feature?.Description">
                      <span class="feature-title">{{ feature.Limit | packageLimit }} {{ feature?.Title }}</span>
                    </span>
                    <span *ngIf="feature.Limit != 0" class="float-right font-weight-bold text-black">{{ feature.Consumed }}/{{ feature.Limit }}</span>
                  </div>
                </li>
              </ul>
            </div>
           </div>          
         <div class="text-center">
            <button *ngIf="package?.SubscriptionId != null" class="btn btn-danger btn-sm mb-2" [class.btn-block]="!IsAccountSettingPage" (click)="cancelSubscription(package)"
              [disabled]="package.IsSubscriptionActive !== undefined && package.IsSubscriptionActive === false && 
              package?.SubscriptionStatus !== undefined && package.SubscriptionStatus === 'Inactive' || isSubscriptionCancelled ">
              <i class="fas fa-ban"></i>
              {{ (package.IsSubscriptionActive !== undefined && package.IsSubscriptionActive === false) && 
                (package?.SubscriptionStatus !== undefined && package.SubscriptionStatus === 'Inactive') || isSubscriptionCancelled ? 
                'Cancelled subscription' : 
                'Cancel Subscription' }}</button>
          </div>
        </ul>
      </div>
    </div>
  </div>
</div>
