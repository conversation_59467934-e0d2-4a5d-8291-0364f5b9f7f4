<!--Search Section-->
<section class="NewTheme"> 
  <div class="container">
    <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 SliderWrapper">
      <div id="carouselExampleIndicators" class="carousel slide">
        <div class="carousel-inner">
          <div class="carousel-item active">
            <div class="row">
              <div class="col-xl-7 col-lg-7 col-md-12 col-sm-12 SliderBox1"
                [ngClass]="ContextService.IsUserLoggedIn ? 'Heightfix' : ''">
                <h2>Hire Faster</h2>
                <span class="Search4u">with Apply4U.</span>                
                <h3 class="NewJobs">Your helpful job site</h3>
                <!-- <p [ngClass]="!ContextService.IsUserLoggedIn ? 'btnfix registerBtn' : ''">
                  <a class="btn btn-primary btnsearchcand" *ngIf="!ContextService.IsUserLoggedIn"
                    [href]="Navigations.RecruiterJoinNow">Register</a>
                </p> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="container">
    <div class="row card" [ngClass]="ContextService.IsUserLoggedIn ? 'mt-4' : ''">
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
        <div class="row d-flex justify-content-center">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
            <div class="row m-0">
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group mb-0">
                  <label>Describe your ideal hire</label>
                  <app-keyword-autocomplete class="formMain" class="form-control-sm p-0"
                    [SearchKeywordTitle]="SearchParams.Keywords" (SearchKeywordTitleChanged)="SearchParams.Keywords=$event"
                    (IsEnterKeyPressed)="SearchEnterKeyPressed($event)">
                  </app-keyword-autocomplete>
                </div>
              </div>
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group mb-0">
                  <label>Where</label>
                  <app-smart-location-autocomplete [SearchKeywordLocation]="SearchParams.Location" class="form-control-sm p-0"
                    (SearchKeywordLocationChanged)="OnLocationChanged($event)" [ReadCurrentLocation]="ReadCurrentLocation">
                  </app-smart-location-autocomplete>
                </div>
              </div>
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group MobileFix">
                  <button class="btn btn-primary btn-sm" (click)="SearchButtonClickHanler()">Search CVs</button>
                </div>
              </div>
              <div class="col-md-8 MobileSearchForm">
                <div class="form-group MobileFix padfix">
                  <a (click)="AdvanceSearchClickHandler()" class="text-white" href="JavaScript:Void(0);" title="Advance Search">Advanced Search <i class="fas fa-chevron-right"></i></a>
                </div>
              </div>
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group MobileFix padfix">
                  <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{Navigations.BrowseCVs}}" class="MobileSPC text-white" title="Browse CVs">Browse CVs <i class="fas fa-chevron-right"></i></a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!--Search Section-->

<!--3 in one -->
<section class="NewThemeSearch">
  <div class="container">
  
    <!--Latest CVs-->
    <div class="row card LatestCVs" #LatestCVsId>
      <div class="card-header-morejobs">
        <h1 class="title">Latest CVs</h1>
        <a class="MoreJobs" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{Navigations.CvSearch}}" title="View more CVs" (click)="OnClickRecruiterHomeNavigation('Latest CVs')">View More CVs</a>
      </div>
        <div class="row m-0" *ngIf="ContextService.IsDesktopView">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">              
              <div class="carousel-inner mt-5 mb-3">
                    <div class="row m-0">
                  <div class="col" *ngFor="let Candidate of RecentCVs; let i = index;" >
                    <img width="100" height="100" *ngIf="Candidate && Candidate.ProfileImagePath && Candidate.ProfileImagePath!=''"
                  src="{{Candidate.ProfileImagePath}}" loading="lazy"  alt="User Profile Pic" class="media-object img-circle img-h-w latest-img">
                    <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Candidate?.JobTitle | cvSearchDetailUrl:Candidate?.FormattedAddress:Candidate?.Id" #cvDetail (click)="OnClickRecruiterHomeNavigation('Latest CVs')">
                      <h4 innerHtml="{{ Candidate?.JobTitle ? (Candidate?.JobTitle) : 'Candidate [' +Candidate?.Id + ']' }}"
                        title="{{Candidate?.JobTitle? Candidate?.JobTitle : Candidate?.Id}}"></h4>
                    </a>
                  </div>
                  </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row m-0" *ngIf="ContextService.IsMobileView">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center carousel slide" id="LatestCVscarousel" data-ride="carousel">          
              <div class="carousel-inner mt-5 mb-3">
                    <div class="row m-0">
                  <div class="col-12 carousel-item" *ngFor="let Candidate of RecentCVs; let i = index; first as isFirst" [ngClass]="{active:isFirst}" >
                  <div>
                    <img width="100" height="100" *ngIf="Candidate && Candidate.ProfileImagePath && Candidate.ProfileImagePath!=''"
                  src="{{Candidate.ProfileImagePath}}" loading="lazy" alt="User Profile Pic" class="media-object img-circle img-h-w ">
                    <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Candidate?.JobTitle | cvSearchDetailUrl:Candidate?.FormattedAddress:Candidate?.Id" #cvDetail (click)="OnClickRecruiterHomeNavigation('Latest CVs')">
                      <h4 innerHtml="{{ Candidate?.JobTitle ? (Candidate?.JobTitle) : 'Candidate [' +Candidate?.Id + ']' }}"
                        title="{{Candidate?.JobTitle? Candidate?.JobTitle : Candidate?.Id}}"></h4>
                    </a>
                  </div>
                  </div>
                    </div>
              </div>
          
            <ol class="carousel-indicators">
              <li data-target="#LatestCVscarousel" data-slide-to="0" class="active"></li>
              <li data-target="#LatestCVscarousel" data-slide-to="1"></li>
              <li data-target="#LatestCVscarousel" data-slide-to="2"></li>
              <li data-target="#LatestCVscarousel" data-slide-to="3"></li>
              <li data-target="#LatestCVscarousel" data-slide-to="4"></li>
            </ol>
          </div>
        </div>
      </div>
    </div>
    <!--Latest CVs-->
    

    <!--More CV Search Options-->
    <div class="row searchmore" #MoreCVsSearchOptionId>
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
        <h2><span>More</span> CV Search Options</h2>
      </div>
      <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12">
        <div id="accordion">
          <div class="accordion" id="PerInfo">
            <div class="card cardDesign" [ngClass]="ContextService.IsMobileView ? 'mb-0': ''">
              <div class="card-header" data-toggle="collapse" (click)="toggleCvSearchAccordion()">
                <span class="title">Popular CV Searches</span>
                <span *ngIf="ContextService.IsMobileView" class="accicon">
                  <i class="fas" [ngClass]="{'fa-angle-down': !IsCvSearchAccordionExpanded, 'fa-angle-up': IsCvSearchAccordionExpanded}"></i>
                </span>
              </div>
              <div class="panel-collapse collapse show" aria-labelledby="headingThree" [class.show]="IsCvSearchAccordionExpanded">
                <div class="panel-body p-0 listInScroll" *ngIf="ContextService.IsDesktopView || IsCvSearchAccordionExpanded">
                  <ul>
                    <li *ngFor="let popular of IsShowMorePopularSearch && (IsShowMorePopularSearchClicked || ContextService.IsServer) ? TopPopularSearchesAll : TopPopularSearches">
                      <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="popular.CVSearchUrl" title="{{ popular.SearchName }} CVs" (click)="OnClickRecruiterHomeNavigation('Search Option')">
                        {{ popular.SearchName }} CVs
                      </a>
                    </li>
                  </ul>
                  <div class="pb-2">
                    <a href="JavaScript:void(0);" class="btnshowmore" (click)="PopularSearchesExpandHandler()">
                      {{ IsShowMorePopularSearch ? "See less" : "See more" }} CVs
                      <i [ngClass]="IsShowMorePopularSearch ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
     <!-- CVs By Location Section -->
     <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12">
      <div class="accordion" id="PerInfo">
        <div class="card cardDesign2" [ngClass]="ContextService.IsMobileView ? 'mb-0': ''">
          <div [ngClass]="ContextService.IsMobileView ? 'card-header collapsed' : 'card-header show in'"
            data-toggle="collapse"
            (click)="toggleLocationAccordion()">
            <span class="title">CVs By Location</span>
            <span *ngIf="ContextService.IsMobileView" class="accicon"><i class="fas" [ngClass]="{'fa-angle-down': !IsLocationAccordionExpanded, 'fa-angle-up': IsLocationAccordionExpanded}"></i></span>
          </div>
          <div [ngClass]="ContextService.IsMobileView ? 'panel-collapse collapse' : 'panel-collapse show'"
            aria-labelledby="headingTwo" [class.show]="IsLocationAccordionExpanded">
            <div class="panel-body jobs listInScroll">
              <app-browse-by-location-item [isUnderLine]="true" *ngIf="ContextService.IsDesktopView || IsLocationAccordionExpanded"
                [LocationsByRegion]="IsShowMoreRegions && (IsShowMoreRegionsClicked || ContextService.IsServer) ? AllRegion : LocationRegion"
                [IsJob]="ContextService.IsJobSeeker" [IsHomePageDisplay]="true">
              </app-browse-by-location-item>
              <div>
                <a class="btnshowmore" href="JavaScript:Void(0);" (click)="LocationExpandHandler()">
                  {{ IsShowMoreRegions ? "See less" : "See more" }} Locations
                  <i *ngIf="!IsShowMoreRegions" [ngClass]="{ 'fas fa-chevron-down': !IsShowMoreRegions }"></i>
                  <i *ngIf="IsShowMoreRegions" [ngClass]="{ 'fas fa-chevron-up': IsShowMoreRegions }"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    

<div class="col-xl-4 col-lg-4 col-md-12 col-sm-12">
  <div class="accordion" id="PerInfo">
      <div class="card cardDesign3">
          <div 
              [ngClass]="ContextService.IsMobileView ? 'card-header collapsed' : 'card-header show in'"
              (click)="toggleIndustryAccordion()">
              <span class="title">CVs By Industry</span>
              <span *ngIf="ContextService.IsMobileView" class="accicon">
                  <i class="fas" [ngClass]="{'fa-angle-down': !IsIndustryAccordionExpanded, 'fa-angle-up': IsIndustryAccordionExpanded}"></i>
              </span>
          </div>
          <div 
              [ngClass]="ContextService.IsMobileView ? 'panel-collapse collapse' : 'panel-collapse collapse show'"
              [class.show]="IsIndustryAccordionExpanded">
              <div class="panel-body jobs listInScroll">
                  <app-browse-by-industry *ngIf="ContextService.IsDesktopView || IsIndustryAccordionExpanded"
                      [IsHomePageDisplay]="true" 
                      [CssClass]="'HomeListing'"
                      [Industries]="IsShowMoreIndustry && (IsShowMoreIndustryClicked || ContextService.IsServer) ? AllSectors : Sectors">
                  </app-browse-by-industry>
                  <div class="mt-2">
                      <a class="btnshowmore" (click)="IndustryExpandHandler()" href="JavaScript:Void(0);">
                          {{ IsShowMoreIndustry ? "See less" : "See more" }} Industries
                          <i *ngIf="!IsShowMoreIndustry" [ngClass]="{ 'fas fa-chevron-down': !IsShowMoreIndustry }"></i>
                          <i *ngIf="IsShowMoreIndustry" [ngClass]="{ 'fas fa-chevron-up': IsShowMoreIndustry }"></i>
                      </a>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>




    </div>
    <!--More CV Search Options-->


    <!--Screened Candidates-->
    <div class="row card mt-3 FeaturedCVs" #HomePageScreenedCandidateId>
      <div class="card-header-morejobs"
      [ngClass]="ContextService.IsMobileView ? 'mobile-view' : 'NewThemeSearch .card .card-header-morejobs a'">
        <span class="title">Screened Candidates</span>
        <a class="MoreJobs" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
          routerLink="{{Navigations.CvSearch}}" [queryParams]="{profiletype: '2'}" title="View more Candidates"
          (click)="OnClickRecruiterHomeNavigation('Screened Candidates')"> View more CVs</a>
      </div>
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12" *ngIf="ContextService.IsDesktopView">
        <smart-cv-card [Candidates]="Candidates" [IsHomePageScreenedCandidate]="true" [CompanyVerificationId]="CompanyVerificationStatusId"></smart-cv-card>
      </div>
    
      <div class="row m-0 w-100" *ngIf="ContextService.IsMobileView">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center carousel slide" id="ScreenedCVscarouselId"
          data-ride="carousel">
          <div class="carousel-inner">
            <div class="row m-0">
              <div class="col-12 carousel-item" *ngFor="let candidate of Candidates; let i = index; first as isFirst"
                [ngClass]="{active:isFirst}">
                <div>
                  <app-smart-cv-card-carousel [Candidate]="candidate"
                    [IsHomePageScreenedCandidate]="true"></app-smart-cv-card-carousel>
                </div>
              </div>
            </div>
          </div>
      
          <ol class="carousel-indicators">
            <li data-target="#ScreenedCVscarouselId" data-slide-to="0" class="active"></li>
            <li data-target="#ScreenedCVscarouselId" data-slide-to="1"></li>
            <li data-target="#ScreenedCVscarouselId" data-slide-to="2"></li>
          </ol>
        </div>
      </div>
    </div>
    <!--Screened Candidates-->

  </div>
</section>
<!--3 in one -->

<!--Hire in 4 Easy Steps-->
<section class="Registration">
  <div class="container">
    <div class="row">
      <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 R-desktop R-Mobile text-center">
        <h2><span>Hire</span> in 4 Easy Steps</h2>
        <p class="mt-4 mb-0"><strong>Step 1</strong></p> <p><span> Join now</span> and post your company details & jobs</p>
        <p class="mb-0"><strong>Step 2</strong></p> <p> Benefit from recommended<span> recommended CVs, pre-screened</span> candidates & access to 4M+ CVs</p>
        <p class="mb-0"><strong>Step 3</strong></p><p> Tell our <span>experts</span> what you're looking for so we can Search4U for 4 CVs a day!</p>
        <p class="mb-0"><strong>Step 4</strong></p><p> Start <span>hiring</span> better quality candidates faster with Apply4U</p>
        <a class="btn btn-primary" *ngIf="!ContextService.IsUserLoggedIn" [href]="Navigations.RecruiterJoinNow" (click)="ContextService.OnRegisterClick()">Start Hiring Now</a>
      </div>
      <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 MadeEasy">
        <div class="row d-flex justify-content-center">
          <div class="col-xl-7 col-lg-10 col-md-12 col-sm-12">
            <h2>Recruitment <span>Made Easy</span></h2>
            <div id="accordion8" #HomePageMadeEasyPostJobId>
              <div class="accordion" id="accordionExample">
                <div class="card customDesign">
                  <div class="card-header" data-toggle="collapse" data-target="#collapseOne" aria-expanded="true">
                    <span class="title">Unlimited Free Job Ads </span>
                    <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                  </div>
                  <div id="collapseOne" class="panel-collapse collapse show" data-parent="#accordionExample">
                    <div class="panel-body">
                      <p>Post branded job adverts directly or through any leading multi-poster. Advertise your jobs in front of
                        relevant, verified & pre-screened candidates today. Connect directly without limits!</p>
                      <a class="btn btn-primary" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                        [routerLink]="Navigations.JobPost" (click)="OnClickRecruiterHomeNavigation('Post Jobs')">Post Jobs</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion" id="PerInfo">
                <div class="card customDesign2">
                  <div class="card-header collapsed" data-toggle="collapse" data-target="#collapseTwo" aria-expanded="false">
                    <span class="title">Search CVs Instantly </span>
                    <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                  </div>
                  <div id="collapseTwo" class="panel-collapse collapse" data-parent="#accordionExample">
                    <div class="panel-body">
                      <p>Access millions of CVs across all industries. Set up auto search alerts to be notified of new CVs and try
                        our disruptive HumaTec®nology to find relevant CVs from across platforms.</p>
                      <a class="btn btn-primary" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                        [routerLink]="Navigations.CvSearch" (click)="OnClickRecruiterHomeNavigation('Post Jobs')">Search CVs</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion" id="PerInfo">
                <div class="card customDesign3">
                  <div class="card-header collapsed" data-toggle="collapse" data-target="#collapseThree" aria-expanded="false">
                    <span class="title">Expert Help & Recommendations </span>
                    <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                  </div>
                  <div id="collapseThree" class="panel-collapse collapse" data-parent="#accordionExample">
                    <div class="panel-body">
                      <p>Our experts are on hand to help throughout your search, setting up our technology to ensure you receive
                        the very best recommendations and speak to candidates for you.</p>
                      <a class="btn btn-primary" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                        routerLink="{{ Navigations.ContactUs }}" (click)="OnClickRecruiterHomeNavigation('Post Jobs')">Speak to an
                        Expert</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
    </div>
  </div>
</section>
<!--Registration-->


<!--Client Testimonials-->
<section class="Registration">
  <div class="container-xl customSize">
    <h2 class="text-center UserStory"><b>Client Testimonials</b></h2>
    <hr class="midline" />
    <div class="card col-md-12 mt-2">
      <div id="carouselExampleControls" class="carousel slide mb-5" data-ride="carousel" data-interval="100000">
        <div class="w-100 carousel-inner mb-3">
          <div class="carousel-item active">
            <div class="row">
              <div class="col-md-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>The Telegraph </h2>
                      <small><q>True professionals with an excellent understanding for the online recruitment industry. I have no problem recommending to all..</q></small>
                      <h3 class="smallest mute">Bhavin Trivedi</h3>
                    </div>
                  </div>
                </div>
              </div>
              </div>
            </div>
              <div class="carousel-item">
                <div class="row">
              <div class="col-md-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>Michael Page</h2>
                      <small><q>I just wanted to thank you very much for all your help! I found Apply4U to be an extremely helpful, proactive recruitment tool - impressively getting me the results where 
                        they counted.
                      </q></small>
                      <h3 class="smallest mute">Ania Taylor</h3>
                    </div>
                  </div>
                  </div>
                  </div>
                </div>
              </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>McGregor Boyall</h2>
                      <small><q>My Apply4U consultant was very professional in his approach and excellent at managing my expectations. He gave me very practical advice and got me the result I wanted, matching 
                        my specific strengths within the specified time-frame. He has high energy and is very personable to deal with.
                      </q></small>
                      <h3 class="smallest mute">Rachel Graham</h3>
                    </div>
                  </div>
                </div>
              </div>
              </div>
              </div>
              <div class="carousel-item">
                <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>Propeller Recruitment</h2>
                      <small><q>I used Apply4U for the first time earlier this summer and have been impressed by the quality of candidates that they are able to source. Although it is still very early 
                        for 
                        us both I have already placed one of the candidates and have great hopes of doing so again very soon.</q></small>
                      <h3 class="smallest mute">Alan Morgan </h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>Piedmont IMD</h2>
                      <small><q>I have been approached and have worked with a number of search to search firms, but none have come close to 
                        identifying what exactly 
                        I was looking for. Apply4U.co.uk sourced and presented two candidates – both of whom were spot 
                        on, and one of which now is part of my team.</q></small>
                      <h3 class="smallest mute">Karla Dorsch </h3>
                    </div>
                  </div>
                </div>
              </div>
              </div>
              </div>
              <div class="carousel-item">
                <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>Logic Engagements</h2>
                      <small><q>Whilst I have only been involved with Apply 4U for a short time, I am impressed with their level of service,
                         industry knowledge and efficiency. The model appears to represent excellent value for money</q></small>
                      <h3 class="smallest mute">Ian Threlfall</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>MRA Associates</h2>
                      <small><q>
                        I first came across Iffy 4 years ago when I was helping to set up the UK operation of Career Builder. He is a guy of high integrity with a desire to deliver and do a great job. 
                        Enthusiastic and resourceful beyond his years he is probably one of the few Sales people I have met over the last 20 years who continues to follow his dream...of running his own business. Keep an eye on this guy he`s 
                        good not a 
                        one trick pony!!!</q></small>
                      <h3 class="smallest mute">Andrew Rutherford </h3>
                    </div>
                  </div>
                </div>
              </div>
              </div>
              </div>
              <div class="carousel-item">
                <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2>ReRecruit</h2>
                      <small><q>Within 1 week of signing up to Apply4U.co.uk, we hired a consultant, an Apply4U recommended candidate. She has now been with us 8 weeks and has 
                        already placed 12 successful placements within the Childcare sector. She is earning up to £5,000 per month 
                        and planning to grow the care division.</q></small>
                      <h3 class="smallest mute">Naz Tapander</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"><i class="fas fa-chevron-left"></i></span>
          <span class="sr-only">Previous</span>
        </a>
        <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"><i class="fas fa-chevron-right"></i></span>
          <span class="sr-only">Next</span>
        </a>
      </div>
    </div>
  </div>
</section>
<!--Client Testimonials-->


<!--Help & Advice-->
<section class="HelpAdvice">
  <div class="container-xl customSize2">
    
    <div class="row" [ngClass]="ContextService.IsMobileView ? 'carousel slide': ''" id="carouselHelp" data-ride="carousel">
      <div class="carousel-inner">
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
        <h2 class="h1"><span>Help</span> and Advice</h2>
      </div>
      <div class="row">
      <div [ngClass]="ContextService.IsMobileView ? 'carousel-item item active': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
        <div id="PerInfo" class="accordion">
            <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard maxhight' : ''">
            <div class="card-header show in" data-toggle="collapse">
              <span class="title">Recruitment Advice</span>
            </div>
            <div class="panel-collapse collapse show" aria-labelledby="headingTwo">
              <div class="panel-body">
                <div class="row m-0">
                  <div class=" col-md-12 VideoCard">
                    <div class="card-body">
                        <img src="assets/images/home/<USER>" loading="lazy" alt="CV" title="Your CV" width="100" height="100">
                          <p>Struggling to find the right candidate to supercharge your company? We've got you covered! Our comprehensive
                          guide covers all the key factors you need to consider before, during, and after the recruitment process, ensuring
                          you make the best hiring decisions.</p>
                        <a href="/recruitment-advice" class="btn btn-primary mt-3">Recruitment Advice</a>
                      </div>
                  </div>
                </div>
                </div>
          </div>
          </div>
        </div>
      </div>
      <div class="" [ngClass]="ContextService.IsMobileView ? 'carousel-item': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
        <div class="accordion" id="PerInfo">
          <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard maxhight' : ''">
            <div class="card-header show in" data-toggle="collapse">
              <span class="title">Blogs </span>
            </div>
            <div class="panel-collapse collapse show" aria-labelledby="headingOne">
              <div class="panel-body">
                <div class="row m-0">
                  <div class=" col-md-12 VideoCard">
                    <div class="card-body">
                      <img src="assets/images/home/<USER>" loading="lazy" alt="Job Search" title="Your Job Search" width="100" height="100">
                        <p> Whether you're a hiring manager searching for your next superstar or a recruiter seeking top talent,
                        our blogs provide invaluable <strong>help and advice</strong> to address all your pain points, offering insights, strategies, 
                        and tips to make your recruitment process more effective and efficient.</p>

                      <a href="/blogs" class="btn btn-primary" [ngClass]="ContextService.IsMobileView ? 'mb-3' : 'mt-3'">See all Blogs</a>

                      </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="" [ngClass]="ContextService.IsMobileView ? 'carousel-item': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
        <div class="accordion" id="PerInfo">
          <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard maxhight' : ''">
            <div class="card-header show in" data-toggle="collapse">
              <span class="title">Need More Help? </span>
            </div>
            <div class="panel-collapse collapse show" aria-labelledby="headingTwo">
              <div class="panel-body">
                <div class="row m-0">
                  <div class=" col-md-12 VideoCard">
                    <div class="card-body">
                      <img src="assets/images/home/<USER>" loading="lazy" alt=" Interview" title="Your Interview" width="100" height="100">
                      <p>If you're an existing member struggling with the process, why not consider upgrading your account? We will help
                        you with your candidate search and so much more so you can feel 
                        confident in ensuring you find the right fit for your company.</p>
                      <a class="btn btn-primary mt-3" (click)="RedirectToUpgrades()">View Upgrades</a>
                      </div>
                  </div>
                </div>
                </div>
          </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    <ol *ngIf="ContextService.IsMobileView" class="carousel-indicators indicators">
      <li data-target="#carouselHelp" data-slide-to="0" class="active"></li>
      <li data-target="#carouselHelp" data-slide-to="1"></li>
      <li data-target="#carouselHelp" data-slide-to="2"></li>
    </ol>
    </div>

    <div class="row PartnerBox">
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
        <h2 class="mt-4 mb-4">Our <span>Trusted Partners</span></h2>
      </div>
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
        <div id="carouselPartners" class="carousel slide" data-ride="carousel">
          <div class="carousel-inner">
            <div class="carousel-item item active">
              <a href="/companies/aspire-people-profile-101441">
                <img src="assets/images/home/<USER>" loading="lazy" alt="apple" width="122px" height="121px" class="img">
            </a>
            </div>
            <div class="carousel-item item">
              <img src="assets/images/a4u-app-banner/Img-02.png" loading="lazy" alt="NHS" width="122px" height="121px" class="img">
            </div>
            <div class="carousel-item item">
              <img src="assets/images/home/<USER>" loading="lazy" alt="asics" width="122px" height="121px" class="img">
            </div>
            <div class="carousel-item item">
              <img src="assets/images/home/<USER>" loading="lazy" alt="Adecco" width="122px" height="121px" class="img">
            </div>
            <div class="carousel-item item">
              <img src="assets/images/home/<USER>" loading="lazy" alt="image" width="122px" height="121px" class="img">
            </div>
          </div>
          <ol class="carousel-indicators mt-5">
            <li data-target="#carouselPartners" data-slide-to="0" class="active"></li>
            <li data-target="#carouselPartners" data-slide-to="1"></li>
            <li data-target="#carouselPartners" data-slide-to="2"></li>
            <li data-target="#carouselPartners" data-slide-to="3"></li>
            <li data-target="#carouselPartners" data-slide-to="4"></li>
          </ol>
        </div>
      </div>
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center mt-4">
        <a class="btn btn-primary" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{ Navigations.CompanyDirectory }}">See All Companies</a>
      </div>
    </div>

  </div>
</section>
<!--Help & Advice-->


<!-- Blog -->
<section class="blog">
  <div class="container-xl customSize3">
    <div class="row d-flex justify-content-center">
      <h2 class="btn-text">Our Blogs</h2>
      <div class="col-12" *ngIf="ContextService.IsDesktopView">
        <div class="row m-0">
          <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 mb-4 mb-md-0" *ngFor="let blog of BlogsList; let i = index;">
            <div class="card BoxListing">
              <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{BlogsUrl}}{{blog?.RedirectUrl}}" class="cursor">
                <img *ngIf="blog.ThumbnailPath" alt="{{blog.Title}}" src="{{BaseUrl}}/{{blog.ThumbnailPath}}" loading="lazy" width="100" height="100" />
                <img *ngIf="!blog.ThumbnailPath" alt="{{blog.Title}}" src="assets/images/Groups/BlogPost.png" loading="lazy" width="100" height="100" />
              </a>
              <div class="card-body">
                <h2 class="title-elipsis cursor text-center" title="{{blog.Title}}"><a
                    routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{BlogsUrl}}{{blog?.RedirectUrl}}" class="text-bold">
                    {{blog.Title}}</a></h2>
              </div>
              <div class="height">
                <p class="content-elipsis" title="{{blog.QuickSummary}}">{{blog.QuickSummary}}</p>
              </div>
              <a class="btn-textt btn btn-primary btn-sm mt-2" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{BlogsUrl}}{{blog?.RedirectUrl}}">Blog Detail</a>
            </div>
          </div>
        </div>
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
          <a class="btn btn-primary allBlogs" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{ Navigations.Blog }}">See All Blogs</a>
        </div>
      </div>
      <div class="row m-0" *ngIf="ContextService.IsMobileView">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center carousel slide" id="Blogscarousel" data-ride="carousel">          
            <div class="carousel-inner mb-3">
                  <div class="row m-0">
                 <div class="col-12 carousel-item" *ngFor="let blog of BlogsList; let i = index; first as isFirst" [ngClass]="{active:isFirst}" >
                  <div class="card BoxListing">
                    <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{BlogsUrl}}{{blog?.RedirectUrl}}" class="cursor">
                      <img *ngIf="blog.ThumbnailPath" alt="{{blog.Title}}" src="{{BaseUrl}}/{{blog.ThumbnailPath}}" loading="lazy" width="100" height="100" />
                      <img *ngIf="!blog.ThumbnailPath" alt="{{blog.Title}}" src="assets/images/Groups/BlogPost.png" loading="lazy" width="100" height="100" />
                    </a>
                    <div class="card-body">
                      <h2 class="title-elipsis cursor text-center" title="{{blog.Title}}"><a
                          routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{BlogsUrl}}{{blog?.RedirectUrl}}" class="text-bold">
                          {{blog.Title}}</a></h2>
                    </div>
                    <div class="height">
                      <p class="content-elipsis" title="{{blog.QuickSummary}}">{{blog.QuickSummary}}</p>
                    </div>
                <a class="btn-textt btn btn-primary btn-sm mt-2" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{BlogsUrl}}{{blog?.RedirectUrl}}">Blog Detail</a>
                  </div>
                  </div>
                 
            </div>
         
          <ol class="carousel-indicators">
            <li data-target="#Blogscarousel" data-slide-to="0" class="active"></li>
            <li data-target="#Blogscarousel" data-slide-to="1"></li>
            <li data-target="#Blogscarousel" data-slide-to="2"></li>
            <li data-target="#Blogscarousel" data-slide-to="3"></li>
          </ol>
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
            <a class="btn btn-primary allBlogs" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{ Navigations.Blog }}">See All Blogs</a>
          </div>
        </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</section>
<!-- Blog -->

<!-- Trustpilot -->
@defer (on viewport()) {
  <section class="Trustpilot">
    <div class="container">
      <trust-pilot-widget></trust-pilot-widget>
    </div>
  </section>
  } @placeholder {
    <section class="Trustpilot"></section>
  }
<!-- Trustpilot -->


