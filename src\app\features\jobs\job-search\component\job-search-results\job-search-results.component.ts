import { Component, On<PERSON><PERSON>roy, OnInit, makeStateKey, TransferState, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { ActivatedRoute, Router } from '@angular/router';
import { BreadCrumb } from '@apply4u/models/breadcrumb';
import { KeyValueStrings } from '@apply4u/models/common/key-value-pair';
import { JobGateJobAlert } from '@apply4u/models/jobGateJobAlert';
import { Job } from '@apply4u/models/search/job';
import { ResultsWithOutPagination } from '@apply4u/models/search/results-with-out-pagination';
import { SearchParams } from '@apply4u/models/search/search-params';
import { Sector } from '@apply4u/models/sector';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { SeoContent } from '@apply4u/models/seoContent';
import { TotalJobAlert } from '@apply4u/models/total-job-alert';
import { HttpClientWrapper } from '@apply4u/services';
import { SetJob<PERSON>lert, SetJobGateJobAlert, SetTotalJobAlert } from '@apply4u/services/attb-job-alert.service';
import { ContextService } from '@apply4u/services/context-service';
import { AdvanceSearchService } from '@apply4u/services/data-services/advance-search.service';
import { LoadingService } from '@apply4u/services/loading.service';
import { MasterDataService } from '@apply4u/services/masterdata.service';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { JobSearchService } from '@apply4u/services/search/job-search.service';
import { SearchService } from '@apply4u/services/search/search.service';
import { ShowHideLogsService } from '@apply4u/services/showHideLogs.service';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { DatePosted, Relevancy } from '@apply4u/shared/constant/constant';
import { BrowseJob_RouteUrl, JobsIn_RouteUrl, Jobs_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';
import { SeoContentFor } from '@apply4u/shared/constant/seoContentForConstant';
import { IsNotNumeric } from '@apply4u/shared/helpers/common/numeric-helpers';
import { ReplaceHyphenWithSpace, ToCamelCase, ToLowerCase, ToTitleCase } from '@apply4u/shared/helpers/common/string-helpers';
import { IsAny, IsFalse, IsNotAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber, IsNullOrEmpty, IsTrue } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { FormatSeoUrl, GetLocationForSeo } from '@apply4u/shared/helpers/seo-helper';
import { environment } from '@environment/environment';
import { forkJoin } from 'rxjs/internal/observable/forkJoin';
import { JobType } from '@apply4u/shared/enums/job-type.enum';
import { SearchDurationConst } from '@apply4u/shared/constant/search-within/search-duration-const';

const STATE_KEY_JOB_SEARCH_RESUTLS = makeStateKey<any>('JobSearchResutls');

@Component({
  templateUrl: './job-search-results.component.html',
  styleUrls: ['./job-search-results.component.css'],
})
export class JobSearchResultsComponent implements OnInit, OnDestroy {
  destroyRef = inject(DestroyRef);
  Jobs: Job[] = [];
  TotalRecords: number = null;
  ResultsFoundFor: string;
  SearchParams: SearchParams;
  SeoContent: SeoContent;
  SearchedText: string;
  CreatedCanonicalUrlForBreadcrumbs: string;
  BreadCrumbsDisplayList: BreadCrumbs;
  ContextService: ContextService;
  Skills: string[];
  IsSearchyByIndustry: boolean = false;
  SearchResult: ResultsWithOutPagination<Job>;
  SeoIndustryName: string;
  ShouldShowLocationMessage: boolean = false;
  IsServerResults: boolean = false;
  HiddenJobId : number = 0;
  //Subscription : Subscription;
  IsExternalRecords: boolean = false;
  SelectedJobTypes: string = null;
  JobTypes = JobType;
  isLoading = false;

  constructor(private loadingService: LoadingService,
    private toasterService: ToasterMessageService,
    private contextService: ContextService,
    private jobSearchService: JobSearchService,
    private metaService: MetaWrapperService,
    private masterDataService: MasterDataService,
    private http: HttpClientWrapper,
    private searchService: SearchService,
    private router: Router,
    private state: TransferState,
    private activatedRoute: ActivatedRoute,
    private advanceSearchService: AdvanceSearchService,
    private showHideLogsService: ShowHideLogsService,) {
      this.SearchParams = this.searchService.SearchParams = new SearchParams();
      this.SearchParams.SearchWithIn = SearchDurationConst.AnytimeOrAll;
      this.ContextService = this.contextService;
      this.SearchParams.SearchWithIn = SearchDurationConst.AnytimeOrAll;
     // this.GetServerResults();
    }

  ngOnInit(): void {  
    this.BreadCrumbsDisplayList = new BreadCrumbs();
    this.searchService.IsSearchPage = true;
    this.contextService.SetIsJobSeeker(true);
    this.InitializeSearch();
    this.showHideLogsService.NotifyHideJob$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(isSuccess => {  this.FilterHiddenJobs(isSuccess); });
  }

  FilterHiddenJobs(isSuccess: boolean){
    if(isSuccess){
      this.Jobs =  this.Jobs.filter(x=> x.Id != this.HiddenJobId);
      this.TotalRecords = this.TotalRecords -1
    }

    if (IsNotAny(this.Jobs) && this.Jobs.length == 0) {
      if (IsNotZero(this.TotalRecords) && this.TotalRecords > 10) {
        if (IsNotZero(this.SearchParams.PageNumber)) {          
          this.OnPageNumberChanged(this.SearchParams.PageNumber);          
        }
      }
    }   
  }

  ngOnDestroy(): void {
    this.metaService.RemoveCanonicalTagsIfExists();
  }

  GetServerResults() {
    let results = this.state.get<ResultsWithOutPagination<Job>>(STATE_KEY_JOB_SEARCH_RESUTLS, null);

    if (IsNotNull(results) && !this.contextService.IsClientSideLoading) {
      this.IsServerResults = true;
      this.ResultsProcessor(results);
    }
  }

  InitializeSearch(): void {
    this.SearchParams.IsSimpleSearch = true;
    this.activatedRoute.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParamMap) => {
      if (queryParamMap.has("page")) {
        this.SearchParams.PageNumber = parseInt(queryParamMap.get("page"));
      }

      if (queryParamMap.has("pagesize")) {
        this.SearchParams.PageSize = parseInt(queryParamMap.get("pagesize"));
      }
      
      if (this.activatedRoute.snapshot.paramMap.has("keywords")) {
        this.SearchParams.Keywords = this.activatedRoute.snapshot.paramMap.get("keywords");
        this.SearchedText = this.SearchParams.Keywords;

        if (this.contextService.IsSeoRequired) {
          this.CreatedCanonicalUrlForBreadcrumbs = this.metaService.GetCurrentPageURLWithoutQueryParams();
          this.metaService.CreateCanonicalURL(this.CreatedCanonicalUrlForBreadcrumbs);
        }
      } else if (queryParamMap.has("q")) {
        this.SearchParams.Keywords = queryParamMap.get("q");
        this.SearchedText = this.SearchParams.Keywords;

        if (this.contextService.IsSeoRequired) {
          this.CreatedCanonicalUrlForBreadcrumbs = `${this.metaService.GetCurrentPageURLWithoutQueryParams()}?q=${encodeURIComponent(this.SearchParams.Keywords)}`;
          this.metaService.CreateCanonicalURL(this.CreatedCanonicalUrlForBreadcrumbs);
        }
      } else {
        if (this.contextService.IsSeoRequired) {
          this.metaService.CreateCanonicalURL(this.metaService.GetCurrentPageURLWithoutQueryParams());
        }
      }

      if (queryParamMap.has("radius")) {
        this.SearchParams.Radius = parseInt((queryParamMap.get("radius").toString()));
      } else {
        this.SearchParams.Radius = 0;
      }

      if (queryParamMap.has("salaryfrom")) {
        this.SearchParams.SalaryFrom = parseFloat(queryParamMap.get("salaryfrom"));
      }

      if (queryParamMap.has("salaryto")) {
        this.SearchParams.SalaryTo = parseFloat(queryParamMap.get("salaryto"));
      }

      if (queryParamMap.has("unspecifiedsalaries")) {
        this.SearchParams.IncludeUnspecifiedSalaries = queryParamMap.get("unspecifiedsalaries") === 'true';
      }

      if (queryParamMap.has("salaryduration")) {
        this.SearchParams.SalaryDuration = parseInt(queryParamMap.get("salaryduration"));
      }

      if (queryParamMap.has("jobtypes")) {
        this.SearchParams.JobTypes = queryParamMap.get("jobtypes");
      }

      if (queryParamMap.has("jobstatus")) {
        this.SearchParams.JobStatus = queryParamMap.get("jobstatus");
      }

      if (queryParamMap.has("within")) {
        let searchWithIn = queryParamMap.get("within");

        if(IsNotNullOrEmpty(searchWithIn)){
          this.SearchParams.SearchWithIn = parseInt(searchWithIn);
        }        
      }

      if(queryParamMap.has("sectorids")){
        this.SearchParams.SectorIds = queryParamMap.get("sectorids");
      }      

      if (queryParamMap.has("sortby")) {
        this.SearchParams.SortBy = queryParamMap.get("sortby");
        this.SearchParams.IsCustomSort = true;

        if (ToLowerCase(this.SearchParams.SortBy) == ToLowerCase(Relevancy)) {
          this.SearchParams.SortBy = Relevancy;
        } else {
          this.SearchParams.SortBy = DatePosted;
        }
      }

      if (queryParamMap.has("applyfilter")) {
        this.SearchParams.IsFilterSearch = (Boolean)(queryParamMap.get("applyfilter"));
      } else {
        this.SearchParams.IsFilterSearch = false;
      }

      if (queryParamMap.has("allofthesewords")) {
        this.SearchParams.AllofTheseWords = decodeURIComponent((queryParamMap.get("allofthesewords")));
      } else {
        this.SearchParams.AllofTheseWords = "";
      }

      if (queryParamMap.has("exactphrase")) {
        this.SearchParams.ExactPhrae = (queryParamMap.get("exactphrase"));
      } else {
        this.SearchParams.ExactPhrae = "";
      }

      if (queryParamMap.has("anyofthesewords")) {
        this.SearchParams.AnyOfTheseWords = (queryParamMap.get("anyofthesewords"));
      } else {
        this.SearchParams.AnyOfTheseWords = "";
      }

      if (queryParamMap.has("noneofthesewords")) {
        this.SearchParams.NoneOfTheseWords = (queryParamMap.get("noneofthesewords"));
      } else {
        this.SearchParams.NoneOfTheseWords = "";
      }

      if (IsNotNullOrEmpty(this.SearchParams.Keywords)) {
        this.SearchParams.Keywords = decodeURIComponent(this.SearchParams.Keywords);
        let paramStringParts = [];
        var isPopularSearch = false;

        if (this.SearchParams.Keywords.indexOf("-near-me") !== -1) {
          isPopularSearch = true;
        }

        paramStringParts = this.SearchParams.Keywords.split("-jobs-in-");

        if (paramStringParts.length <= 1) {
          paramStringParts = this.SearchParams.Keywords.split("jobs-in-");
        }

        if (paramStringParts.length <= 1) {
          paramStringParts = this.SearchParams.Keywords.split("-jobs");
        }

        if (paramStringParts.length > 1) {
          let keywords = "";
          let location = "";

          if (isPopularSearch) {
            keywords = paramStringParts[0];
          } else {
            keywords = paramStringParts[0];
            location = paramStringParts[1];
          }

          keywords = keywords.replace('-jobs', '');
          keywords = keywords.replace('-job', '');
          keywords = keywords.replace('-JOBS', '');
          keywords = keywords.replace('-JOB', '');
          keywords = keywords.replace('-Jobs', '');
          keywords = keywords.replace('-Job', '');
          keywords = keywords.replace(/-/g, ' ');
          keywords = keywords.replace(/\s\s+/g, ' ');
          this.SearchParams.Keywords = ToLowerCase(keywords);

          if (IsNotNullOrEmpty(location)) {
            let locationParts = location.split("-");

            if (IsNotNull(locationParts) && locationParts.length > 1) {
              this.SearchParams.Location = (location.replace(/-/g, ' '));
              this.SearchParams.Location = (this.SearchParams.Location.replace(/_/g, " "));
              this.SearchParams.Location = (this.SearchParams.Location.replace(/[{'}]/g, ")"));
              this.SearchParams.Location = ToCamelCase((this.SearchParams.Location.replace(/[{~}]/g, '(')));
            } else {
              this.SearchParams.Location = location.replace(/_/g, " ");
              this.SearchParams.Location = (this.SearchParams.Location.replace(/[{'}]/g, ")"));
              this.SearchParams.Location = ToCamelCase((this.SearchParams.Location.replace(/[{~}]/g, '(')));
            }

            this.searchService.CheckRadiusDisableStatus();
          }
        } else {
          this.SearchParams.Keywords = this.SearchParams.Keywords.replace(/-/g, ' ');
          this.SearchParams.Keywords = this.SearchParams.Keywords.replace(/\s\s+/g, ' ');
        }

        if (this.SearchParams.IsSearchBySector) {
          this.SearchParams.SectorName = ToLowerCase(FormatSeoUrl(this.SearchParams.Keywords));
        }
      }

      if (this.SearchParams.IsCustomSort == false) {
        if (IsNotNullOrEmpty(this.SearchParams.Keywords)) {
          this.SearchParams.SortBy = Relevancy;
        } else {
          this.SearchParams.SortBy = DatePosted;
        }
      }

      this.ResultsFoundFor = this.SearchParams.Keywords;

      if (this.contextService.IsSeoRequired) {
        this.AddSeoMetaTags();
      }

      this.SearchParams.SeoContentFor = SeoContentFor.SimpleJobSearch;
      this.PopulateBreadCrumbsDisplayList().then(() => {
        this.AddBreadCrumbsForSeo();
      });

      this.ExecuteJobSearch();
    });
  }

  AddSeoMetaTags(): void {
    if (IsNotNull(this.searchService.SearchParams)) {
      this.MatchJobsWordInKeywordString(this.SearchParams.Keywords);
      let wordsInTitleCase = ToTitleCase(this.SearchParams.Keywords);
      let locationInCamelCase = GetLocationForSeo(this.SearchParams.Location);

      if (IsNotNullOrEmpty(this.SearchParams.Location) && IsNotNullOrEmpty(this.SearchParams.Keywords)) {
        this.metaService.SetPageTitle(`${wordsInTitleCase} Jobs in ${locationInCamelCase} | Apply4U`);
        this.metaService.UpdateMetaTag('description', `Search and apply online for ${wordsInTitleCase} jobs in ${locationInCamelCase} on Apply4U, Your Helpful Community Jobsite for careers, vacancies and UK job search`);
      } else if (IsNotNullOrEmpty(this.SearchParams.Keywords) && IsNullOrEmpty(this.SearchParams.Location)) {
        this.metaService.SetPageTitle(`${wordsInTitleCase} Jobs | Apply4U | Helpful Job Search UK`);
        this.metaService.UpdateMetaTag('description', `Search and apply online for ${wordsInTitleCase} jobs on Apply4U, Your Helpful Community Jobsite for careers, vacancies and UK job search`);
      } else if (IsNotNullOrEmpty(this.SearchParams.Location) && IsNullOrEmpty(this.SearchParams.Keywords)) {
        this.metaService.SetPageTitle(`${locationInCamelCase} Jobs | Apply4U | Helpful Job Search UK`);
        this.metaService.UpdateMetaTag('description', `Search and apply online for jobs in ${locationInCamelCase} on Apply4U, Your Helpful Community Jobsite for careers, vacancies and UK job search`);
      } else {
        this.metaService.SetPageTitle('Job Search | Jobsite that apply 10 jobs a day for you | Jobs Near Me | Apply4U');
        this.metaService.UpdateMetaTag('description', 'Search jobs and apply online on 1000s of jobs near you on Apply4U. Find a job or let us find you the jobs and we even apply on 10 jobs a day for you');
      }
    }
  }

  AddBreadCrumbsForSeo(): void {
    if (IsAny(this.BreadCrumbsDisplayList?.itemListElement)) {
      let breadcrumbSchema = new BreadCrumbs();
      let position = 1;

      this.BreadCrumbsDisplayList.itemListElement.forEach(item => {
        let homeItem = new ListItem();

        homeItem.position = position;
        homeItem.name = item.name.toLocaleLowerCase();
        homeItem.item = `${environment.HostName}${item.item.toLocaleLowerCase()}`;
        breadcrumbSchema.itemListElement.push(homeItem);

        position += 1;
      });

      this.metaService.CreateBreadCrumbsScript(JSON.stringify(breadcrumbSchema));
    }
  }

  PopulateBreadCrumbsDisplayList(): Promise<string> {
    this.BreadCrumbsDisplayList.itemListElement = [];
    let sectorName = null;

    if (IsNotNullOrEmpty(this.SearchParams.Keywords) && IsNotNumeric(this.SearchParams.Keywords)) {
      sectorName = this.SearchParams.Keywords;
    }

    let browseItem = new ListItem();

    browseItem.name = "Browse Jobs";
    browseItem.item = BrowseJob_RouteUrl;
    this.BreadCrumbsDisplayList.itemListElement.push(browseItem);

    return new Promise((resolve, reject) => {
      if (IsNotNullOrEmpty(sectorName) && IsNotNullOrEmpty(this.SearchParams.Location)) {
        let sectorApiRequest = this.masterDataService.GetParentSectorByChildSectorName(FormatSeoUrl(sectorName));
        let locationApiRequest = this.masterDataService.GetLocationHierarchyAsync(this.SearchParams.Location.toLowerCase());

        forkJoin([sectorApiRequest, locationApiRequest]).pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(([sectorResult, locationHierarchy]) => {
            this.PopulateSectorBreadCrumb(sectorResult, sectorName);
            this.PopulateLocationBreadCrumb(locationHierarchy, sectorName);
            resolve("Completed sucessfully.");
          }, ([sectorError, locationError]) => {
            this.AddSectorBreadCrumb(sectorName);
            this.AddLocationBreadCrumb(this.SearchParams.Location);
            resolve("Something went wrong.");
          });
      } else if (IsNotNullOrEmpty(sectorName)) {
        this.masterDataService.GetParentSectorByChildSectorName(FormatSeoUrl(sectorName)).pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(sectorResult => {
            this.PopulateSectorBreadCrumb(sectorResult, sectorName);
            resolve("Completed sucessfully.");
          }, errorResult => {
            this.AddSectorBreadCrumb(sectorName);
            resolve("Something went wrong.");
          });
      } else if (IsNotNullOrEmpty(this.SearchParams.Location)) {
        this.masterDataService.GetLocationHierarchyAsync(this.SearchParams.Location.toLowerCase()).pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(locationHierarchy => {
            this.PopulateLocationBreadCrumb(locationHierarchy);
            resolve("Completed sucessfully.");
          }, errorResult => {
            this.AddLocationBreadCrumb(this.SearchParams.Location);
            resolve("Something went wrong.");
          });
      }
    });
  }

  PopulateSectorBreadCrumb(sectorResult: Sector, sectorName: string) {
    if (IsNotNull(sectorResult)) {
      let parentSectorItem = new ListItem();

      parentSectorItem.name = sectorResult.SectorName;
      parentSectorItem.item = `${Jobs_RouteUrl}/${sectorResult.SectorSeoName}-jobs`;
      this.BreadCrumbsDisplayList.itemListElement.push(parentSectorItem);
      this.SearchParams.SectorName = ToLowerCase(FormatSeoUrl(sectorResult.SectorName));

      if (ToLowerCase(sectorResult.SectorName) != ToLowerCase(sectorName)) {
        this.SearchParams.SubSectorName = ToLowerCase(FormatSeoUrl(sectorName));

        this.AddSectorBreadCrumb(sectorName);
      }
    } else {
      this.AddSectorBreadCrumb(sectorName);
    }
  }

  PopulateLocationBreadCrumb(locationHierarchy: KeyValueStrings[], sectorName: string = "") {
    if (IsAny(locationHierarchy)) {
      locationHierarchy.forEach(location => {
        let breadCrumbItem = new ListItem();

        if (IsNotNullOrEmpty(sectorName)) {
          sectorName = `${sectorName}-`;
        }

        breadCrumbItem.name = ToTitleCase(location.Key);
        breadCrumbItem.item = `${Jobs_RouteUrl}/${FormatSeoUrl(sectorName)}jobs-in-${FormatSeoUrl(location.Value.replace(", ", "-"))}`;
        this.BreadCrumbsDisplayList.itemListElement.push(breadCrumbItem);
      });
    } else {
      this.AddLocationBreadCrumb(this.SearchParams.Location);
    }
  }

  AddSectorBreadCrumb(sectorName: string) {
    let breadcrumb = new ListItem();

    breadcrumb.name = ToTitleCase(ReplaceHyphenWithSpace(sectorName));
    breadcrumb.item = `${Jobs_RouteUrl}/${FormatSeoUrl(sectorName.toLocaleLowerCase())}-jobs`;
    this.BreadCrumbsDisplayList.itemListElement.push(breadcrumb);
  }

  AddLocationBreadCrumb(locationName: string) {
    let breadcrumb = new ListItem();

    breadcrumb.name = ToTitleCase(ReplaceHyphenWithSpace(locationName));
    breadcrumb.item = `${JobsIn_RouteUrl}${FormatSeoUrl(locationName.toLocaleLowerCase())}`;
    this.BreadCrumbsDisplayList.itemListElement.push(breadcrumb);
  }

  MatchJobsWordInKeywordString(keyword: any): void {
    if (IsNotNullOrEmpty(keyword)) {
      let foundedJobs = keyword.match(/Jobs/gi);
      let foundedJob = keyword.match(/Job/gi);

      keyword.replace(foundedJob, "");
      keyword.replace(foundedJobs, "");
    }
  }

  ExecuteJobSearch(): void {
    this.RemoveSpecialCharactersFromKeywords();
    // this.loadingService.Show();
    this.isLoading = true;
    // if (!this.IsServerResults || this.contextService.IsClientSideLoading || this.contextService.IsUserLoggedIn) {
    //   if (this.contextService.IsClientSideLoading) {
    //     this.loadingService.Show();
    //   }

      this.jobSearchService.JobSearchNew(this.searchService.SearchParams).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: searchResult => {
          this.isLoading = false;
          // if (this.contextService.IsServer) {
          //   this.state.set<ResultsWithOutPagination<Job>>(STATE_KEY_JOB_SEARCH_RESUTLS, searchResult);
          // } else {
          //   this.state.set<ResultsWithOutPagination<Job>>(STATE_KEY_JOB_SEARCH_RESUTLS, null);
          // }

          this.ResultsProcessor(searchResult);
          // this.loadingService.Hide();
        },
        error: errorResult => {
          this.isLoading = false;
          this.TotalRecords = 0;
          // this.loadingService.Hide();
        }
      });
    //}
  }

  ResultsProcessor(searchResult: ResultsWithOutPagination<Job>): void {
    if (IsNotNull(searchResult)) {
      this.Jobs = searchResult.Response;
      
      if (IsAny(searchResult.Response)) {
        this.Jobs = searchResult.Response;
        this.jobSearchService.SearchHash = searchResult.SearchHash;

        const externalJobIndex = this.Jobs.findIndex(e => e.Id == 0);

        if (externalJobIndex != -1) {
          this.Jobs[externalJobIndex].IsExternalResultStart = true;
          this.IsExternalRecords = true;
        }

        if (this.contextService.IsBrowser) {
          this.searchService.SetRecentSearches(true);
        }

        if (IsTrue(searchResult.IsWorkFromHomeJobSearch)) {
          if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
            if (this.searchService.SearchParams.JobTypes.indexOf(this.JobTypes.WorkFromHome) == -1) {
              this.searchService.SearchParams.JobTypes += ',' + this.JobTypes.WorkFromHome;
            }
          } else {
            this.searchService.SearchParams.JobTypes = this.JobTypes.WorkFromHome;
          }
        }

        if (IsTrue(searchResult.IsContractJobSearch)) {
          if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
            if (this.searchService.SearchParams.JobTypes.indexOf(this.JobTypes.Contract) == -1) {
              this.searchService.SearchParams.JobTypes += ',' + this.JobTypes.Contract;
            }
          } else {
            this.searchService.SearchParams.JobTypes = this.JobTypes.Contract;
          }
        }

        if (IsTrue(searchResult.IsPartTimeJobSearch)) {
          if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
            if (this.searchService.SearchParams.JobTypes.indexOf(this.JobTypes.PartTime) == -1) {
              this.searchService.SearchParams.JobTypes += ',' + this.JobTypes.PartTime;
            }
          } else {
            this.searchService.SearchParams.JobTypes = this.JobTypes.PartTime;
          }
        }

        if (IsTrue(searchResult.IsPermanentJobSearch)) {
          if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
            if (this.searchService.SearchParams.JobTypes.indexOf(this.JobTypes.Permanent) == -1) {
              this.searchService.SearchParams.JobTypes += ',' + this.JobTypes.Permanent;
            }
          } else {
            this.searchService.SearchParams.JobTypes = this.JobTypes.Permanent;
          }
        }

        if (IsTrue(searchResult.IsTemporaryJobSearch)) {
          if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
            if (this.searchService.SearchParams.JobTypes.indexOf(this.JobTypes.Temporary) == -1) {
              this.searchService.SearchParams.JobTypes += ',' + this.JobTypes.Temporary;
            }
          } else {
            this.searchService.SearchParams.JobTypes = this.JobTypes.Temporary;
          }
        }

        if (IsTrue(searchResult.IsGraduateTraineeSearch)) {
          if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
            if (this.searchService.SearchParams.JobTypes.indexOf(this.JobTypes.GarduateTrainee) == -1) {
              this.searchService.SearchParams.JobTypes += ',' + this.JobTypes.GarduateTrainee;
            }
          } else {
            this.searchService.SearchParams.JobTypes = this.JobTypes.GarduateTrainee;
          }
        }

        if (IsNotNullOrEmpty(this.searchService.SearchParams.JobTypes)) {
          this.SelectedJobTypes = this.searchService.SearchParams.JobTypes;
        }
        
        this.TotalRecords = searchResult.TotalRecords;
        this.ShouldShowLocationMessage = searchResult.ShouldShowLocationMessage;
      }

      if (IsNotNull(searchResult.SeoContent)) {
        this.SeoContent = searchResult.SeoContent;
        if (IsNotNull(this.SeoContent.Sector) && IsNotNull(this.SeoContent.Sector.SectorSeoName)) {
          this.SeoIndustryName = this.SeoContent.Sector.SectorSeoName;
        } else if (IsNullOrEmpty(this.SeoContent.Sector)) {
          this.SeoIndustryName = this.searchService.SearchParams.Keywords;
        }
      }

      this.TotalRecords = searchResult.TotalRecords;
      this.ShouldShowLocationMessage = searchResult.ShouldShowLocationMessage;
    } else {
      this.TotalRecords = 0;
    }
  }

  RemoveSpecialCharactersFromKeywords(): void {
    if (IsNotNullOrEmpty(this.SearchParams.Keywords)) {
      this.SearchParams.Keywords = this.SearchParams.Keywords.replace(new RegExp('”', 'g'), '"');
      this.SearchParams.Keywords = this.SearchParams.Keywords.replace(new RegExp('“', 'g'), '"');
    }
  }

  SetJobAlert(): void {
    let jobTitle = this.SearchParams.Keywords;
    let location = this.SearchParams.Location;

    if (IsNotZero(this.contextService.LoggedInUserId)) {
      SetJobAlert(this.http, this.contextService, this.toasterService, this.loadingService, jobTitle, location);
      
      let jobGateAlert = new JobGateJobAlert();
      let totalJobAlert = new TotalJobAlert();

      jobGateAlert.UserId = this.contextService.LoggedInUserId;
      jobGateAlert.SearchKeyWord = jobTitle;
      jobGateAlert.LocationText = location;
      totalJobAlert.UserId = this.contextService.LoggedInUserId;
      totalJobAlert.SearchKeyword = jobTitle;
      totalJobAlert.LocationText = jobTitle;
      totalJobAlert.EmailConsent = true;

      let hasPostCode = /\d/;

      if (hasPostCode.test(location)) {
        let locParts = location.split(",");

        if (locParts.length > 0) {
          jobGateAlert.PostCode = locParts[locParts.length - 1];
        }
      }

      SetJobGateJobAlert(this.http, jobGateAlert).subscribe();
      this.advanceSearchService.CreateAutoSaveSearch(this.SearchParams);
      SetTotalJobAlert(this.http, totalJobAlert).subscribe();      
    } else {
      this.toasterService.LoginMessageError();
    }
  }

  OnPageNumberChanged(pageNumber: number): void {
    this.SearchParams.PageNumber = pageNumber;

    this.searchService.SearchRedirection();
  }

  OnSortByChange(sortBy: string): void {
    this.SearchParams.SortBy = sortBy;
  }

  OnPageSizeChanged(pageSize: number): void {
    this.SearchParams.PageSize = pageSize;

    this.searchService.SearchRedirection();
  }

  OnConfirmSaveSearchModal(): void {
    this.advanceSearchService.OnClickConfirmSaveSearchModal();
  }

  OnCloseSaveSearchModal(): void {
    this.advanceSearchService.OnCloseConfirmSaveSearchModal();
  }

  OnHideJob(job : Job): void {
    this.HiddenJobId = job.Id;
    this.showHideLogsService.SaveHideJobInfo(job);
  }
}
