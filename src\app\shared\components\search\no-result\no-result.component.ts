import { Component, DebugElement, Input, OnChanges, OnInit, SimpleChang<PERSON> } from '@angular/core';
import { SearchDuration } from '@apply4u/models/search/search-duration';
import { SearchParams } from '@apply4u/models/search/search-params';
import { ContextService } from '@apply4u/services/context-service';
import { SearchService } from '@apply4u/services/search/search.service';
import { SearchDurations } from '@apply4u/shared/constant/search-within/search-durations';
import { IsNotNumeric } from '@apply4u/shared/helpers/common/numeric-helpers';
import { ToTitleCase } from '@apply4u/shared/helpers/common/string-helpers';
import { IsNotNull, IsNotNullOrEmpty, IsTrue } from '@apply4u/shared/helpers/common/type-safe-helpers';

@Component({
  selector: 'search-no-result',
  templateUrl: './no-result.component.html',
  styleUrls: ['./no-result.component.css']
})
export class NoResultComponent implements OnInit, OnChanges {

  @Input() TotalRecords: number;
  @Input() ShouldShowLocationMessage: boolean;
  @Input() IsRemoteWork: boolean = false;
  SearchService: SearchService;
  contextService: ContextService;
  ResultsFoundFor: string ="";
  InLocation: string ="";
  radius: any;
  SearchWithInText: string ="";
  SearchWithInFilterCollection: SearchDuration[] = SearchDurations;

  constructor(private _searchService: SearchService,
    private _contextService: ContextService) {
    this.SearchService = this._searchService;
    this.contextService = this._contextService;
  }

  ngOnInit() {  
    if (IsNotNumeric(this.SearchService.SearchParams.Keywords)) {
      this.ResultsFoundFor = ToTitleCase(this.SearchService.SearchParams.Keywords);
    }else {
      this.ResultsFoundFor = `${this.SearchService.SearchParams.Keywords}`;
    }

    if (IsNotNullOrEmpty(this.SearchService.SearchParams.Location)) {
      this.InLocation = ToTitleCase(this.SearchService.SearchParams.Location);
    }
    
    if (this.SearchService.SearchParams.Radius) {      
      this.radius = this.SearchService.SearchParams.Radius;
    }

    this.GetSearchWithInText();
  }

  ngOnChanges(changes: SimpleChanges) {
    this.GetSearchWithInText();
  }

  GetSearchWithInText(): void {
    if (IsNotNull(this.SearchService.SearchParams.SearchWithIn) && this.SearchService.SearchParams.SearchWithIn != -1) {
      if (IsNotNullOrEmpty(this.ResultsFoundFor)) {
        this.SearchWithInText = `${this.ResultsFoundFor}${this.contextService.IsJobSeeker ? ' jobs' : ' CVs'}`;

        if (IsNotNullOrEmpty(this.InLocation)) {
          this.SearchWithInText = this.SearchWithInText;
          this.SearchWithInText += IsTrue(this.ShouldShowLocationMessage) ? '' : ' near ' + this.InLocation;
        }
      } else if (IsNotNullOrEmpty(this.InLocation)) {
        let selectedWithInId = this.SearchWithInFilterCollection.filter(search => search.Id == this.SearchService.SearchParams.SearchWithIn);
        this.SearchWithInText = `${this.contextService.IsJobSeeker ? ' jobs' : ' CVs'}`;
        this.SearchWithInText += IsTrue(this.ShouldShowLocationMessage) ? '' : ' near ' + this.InLocation + ` within last ${selectedWithInId[0].Description}`;
      } else if(this.SearchService.SearchParams.SearchWithIn === 0){
        this.SearchWithInText = this.contextService.IsJobSeeker ? ' jobs' : ' CVs';
      }
      else {
        // Empty Search
        let selectedWithInId = this.SearchWithInFilterCollection.filter(search => search.Id == this.SearchService.SearchParams.SearchWithIn);
        this.SearchWithInText = `${this.contextService.IsJobSeeker ? ' jobs' : ' CVs'}` + (!!selectedWithInId && !!selectedWithInId[0] && !!selectedWithInId[0].Id && selectedWithInId[0].Id > 0 ? ` within last ${selectedWithInId[0].Description}` : '');
      }
    } 
    else{
      this.SearchWithInText = this.contextService.IsJobSeeker ? ' jobs' : ' CVs';
    }
    // else {
    //   if (IsNotNullOrEmpty(this.ResultsFoundFor)) {
    //     if (IsNotNumeric(this.ResultsFoundFor)) {
    //       this.SearchWithInText = this.ResultsFoundFor + `${this.contextService.IsJobSeeker ? ' jobs' : ' CVs'}`;
    //     } else {
    //       this.SearchWithInText = `${this.contextService.IsJobSeeker ? ' jobs' : ' CV'} ${this.ResultsFoundFor} found`;
    //     }
    //     if (IsNotNullOrEmpty(this.InLocation)) {
    //       this.SearchWithInText = `${this.SearchWithInText}`;
    //       this.SearchWithInText += IsTrue(this.ShouldShowLocationMessage) ? '' : ' in ' + this.InLocation;
    //     }
    //   } else if (IsNotNullOrEmpty(this.InLocation)) {
    //     this.SearchWithInText = `${this.contextService.IsJobSeeker ? ' jobs' : ' CVs'}`;
    //     this.SearchWithInText += IsTrue(this.ShouldShowLocationMessage) ? '' : ' in ' + this.InLocation;
    //   } else {
    //     this.SearchService.SearchParams.IsCustomSort = true;

    //     this.SearchWithInText = `${this.contextService.IsJobSeeker ? ' jobs' : ' CVs'} found`;
    //   }
    // }
  }

  onclick(IstitleSearch, IsLocationSearch) {
    this.SearchService.SearchParams = new SearchParams();
    if (IsTrue(IstitleSearch)) {
      this.SearchService.SearchParams.Keywords = this.ResultsFoundFor;
    } else if (IsTrue(IsLocationSearch)) {
      this.SearchService.SearchParams.Location = this.InLocation;
    }
    this.SearchService.SearchParams.IsSimpleSearch = true;
    this.SearchService.SearchRedirection();
  }
}
