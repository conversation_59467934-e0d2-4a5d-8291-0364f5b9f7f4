<div class="row m-0" *ngFor="let job of Jobs; let indx = index;">
    <div *ngIf="job?.IsExternalResultStart" class="text-center col-md-12 p-0">
        <!-- <p class="resultfrompartner"><span *ngIf="job?.InterJobsTotalCount != 0">No more results found</span></p> -->
        <div class="mt-2 mb-2 col-12 p-0">
            <a href="{{ContactUsCardUTMSource}}" *ngIf="IsSearchResultPage">
                <img class="img-fluid register-banner"
                    src="assets/images/Job-Search-Banner-Cards/Cant-find-what-you-looking-For-Card.webp"
                    alt="Can't find what you're looking for?" width="100" height="100">
            </a>
        </div>
        <p class="resultfrompartner"><span *ngIf="job?.PartnerJobsTotalCount">{{job?.PartnerJobsTotalCount}}</span> More
            results
            from our partners</p>
    </div>
    <!-- <div class="row m-0" *ngIf="(indx +1)% 6 === 0 && !IsExternalRecords">
        <div class="mt-2 mb-2 col-12 p-0" *ngIf="IsSearchResultPage && !ContextService.IsUserLoggedIn">
            <a href="{{Apply4UCardsUTMSource}}">
                <img class="img-fluid register-banner" loading="lazy" src="{{Apply4UCardImageSource}}"
                    alt="{{Apply4UCardAltText}}" width="100" height="100">
            </a>
        </div>
    </div> -->
    <div class="col-12 card p-3 JobCard" id="Job-{{job.Id}}"
        [ngClass]="{'JobCard-Red':job?.JobCategory == 'Featured' , 'JobCard-Green':job?.JobCategory == 'Sponsored' ,  'JobCard-Blue':job?.JobCategory == 'Multi Post' , 'JobCard-Grey':job?.JobCategory == 'External' , 'JobCard-Orange':job?.JobCategory == 'Standard'}">
        
        <div class="row m-0 mb-2">
            <div class="col-9 p-0">
                <span *ngIf="job?.JobCategory != 'Standard'" class="JobType btn"
                    title="{{job?.JobCategory}}">{{job?.JobCategory}}</span>
                <span *ngIf="ContextService.IsUserLoggedIn && (ContextService.IsApply4UAdmin || A4UAdmin) && job.JobSourceText"
                    class="sourceType btn" title="{{job?.JobSourceText}}">{{job?.JobSourceText}}</span>
            </div>
            <div *ngIf="ContextService.IsUserLoggedIn" class="col-3 p-0 JobCardIcon">
                <button
                    *ngIf="job?.IsInternalSource && job?.UserConnectionId != 0 && job?.UserConnectionStatusId == 1"
                    title="Message this user"><a routerLinkActive="active"
                        [routerLinkActiveOptions]="{ exact: true }"
                        routerLink="/messaging/{{job?.PostedByUserId}}"><i class="fas fa-comment-dots"></i></a>

                </button>
                <button *ngIf="job?.Id != 0" data-toggle="modal" title="Share this Job"
                    id="btnShareJob_{{job?.Id}}" (click)="OnShareJobClick(job)">
                    <i class="fa fa-share-alt"></i>
                </button>
                <button *ngIf="job?.Id != 0 && IsShortlistRequired" id="btnRemoveShortlist_{{job?.Id}}"
                    title="Remove this Job" (click)="OnRemoveJobFromShortlistClick(job?.ShortListDetailId)">
                    <i class="fa fa-heart heart-filled"></i>
                </button>
                <button *ngIf="job?.Id != 0 && !IsShortlistRequired && !IsMyApplicationPage && !HideShortlist"
                    id="btnWishList_{{job?.Id}}" [ngClass]="{'active':job?.IsShortListed == true}"
                    title="{{job?.IsShortListed ? 'Job shortlisted' : 'Shortlist job'}}" data-toggle="modal"
                    (click)="OnWishListIconClick(job)">
                    <i *ngIf="job?.IsShortListed" class="fa fa-heart"></i>
                    <i *ngIf="!job?.IsShortListed" class="far fa-heart"></i>
                </button>
                <button *ngIf="IsShowIconRequired || IsHideIconRequired && job?.Id != 0"
                    [title]="IsHideIconRequired && !IsShowIconRequired ? 'Hide this job' : 'Unhide this job'"
                    (click)="(IsHideIconRequired && !IsShowIconRequired) ? HideJobClick(job) : UnhideJobClick(job)">
                    <i
                        [ngClass]="IsHideIconRequired && !IsShowIconRequired ? 'fas fa-times' : 'fas fa-eye'"></i>
                </button>
                <button *ngIf="job?.Id != 0 && ContextService.IsApply4UAdmin"
                    id="btnRecommendationList_{{job?.Id}}" [ngClass]="{'active':job?.IsRecommended == true}"
                    title="{{job?.IsRecommended ? 'Job Recommended' : 'Recommend job'}}" data-toggle="modal"
                    (click)="OnRecommendationIconClick(job?.Id)">
                    <i *ngIf="job?.IsRecommended" class="fas fa-thumbs-up"></i>
                    <i *ngIf="!job?.IsRecommended" class="far fa-thumbs-up"></i>
                </button>
            </div>
        </div>
        
        <div class="row m-0">
            <div class="col-md-9 col-sm-12 p-0">
                <div class="row m-0">
                    <div class="col-md-12 p-0 JobTitle">
                        <a *ngIf="job?.Id != 0" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                            routerLink="{{ job?.Title | JobDetailUrlSmartPipe: job?.Id }}" id="{{job.Id}}"
                            title="{{job?.Title}} Job" (click)="OnClickFeaturedJobs()">
                            <h2 innerHtml="{{ job?.Title | highlight: WordsToHighlight: true }}" title="{{job?.Title}}">
                            </h2>
                        </a>
                        <a *ngIf="job?.Id == 0" href="{{ job?.ApplicationUrl }}" target="_blank"
                            title="{{job?.Title}} Job">
                            <h2 innerHtml="{{ job?.Title | highlight: WordsToHighlight: true }}" title="{{job?.Title}}">
                            </h2>
                        </a>
                    </div>
                    <div class="col-md-12 p-0">
                        <p class="posted">Posted {{job?.DisplayStartDate | localDateTime | timeAgo:true}}
                            <span *ngIf="(job?.PostedByUserName || job?.CompanyName) && job.Id !=0">by</span>
                            <a *ngIf="job?.PostedByUserName" class="companyLink" routerLinkActive="active"
                                [routerLinkActiveOptions]="{ exact: true }"
                                routerLink="/users/{{job?.PostedByUserName}}-{{job?.PostedByUserId}}" title="{{job?.PostedByUserName}}">
                                {{job?.PostedByUserName}}</a>
                            <span *ngIf="job?.CompanyName && job.Id !=0">&#64;</span>
                            <a *ngIf="job?.CompanyName && job.Id != 0" class="companyLink" routerLinkActive="active"
                                [routerLinkActiveOptions]="{ exact: true }"
                                [routerLink]="[job?.CompanyName | CompanyProfileUrlPipe: job?.CompanyId : true]"
                                [queryParams]="{ Jobs: true }" title="{{job?.CompanyName}}">
                                {{job?.CompanyName}}
                            </a>
                            <span *ngIf="job?.CompanyName && job.Id == 0" title="{{job?.CompanyName}}">
                                {{job?.CompanyName}}</span>
                        </p>
                    </div>
                    <div class="col-md-12 LocationInfo p-0">
                        <i *ngIf="job?.LocationText" class="fa fa-map-marker-alt"></i>
                        <span *ngIf="job?.LocationText" 
                            title="Jobs in {{ job?.LocationText }}">{{job?.LocationText}}</span>
                            <span *ngIf="job?.AreaName">, {{job?.AreaName}}</span>
                    </div>
                    <div class="col-md-12 p-0">
                        <ul>
                            <li *ngIf="job?.Salary"><i class="fa fa-pound-sign"></i> {{job?.Salary}}</li>
                            <li *ngIf="job?.JobType"><i class="far fa-clock"></i> {{job?.JobType}}</li>
                            <li *ngIf="IsMyApplicationPage && job?.AppliedOn"><i class="far fa-calendar"></i>
                                {{job?.AppliedOn | localDate }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-12 p-0">
                <!-- <div class="row m-0 text-right">
                    <div *ngIf="ContextService.IsUserLoggedIn" class="col-12 p-0 JobCardIcon mb-3">
                        <button
                            *ngIf="job?.IsInternalSource && job?.UserConnectionId != 0 && job?.UserConnectionStatusId == 1"
                            title="Message this user"><a routerLinkActive="active"
                                [routerLinkActiveOptions]="{ exact: true }"
                                routerLink="/messaging/{{job?.PostedByUserId}}"><i class="fas fa-comment-dots"></i></a>

                        </button>
                        <button *ngIf="job?.Id != 0" data-toggle="modal" title="Share this Job"
                            id="btnShareJob_{{job?.Id}}" (click)="OnShareJobClick(job)">
                            <i class="fa fa-share-alt"></i>
                        </button>
                        <button *ngIf="job?.Id != 0 && IsShortlistRequired" id="btnRemoveShortlist_{{job?.Id}}"
                            title="Remove this Job" (click)="OnRemoveJobFromShortlistClick(job?.ShortListDetailId)">
                            <i class="fa fa-heart heart-filled"></i>
                        </button>
                        <button *ngIf="job?.Id != 0 && !IsShortlistRequired && !IsMyApplicationPage"
                            id="btnWishList_{{job?.Id}}" [ngClass]="{'active':job?.IsShortListed == true}"
                            title="{{job?.IsShortListed ? 'Job shortlisted' : 'Shortlist job'}}" data-toggle="modal"
                            (click)="OnWishListIconClick(job)">
                            <i *ngIf="job?.IsShortListed" class="fa fa-heart"></i>
                            <i *ngIf="!job?.IsShortListed" class="far fa-heart"></i>
                        </button>
                        <button *ngIf="IsShowIconRequired || IsHideIconRequired && job?.Id != 0"
                            [title]="IsHideIconRequired && !IsShowIconRequired ? 'Hide this job' : 'Unhide this job'"
                            (click)="(IsHideIconRequired && !IsShowIconRequired) ? HideJobClick(job) : UnhideJobClick(job)">
                            <i
                                [ngClass]="IsHideIconRequired && !IsShowIconRequired ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                        </button>
                        <button *ngIf="job?.Id != 0 && ContextService.IsApply4UAdmin"
                            id="btnRecommendationList_{{job?.Id}}" [ngClass]="{'active':job?.IsRecommended == true}"
                            title="{{job?.IsRecommended ? 'Job Recommended' : 'Recommend job'}}" data-toggle="modal"
                            (click)="OnRecommendationIconClick(job?.Id)">
                            <i *ngIf="job?.IsRecommended" class="fas fa-thumbs-up"></i>
                            <i *ngIf="!job?.IsRecommended" class="far fa-thumbs-up"></i>
                        </button>
                    </div>
                </div> -->
                <div class="companyLogo" [ngClass]="job?.CompanyName ? 'cursor': '' " routerLinkActive="active"
                    [routerLinkActiveOptions]="{ exact: true }"
                    [routerLink]="[job?.CompanyName | CompanyProfileUrlPipe: job?.CompanyId : true]" [queryParams]="{ Jobs: true }">
                    <span class="inline-b"><img class="img-fluid" 
                        onerror="this.src='/assets/images/preview/Default-Company-Image.png'"
                        [src]="job?.LogoUrl"
                            loading="lazy" alt="{{job?.CompanyName}}"
                            title="{{job?.CompanyName}}" /></span>
                </div>
                <div class="text-center flag-mobile" *ngIf="IsMyApplicationPage">
                    <span class="text-muted">
                        <i title="Cool"
                            [ngClass]=" !job.CandidateSelfSuitableTypeId || job.CandidateSelfSuitableTypeId == SuitabilityTypEnum.Cool ? 'greenflag' : 'text-muted' "
                            class="fa fa-flag cursor"
                            on-click="OnChangeCandidateSuitabilityType(job.DetailId,SuitabilityTypEnum.Cool)"></i>&nbsp;

                        <i title="Medium"
                            [ngClass]=" !job.CandidateSelfSuitableTypeId || job.CandidateSelfSuitableTypeId == SuitabilityTypEnum.Medium ? 'yellowflag' : 'text-muted' "
                            class="fa fa-flag cursor"
                            on-click="OnChangeCandidateSuitabilityType(job.DetailId,SuitabilityTypEnum.Medium)"></i>&nbsp;

                        <i title="Hot"
                            [ngClass]=" !job.CandidateSelfSuitableTypeId || job.CandidateSelfSuitableTypeId == SuitabilityTypEnum.Hot ? 'redflag': 'text-muted' "
                            class="fa fa-flag cursor"
                            on-click="OnChangeCandidateSuitabilityType(job.DetailId,SuitabilityTypEnum.Hot)"></i>&nbsp;
                    </span>
                </div>
                <div *ngIf="IsMyApplicationPage" class="row p-0">
                    <div class=" col-md-12 display-grid">
                        <a class="btn btn-primary btn-lg" title="See more jobs like this" routerLinkActive="active"
                            [routerLinkActiveOptions]="{ exact: true }"
                            routerLink="{{ job?.Title | JobSearchDetailUrlPipe : job?.LocationText}}"> See more jobs
                            like this</a>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="row" *ngIf="ContextService.IsMobileView">
                <div class="col-12 d-flex flex-column align-items-stretch mb-2 mt-3">
                    <!-- Quick Apply Button -->
                    @if(ContextService.IsUserLoggedIn){
                    <button
                        *ngIf="job.JobSourceText && !HideApplyNow && !job?.IsApplied && !!job && !!job.Id && job.Id > 0 
                        && ((job?.JobSourceId != JobSource.Barchester && job?.JobSourceId != JobSource.CvLibrary && job?.JobSourceId != JobSource.Appcast 
                        && job?.JobSourceId != JobSource.AppCastPaid && job?.JobSourceId != JobSource.AppcastPriority && job?.JobSourceId != JobSource.ZipRecruiter 
                        && job?.JobSourceId != JobSource.ZipRecruiterEnt && job?.JobSourceId != JobSource.ZipRecruiterJB) 
                        || (job?.JobSourceId == JobSource.CvLibrary && JobApplicationService.IsCvDistributed == true))"
                        (click)="JobApplicationService.QuickApply(job)" class="btn btn-sm btn-primary customStyle mb-2"
                        title="Quick Apply">
                        <i class="far fa-clock"></i>&nbsp;Quick Apply
                    </button>
                    }
                    <!-- Apply Now Button -->
                    <button (click)="job?.IsApplied ? null : JobApplicationService.ApplyNow(job)" class="btn btn-sm btn-success"
                        [ngClass]="{'disabled Applied': job?.IsApplied}" [title]="job?.IsApplied ? 'Already Applied' : 'Apply to Job'">
                        <i [ngClass]="job?.IsApplied ? 'fas fa-check-circle' : 'fa fa-paper-plane'"></i>&nbsp;{{ job?.IsApplied ?
                        'Applied' : 'Apply Now' }}
                    </button>
                </div>
            </div>
            <div class="row m-0 justify-content-end" *ngIf="ContextService.IsDesktopView">
                <!-- <div class="col-md col-sm-6 p-0 apply-button mt-3" *ngIf="!IsMyApplicationPage">
                    <a *ngIf="job?.Id != 0" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                        routerLink="{{ job?.Title | JobDetailUrlSmartPipe: job?.Id }}" title="{{job?.Title}} Job Details"
                        target="_blank" class="sectorlink" style="color: #21602F;text-decoration: none;" role="button">Learn
                        more
                    </a>
                    <a *ngIf="job?.Id == 0" href="{{ job?.ApplicationUrl }}" target="_blank"
                        style="color: #21602F;text-decoration: none;" title="{{job?.Title}} Job"> Learn more
                    </a>
                </div> -->
                <div *ngIf="!HideApplyNow"
                    class="col-12 p-0 apply-button text-right">

                    @if(ContextService.IsUserLoggedIn){
                    <button (click)="JobApplicationService.QuickApply(job)" class="btn btn-sm btn-primary customStyle"
                    *ngIf="!job?.IsApplied && job.JobSourceText && !!job && !!job.Id && job.Id > 0 && 
                    ((job?.JobSourceId != JobSource.Barchester && job?.JobSourceId != JobSource.CvLibrary && job?.JobSourceId != JobSource.Appcast 
                    && job?.JobSourceId != JobSource.AppCastPaid && job?.JobSourceId != JobSource.AppcastPriority && job?.JobSourceId != JobSource.ZipRecruiter 
                    && job?.JobSourceId != JobSource.ZipRecruiterEnt && job?.JobSourceId != JobSource.ZipRecruiterJB) 
                    || (job?.JobSourceId == JobSource.CvLibrary && JobApplicationService.IsCvDistributed == true))"
                        style="margin-right: 5px;" title="Quick Apply">
                        <i class="far fa-clock"></i>&nbsp;Quick Apply
                    </button>
                    }
                    
                    <button 
                        (click)="job?.IsApplied ? null : JobApplicationService.ApplyNow(job)" 
                        class="btn btn-sm btn-success"
                        [ngClass]="{'disabled Applied': job?.IsApplied}"
                        [title]="job?.IsApplied ? 'Already Applied' : 'Apply to Job'">
                        <i [ngClass]="job?.IsApplied ? 'fas fa-check-circle' : 'fa fa-paper-plane'"></i>&nbsp;{{ job?.IsApplied ? 'Applied' : 'Apply Now' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 card mobile-ad-banner"
        *ngIf="ShowBanner && indx == 6 && CurrentPageNumber == 1 && ShowDownloadBanner">
        <span class="close-btn" (click)="HideBanner()">&times;</span>
        <div class="image-container">
            <img class="mobile-image" src="assets/images/a4u-app-banner/mob-ad-banner.webp" loading="lazy"
                alt="Mobile application" width="414" height="479" />
            <img class="desktop-image" src="assets/images/a4u-app-banner/desktop-baner.webp" loading="lazy"
                alt="Desktop application" width="875" height="335" />
        </div>
        <div class="app-buttons">
            <a href="https://apps.apple.com/gb/app/apply4u/id1531296867" target="_blank" (click)="OnDownloadApp()">
                <img src="assets/images/a4u-app-banner/ios-app.webp" loading="lazy" width="87" height="28"
                    alt="Download on App Store" />
            </a>
            <a href="https://play.google.com/store/apps/details?id=com.apply4u" target="_blank"
                (click)="OnDownloadApp()">
                <img src="assets/images/a4u-app-banner/gplay-app.webp" loading="lazy" width="87" height="28"
                    alt="Download on Google Play" />
            </a>
        </div>
    </div>
</div>
<button class="d-none" data-toggle="modal" data-target="#JobWishListModal" id="JobWishListModalId"></button>
<job-wish-list-modal *ngIf="WishListJobId" [JobId]="WishListJobId" (WishListChange)="OnWishListChange($event)"
    (WishListDelete)="OnWishListDelete($event)"></job-wish-list-modal>
<share-job-modal *ngIf="ShareJobId" [PostUrl]="PostUrl" [RecommendationModel]="RecommendationModel"></share-job-modal>
<recommendations-modal *ngIf="RecommendedId" [RecommendedId]="RecommendedId"
    [RecommendationTypeId]="RecommendationType.Job"
    (RecommendationChange)="OnRecommendationChange($event)"></recommendations-modal>
