<div class="container-fluid containerBg">
  <nav class="navbar mx-auto nav-max-width navbar-icon-top navbar-expand-lg navbar-dark bg-dark fixed-topp">
    <button class="navbar-toggler togglerIcon collapsed" name="btnmanu" aria-label="Mobile-menu" (click)="ToggleNavbarCollapse()">
      <span class="navbar-toggler-icon" [ngClass]="NavbarCollapse ? '':'navbar-toggler-icon-cross'"></span>
    </button>
    <button class="navbar-toggler togglerIcon collapsed d-none" type="button" data-toggle="collapse"
      data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
      aria-label="Toggle navigation" id="navbartoggle">
    </button>

    <a class="navbar-brand company-logo mr-md-0 mr-xl-3" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
      [routerLink]="ContextService?.IsJobSeeker ? '/' : '/recruiter'">
      <img src="assets/images/A4U-logo.webp" width="45" height="45" alt="Apply4U"
        title="Apply4U | The Social Recruitment Jobsite" alt="Apply4U | The Social Recruitment Jobsite"
        class="img-responsive" />
    </a>

    <button class="navbar-toggler Notify" type="button" data-toggle="collapse" data-target="#MobileUser"
      aria-controls="MobileUser" aria-expanded="false" aria-label="Toggle navigation"
      [ngClass]="ContextService?.IsUserLoggedIn ? '' : 'd-none'" *ngIf="ContextService?.IsDesktopView">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="Navigations.DashBoard" title="Dashboard"><i class="fa fa-tachometer icon"
              aria-hidden="true"></i></a>
        </li>
      </ul>
    </button>

    @if (ContextService.IsMobileView) {

    @if (ContextService?.IsUserLoggedIn == false) {
    <button class="navbar-toggler MobileDD MobileUserNotLoggedIn" style="padding: 0px !important;" type="button"
      data-toggle="collapse" data-target="#MobileUser">
      <div class="dropdown-item p-0">
        <div class="d-flex">          
          <div class="mr-4">
            <a href="JavaScript:Void(0);" on-click="SignInHandler()" title="Sign In/Join">
              <div><i class="fa fa-user-lock signIn"></i></div>
              <div>Sign In</div>
            </a>
          </div>
          <div class="">
            <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="/registration-type"
              title="Join Now">
              <div><i class="fa fa-user signIn"></i></div>
              <div>Join Now</div>
            </a>
          </div>
        </div>
      </div>
    </button>
    } @else{
    <button class="navbar-toggler MobileDD" type="button" data-toggle="collapse" data-target="#MobileUser"
      aria-controls="MobileUser" aria-expanded="false" aria-label="Toggle navigation">
      <ul class="navbar-nav">
        <li class="nav-item active">
          <span class="nav-link dropdown-toggle userImgLinkMobile" href="JavaScript:Void(0);" id="mobileUser"
            data-toggle="dropdown">
            <i class="userImg"><img id="UserImageMobileHeader" [src]="ProfileImageUrl" alt="User Profile Picture"
                onerror="this.src='assets/images/profileicon.webp'" width="25" height="25" /></i>
          </span>
          <div class="dropdown-menu MyProDD" aria-labelledby="mobileUser">
            <a class="dropdown-item mb-1 userId">User Id : {{ContextService?.LoggedInUserId}} </a>
            <span class="dashboard py-1" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.DashBoard">My Dashboard</span>
            <div class="dropdown-divider ml-2 mr-2 mt-3"></div>
            <div class="myProfile py-1" data-toggle="collapse" data-target="#myProfile" aria-expanded="false"
              aria-controls="MyProfile" (click)="MyProfileToggle($event)">My Profile
              <i *ngIf="MyProfileState" class="fa fa-angle-up float-right" data-toggle="collapse"
                data-target="#myProfile" aria-expanded="false" aria-controls="MyProfile"
                (click)="MyProfileToggle($event)"></i>
              <i *ngIf="!MyProfileState" class="fa fa-angle-down float-right" data-toggle="collapse"
                data-target="#myProfile" aria-expanded="false" aria-controls="MyProfile"
                (click)="MyProfileToggle($event)"></i>
            </div>

            <div class="collapse mt-2 mb-3" id="myProfile" [class.show]="MyProfileState">
              <a class="dropdown-item custom-dropdown-item " *ngIf="MyProfileRedirectedUrl" routerLinkActive="active"
                [routerLinkActiveOptions]="{ exact: true }" routerLink="{{MyProfileRedirectedUrl}}">My Profile</a>
              <a class="dropdown-item custom-dropdown-item " routerLinkActive="active"
                [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.MyCompany"
                *ngIf="ContextService?.IsUserLoggedIn && ContextService?.IsRecruiter">Company Details</a>
              <a class="dropdown-item custom-dropdown-item " routerLinkActive="active"
                [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.AccountSetting">Account
                Settings</a>
              <a class="dropdown-item custom-dropdown-item " routerLinkActive="active"
                [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.EmailAlert">Email Alerts</a>
              <a class="dropdown-item custom-dropdown-item " *ngIf="ContextService?.IsUserLoggedIn"
                routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                [routerLink]="Navigations.UserPayments">Payments</a>
            </div>
            <div class="dropdown-divider ml-2 mr-2"></div>

            <dropdown-items></dropdown-items>

            <div class="dropdown-divider ml-2 mr-2"></div>
            <a href="JavaScript:Void(0);" class="dropdown-item signUp" style="cursor: pointer; color: white;"
              (click)="OnLogout()"> Sign Out</a>
          </div>
        </li>
      </ul>
    </button>
    }

    }
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav mr-auto">
        <li class="nav-item dropdown">
          <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="'/'">Home</a>
        </li>
        @if (ContextService?.IsRecruiter) {
        <li class="nav-item dropdown">
          <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="Navigations.BrowseCVs">CVs</a>
        </li>
        }
        @if(ContextService?.IsJobSeeker) {
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="JavaScript:Void(0);" role="button"
            aria-haspopup="true" aria-expanded="false">Jobs</a>
          <div class="dropdown-menu customLink">
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.BrowseJob">Browse Jobs</a>
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.JobNearMe">Jobs Near Me</a>
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.WorkFromHome">Work from home Jobs</a>
          </div>
        </li>
        }

        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="JavaScript:Void(0);" role="button"
            aria-haspopup="true" aria-expanded="false">About</a>
          <div class="dropdown-menu customLink">
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.AboutUs"><span>Our Story</span></a>
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.HowItWorks"><span>How It Works</span></a>
            @if (ContextService?.IsJobSeeker) {
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.FeaturesForJobseeker">Jobseeker Features</a>
            }

            @if (ContextService?.IsRecruiter) {
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.FeaturesForRecruiter">Recruiter Feature</a>
            }
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="['/blogs']">Blogs</a>
            <div class="dropdown-divider ml-2 mr-2"></div>
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.OurTechnology">Our Technology</a>
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.Faq">FAQs</a>
            <a class="dropdown-item" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
              [routerLink]="Navigations.ContactUs">Contact Us</a>
          </div>
        </li>
        @if (ContextService?.IsJobSeeker) {
        <li class="nav-item dropdown" *ngIf="ContextService?.IsJobSeeker">
          <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="Navigations.CareerAdvice">Career Advice</a>
        </li>
        }

        @if (ContextService?.IsRecruiter) {
        <li class="nav-item dropdown">
          <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="Navigations.RecruitmentAdvice">Recruitment Advice</a>
        </li>
        }
        <li class="nav-item" style="margin-bottom: 5px;">
          <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="ContextService?.IsJobSeeker ? Navigations.JobseekerUpgrades : Navigations.RecruiterUpgrades"
            class="btn btn-outline-light btn-sm display recruiterbtn pricing-button" title="Pricing"><span>Pricing
            </span>
          </a>
        </li>
        @if (ContextService?.IsUserLoggedIn==false) {
        <li class="nav-item">
          <a class="btn btn-outline-light btn-sm recruiter-button  toggle-button" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }" [routerLink]="ContextService?.IsJobSeeker ? '/recruiter' : '/'"
            title="{{ContextService?.IsJobSeeker ? 'Switch to Recruiting?':'Switch to Job Search?'}}">
            {{ContextService?.IsJobSeeker ? 'Recruiting':'Job Search'}}</a>
        </li>
        }

      </ul>

      <!-- Right menu desktop start -->

      <ul class="navbar-nav">
        <li class="trustpilot ">
          <span class="trustpilot-widget header-trustpilot" data-locale="en-GB" id="trustbox-rating-mini" data-template-id="53aa8807dec7e10d38f59f32" data-businessunit-id="5ad869fc089028000118a067" data-style-height="120px" data-style-width="100%" data-theme="dark">
            <a href="https://uk.trustpilot.com/review/www.apply4u.co.uk" target="_blank" rel="noopener">Trustpilot</a>
          </span>         
        </li>

        @if (ContextService?.IsDesktopView == true && ContextService?.IsUserLoggedIn == true) {
        <li class="nav-item">
          <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            routerLink="{{ContextService?.IsJobSeeker ? Navigations.MyJobShortLists : Navigations.MyCVShortList }}"
            title="{{ContextService?.IsJobSeeker ? 'My Shortlists':'My Shortlists'}}">
            <i class="far fa-heart icon" aria-hidden="true"></i>
            <span class="display-text ml-3">Shortlists</span>
          </a>
        </li>
        <li class="nav-item dropdown">
          <a class="nav-link" title="Messages" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="Navigations.MessagingUrl">
            <i class="far fa-envelope icon" aria-hidden="true"></i>
            @if (TotalMessageNotifications) {
            <span class="badge badge-info headerBadge">{{TotalMessageNotifications}}</span>
            }
            <span class="display-text ml-3"> Messages</span></a>
        </li>
        <!-- Dropdown Notification Start     -->
        <li class="nav-item dropdown">
          <a class="nav-link noty-dropdown-toggle" data-toggle="dropdown" title="Notifications" role="button"
            aria-haspopup="true" aria-expanded="false" id="togglenotificationId" (click)="ToggleNotification()">
            <i class="fa fa-bell-o icon"></i>
            @if (TotalNotification > 0) {
            <span class="badge badge-info headerBadge">{{TotalNotification}}</span>
            }
            <span class="display-text ml-3"> Notifications</span></a>
          @if (ShowHideNotification && Notificatiosns && Notificatiosns.length>0) {
          <ul class="notification-menu">
            <li class="noti-li">
              Notifications
            </li>
            <div class="scrolDiv">

              @for (notify of Notificatiosns; track $index) {
              <li class="noti-border">
                <span>
                  <span (click)="MarkedAsViewdHandler(notify)" innerHtml="{{notify.Description}}"></span>
                  <br>{{notify.RecordedAt | localDateTime | timeAgo:true}}
                </span>
                <span>
                  @if (!notify.IsViewed) {
                  <div (click)="MarkedAsViewdHandler(notify)">
                    <span class="status-icon">
                      <abbr data-title="Mark as read">
                        <img src="assets/images/usermarker.webp" width="12px" height="10px">
                      </abbr>
                    </span>
                  </div>
                  }
                  <div (click)="OnDeleteFeedhandler(notify)">
                    <span class="cross-icon">
                      <abbr data-title="Clear notification">
                        <svg focusable="false" width="12px" height="15px" aria-hidden="true" viewBox="0 0 24 24">
                          <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5
                                                                 6.41 10.59 12 5 17.59 6.41 19 12
                                                                 13.41 17.59 19 19 17.59 13.41 12z" />
                        </svg>
                      </abbr>
                    </span>
                  </div>
                </span>
              </li>
              }

              @if (NotificationPageSize < TotalFeedCounts) { <li class="text-center">
                <a href="javascript:void(0);" tabindex="0" (focus)="LoadMoreFeedData()"
                  class="text-underline  bold text-lightblue">Load More
                </a>
        </li>
        }
    </div>
    </ul>
    }
    </li>

    <!-- Dropdown Notification End     -->
    <li class="nav-item hideMenu">
      <a class="nav-link" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
        [routerLink]="Navigations.DashBoard" title="Dashboard"><i class="fas fa-tachometer-alt icon"
          aria-hidden="true"></i>
        <span class="display-text ml-3">Dashboard</span></a>
    </li>
    }

    @if (ContextService?.IsDesktopView == true && ContextService?.IsUserLoggedIn ==false){
    <li class="nav-item">
      <a href="JavaScript:Void(0);" class="nav-link signinwidth login" style="cursor: pointer;"
        on-click="SignInHandler()" title="Sign In"><i class="fa fa-user-lock icon"></i> Sign In</a>
    </li>
    <li *ngIf="ContextService?.IsDesktopView" class="nav-item w-25">
      <a class="nav-link login" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
        routerLink="/registration-type" title="Join Now"><i class="fa fa-user icon"></i> Join Now</a>
    </li>
    }

    @if (ContextService?.IsDesktopView && ContextService?.IsUserLoggedIn) {
    <li class="nav-item dropdown hideMenu">
      <a class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true"
        aria-expanded="false">
        <i class="userImg"><img id="UserImageDesktopHeader" [src]="ProfileImageUrl" width="32" height="32"
            alt="User Profile Picture" /></i>
        {{ (MyProfile !=null && MyProfile.length > 15) ? (MyProfile | slice:0:15)+'...' : (MyProfile)}}
      </a>
      <div class="dropdown-menu MyProDD">
        <a class="dropdown-item mb-1 userId">User Id : {{ContextService?.LoggedInUserId}} </a>
        <span class="dashboard" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
          [routerLink]="Navigations.DashBoard">My Dashboard</span>
        <div class="dropdown-divider ml-2 mr-2 mt-2"></div>
        <div class="myProfile" style="cursor: pointer;" data-toggle="collapse" data-target="#myProfile"
          aria-expanded="false" aria-controls="MyProfile" (click)="MyProfileToggle($event)">My Profile
          @if (MyProfileState) {
          <i class="fa fa-angle-up float-right" style="cursor: pointer;" data-toggle="collapse" data-target="#myProfile"
            aria-expanded="false" aria-controls="MyProfile" (click)="MyProfileToggle($event)"></i>
          } @else {
          <i class="fa fa-angle-down float-right" style="cursor: pointer;" data-toggle="collapse"
            data-target="#myProfile" aria-expanded="false" aria-controls="MyProfile"
            (click)="MyProfileToggle($event)"></i>
          }
        </div>

        <div class="collapse mt-2 mb-3" id="myProfile" [class.show]="MyProfileState">
          @if (MyProfileRedirectedUrl) {
          <a class="dropdown-item custom-dropdown-item" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }" routerLink="{{MyProfileRedirectedUrl}}">My Profile</a>
          }

          @if (ContextService?.IsUserLoggedIn && ContextService?.IsRecruiter) {
          <a class="dropdown-item custom-dropdown-item" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.MyCompany">Company Details</a>
          }
          <a class="dropdown-item custom-dropdown-item" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.AccountSetting">Account
            Settings</a>
          <a class="dropdown-item custom-dropdown-item" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.EmailAlert">Email Alerts</a>

          @if (ContextService?.IsUserLoggedIn) {
          <a class="dropdown-item custom-dropdown-item" routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: true }" [routerLink]="Navigations.UserPayments">Payments</a>
          }

        </div>
        <div class="dropdown-divider ml-2 mr-2"></div>

        <dropdown-items></dropdown-items>
        <div class="dropdown-divider ml-2 mr-2"></div>
        <a href="JavaScript:Void(0);" class="dropdown-item signUp" style="cursor: pointer; color: white;"
          (click)="OnLogout()">Sign Out</a>
      </div>
    </li>
    }
    </ul>
    <!-- Right menu desktop end -->
</div>
</nav>
</div>