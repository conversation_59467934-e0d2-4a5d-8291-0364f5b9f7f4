import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { HiddenCv } from '@apply4u/Models/hidden-cvs';
import { CVShortListDetail } from '@apply4u/models/cv-wish-list/cv-short-list-detail';
import { RecommendationModel } from '@apply4u/models/recommendation-model';
import { Candidate } from '@apply4u/models/search/candidate';
import { UserNote } from '@apply4u/models/user-notes/user-note';
import { HttpClientWrapper } from "@apply4u/services";
import { ContextService } from '@apply4u/services/context-service';
import { CVDownloadService } from '@apply4u/services/downloads/cv-download.service';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { ScreenedCVsService } from '@apply4u/services/screened-cvs.service';
import { CvSearchService } from '@apply4u/services/search/cv-search.service';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { AddEmployerShortlistDetail } from '@apply4u/services/wish-lists/employer-short-list.service';
import { LoginTypeKeys } from '@apply4u/shared/constant/Local-storage-keys/login-type-keys';
import { Employer, Expert, Verified } from '@apply4u/shared/constant/constant';
import { Navigation } from '@apply4u/shared/constant/navigation/navigation';
import { CandidateType } from '@apply4u/shared/enums';
import { NoteType as NoteTypeEnum } from '@apply4u/shared/enums/note-type.enum';
import { RecommendationType } from '@apply4u/shared/enums/recommendation-type.enum';
import { IsAny, IsFalse, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber, IsNullOrEmpty, IsTrue } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { CVSearchDetailPipe } from '@apply4u/shared/pipes/cv-search-detail.pipe';
import { environment } from '@environment/environment';
import { Subscription, timer } from 'rxjs';

@Component({
  selector: 'smart-cv-card',
  templateUrl: './smart-cv-card.component.html',
  styleUrls: ['./smart-cv-card.component.css']
})
export class SmartCvCardComponent implements OnInit,OnDestroy {
  @Input() Candidates: Candidate[];
  @Input() WordsToHighlight: string;
  @Input() IsSearchResultPage: boolean = false;
  @Input() IsCVShortlistRequired: boolean = false;
  @Input() IsScreenedCVsResult: boolean = false;
  @Input() IsRecentApplicantPage: boolean = false;
  @Input() IsShowIconRequired: boolean = false;
  @Input() IsHideIconRequired: boolean = false;
  @Input() IsHomePageScreenedCandidate: boolean = false;
  @Input() CurrentPageNumber: number;
  @Input() CurrentVisitedPage: string;
  @Input() CompanyVerificationId:number;
  @Output() OnShowCandidateRequestPopup = new EventEmitter<Candidate>();
  @Output() OnRemoveShortlistDetailId: EventEmitter<number> = new EventEmitter<number>();
  @Output() CandidateDetailsDisplay: EventEmitter<number> = new EventEmitter<number>();
  @Output() OnHideCV: EventEmitter<Candidate> = new EventEmitter<Candidate>();
  @Output() OnUnhideCV: EventEmitter<HiddenCv> = new EventEmitter<HiddenCv>();

  CandidateType = CandidateType;
  WishListCandidateId: number;
  NoteCandidateId: number;
  RecommendationModel: RecommendationModel = new RecommendationModel();
  ContextService: ContextService;
  SelfVerification = Verified;
  EmployerVerification = Employer;
  ExpertVerification = Expert;
  Candidate: Candidate;
  CandidateTitle: string;
  ShowCandidateRequestPopup: boolean = false;
  NoteTypeId: number;
  CVDownloadSubscription: Subscription;
  CvSearchService: CvSearchService;
  RecommendationType = RecommendationType;
  RecommendedId: number;
  NoteForTypeId: number;
  IsPageEvenNumber:boolean = false;
  ShowBanner: boolean = true;
  CandidateCardsUTMSource: string = "";
  CandidateCardAltText: string = "";
  ContactUsCardUTMSource: string = "";
  Navigations = Navigation;

  constructor(private contextService: ContextService,
    private cvDownloadService: CVDownloadService,
    private toasterService: ToasterMessageService,
    private router: Router,
    private httpClient: HttpClientWrapper,
    public ScreenedCVsService: ScreenedCVsService,
    private cvSearchService: CvSearchService,
    private localStorage: LocalstorageService,) {
    this.ContextService = this.contextService;
    this.CvSearchService = this.cvSearchService;
  }
  
  ngOnInit(): void {  
    if (IsAny(this.Candidates)) {
      this.Candidates.forEach(element => {
        if (IsNotNull(element.NoteForTypeId) && element.NoteForTypeId == 3) {
          element.IsNotesExists = true;
        }
      });
    }

    this.CVDownloadSubscription = this.cvDownloadService.CVDownloadState.subscribe(candidateId => {
      if (IsAny(this.Candidates) && IsNotZeroNumber(candidateId)) {
        this.Candidates.filter(c => c.Id == candidateId)[0].DownloadedOn = new Date();
      }
    });
      }

  SetFocusOnViewedCandidate() {
    if (this.contextService.IsBrowser) {
      let element = this.localStorage.GetItem("CVCardYAxis");

      if (IsNotNull(element)) {
        timer(2000).subscribe(() => {
          let elementId = document.getElementById(element);

          if (IsNotNull(elementId)) {
            elementId.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'end' });
          }
          this.localStorage.RemoveItem("CVCardYAxis");
        });
      }
    }
  }

  ngAfterViewInit(): void {
    if (this.ContextService.IsBrowser) {
      this.SetFocusOnViewedCandidate();
    }
  }

  ngOnChanges(changes: SimpleChanges): void { 
        if(IsNotZeroNumber(changes['CurrentPageNumber']?.currentValue)){
      this.IsPageEven();
    }
  }

  ngOnDestroy() {    
    if (this.CVDownloadSubscription) {
      this.CVDownloadSubscription.unsubscribe();
    }
  }

  DownloadCV(candidateId: number, candidateTypeId: number, cv: any): void {
    if (this.contextService.IsUserLoggedIn && this.contextService.IsBrowser) {
      this.cvDownloadService.DownloadCV(candidateId, candidateTypeId);
    } else {
      this.localStorage.SetItem(LoginTypeKeys.DownloadCVUserSignInRedirection, "true");
      this.ContextService.SignInRedirect(true, this.Navigations.CvSearch + "/connect/" + candidateId.toString());
    }
  }

  OnWishListChange(isWishListed: boolean): void {
    let candIndex = this.Candidates.findIndex(d => d.Id == this.WishListCandidateId);
    this.Candidates[candIndex].IsShortListed = isWishListed;
  }

  OnRecommendationChange(isExist: boolean): void {
    let index = this.Candidates.findIndex(d => d.Id == this.RecommendedId);
    this.Candidates[index].IsRecommended = isExist;
  }

  OnRecommendationIconClick(candidateId: number): void {
    let tagetElement = document.getElementById(`btnRecommendationList_${candidateId}`);

    if (this.contextService.IsUserLoggedIn) {
      tagetElement.setAttribute("data-target", "#RecommendedListModal");
      this.RecommendedId = candidateId;
    } else {
      this.toasterService.LoginMessageError();
      tagetElement.setAttribute("data-target", "");
    }
  }

  SendRecommendation(candidateId: number, postUrl: string): void {
    if (this.contextService.IsUserLoggedIn) {
      this.RecommendationModel = new RecommendationModel();
      this.RecommendationModel.Title = "CV";
      this.RecommendationModel.RecommendedId = candidateId;
      this.RecommendationModel.RecommendedUrl = postUrl;
      this.RecommendationModel.RecommendedtoEmail = "";
      this.RecommendationModel.RecommendedbyUserId = this.contextService.LoggedInUserId;
    } else {
      this.toasterService.LoginMessageError();
    }
  }

  IsUserLoggedIn(event): boolean {
    if (this.contextService.IsUserLoggedIn == false) {
      event.stopPropagation();
      this.toasterService.LoginMessageError();

      return false;
    }

    return true;
  }

  OnClickShortlistIcon(candidate: Candidate) {
    this.WishListCandidateId = null;
    if (this.contextService.IsUserLoggedIn) {
      if (candidate != null) {
        if (candidate.IsShortListed == true) {
          this.WishListCandidateId = candidate.Id;
          localStorage.setItem('CandidateIdIteration', "0");
          var element = document.getElementById("WishListModalId");

          if (IsNotNull(element)) {
            element.click();
          }
        } else {
          if (localStorage.getItem(LoginTypeKeys.ShortlistId) == "0" || IsNullOrEmpty(localStorage.getItem(LoginTypeKeys.ShortlistId))) {
            candidate.IsShortListed = false;
            this.WishListCandidateId = candidate.Id;
            localStorage.setItem('CandidateIdIteration', "0");
            var element = document.getElementById("WishListModalId");

            if (IsNotNull(element)) {
              element.click();
            }
          } else {
            if (IsNotZero(candidate.Id)) {
              let empshortlistdetail = new CVShortListDetail();
              empshortlistdetail.ShortListId = Number(localStorage.getItem(LoginTypeKeys.ShortlistId));
              empshortlistdetail.UserId = this.contextService.LoggedInUserId;
              empshortlistdetail.JobId = candidate.Id;
              empshortlistdetail.AddedOn = new Date();

              AddEmployerShortlistDetail(this.httpClient, empshortlistdetail)
                .subscribe(result => {
                  candidate.IsShortListed = true;
                  this.toasterService.Success("Saved to " + localStorage.getItem(LoginTypeKeys.ShortlistName) + " shortlist.");
                });
            }
          }
        }
      }
    } else {
      localStorage.setItem(LoginTypeKeys.ShortlistCVSignInRedirection, "true");
      this.contextService.SignInRedirect(true, this.Navigations.CvSearch + "/" + candidate.Id);
    }
  }


  ShowCandidatePopup(candidate: Candidate): void {
    let selection = window.getSelection();

    if (IsNotNull(selection) && IsNotNull(selection.focusNode) && selection.focusNode.textContent == "Candidate") {
      this.ShowCandidateRequestPopup = true;
      this.Candidate = candidate;
      this.CandidateTitle = "Candidate " + [candidate.Id];

      let element = document.getElementById("showcandidatePopup");

      if (IsNotNull(element)) {
        element.click();
        this.OnShowCandidateRequestPopup.emit(candidate);
      }
    } else {
      this.ShowCandidateRequestPopup = false;
    }
  }

  ShowCandidateRequestPopupEvent(status: boolean): void {
    this.ShowCandidateRequestPopup = false;
  }

  RemoveCandidateFromShortist(shortListedDetailId: number): void {
    if (IsNotZeroNumber(shortListedDetailId)) {
      this.OnRemoveShortlistDetailId.emit(shortListedDetailId);
    } else {
      this.toasterService.ServerError();
    }
  }

  OnCandidateDetailsDisplayClick(candidateId: number): void {
    this.CandidateDetailsDisplay.emit(candidateId);
  }

  OnNoteChange(userNotes: UserNote): void {
    if (IsNotNull(userNotes)) {
      if (userNotes.NoteTypeId == NoteTypeEnum.RecruiterNote) {
        this.Candidates.filter(d => d.Id == this.NoteCandidateId)[0].IsNotesExists = userNotes.Notes ? true : false;
      } else if (userNotes.NoteTypeId == NoteTypeEnum.RecruiterFeedback) {
        if (IsNotNullOrEmpty(userNotes.Notes)) {
          this.Candidates.filter(d => d.Id == this.NoteCandidateId)[0].IsFeedBackExists = true;
        } else {
          this.Candidates.filter(d => d.Id == this.NoteCandidateId)[0].IsFeedBackExists = false;
        }
      }
    }

    // this.NoteTypeId = 0;
    // this.NoteCandidateId = 0;
  }

  OpenVideoIntro(candidate: Candidate) {
    let pipe = new CVSearchDetailPipe();

    let url = this.IsScreenedCVsResult == true ? candidate?.Id : pipe.transform(candidate.JobTitle, candidate.FormattedAddress, candidate.Id);

    this.router.navigateByUrl(`${url}?video=1`);
  }

  HideCVClick(candidate: Candidate) {
    this.OnHideCV.emit(candidate);
  }

  UnhideCVClick(candidate: HiddenCv) {
    this.OnUnhideCV.emit(candidate);
  }

  ExpnadIndustries(e: any, candidates: Candidate): void {
    if (e == "skill") {
      candidates.IsExpandSkill = !candidates.IsExpandSkill;
    } else if (e == "ind") {
      candidates.IsExpandInd = !candidates.IsExpandInd;
    } else if (e == "degree") {
      candidates.IsExpandDegree = !candidates.IsExpandDegree;
    } else if (e == "jobtitle") {
      candidates.IsExpandJobTitles = !candidates.IsExpandJobTitles;
    }
  }

  IsPageEven(): void {
        if (IsNotZeroNumber(this.CurrentPageNumber)) {
      this.IsPageEvenNumber = this.CurrentPageNumber % 2 == 0 ? true : false;

      if (this.contextService.IsBrowser) {
        if (IsTrue(this.IsPageEvenNumber)) {
          // Page Number Even
          if (IsNotNullOrEmpty(this.CurrentVisitedPage) && this.CurrentVisitedPage == "CV-Search") {
            this.CandidateCardsUTMSource = "https://www.apply4u.co.uk/search-cv?utm_source=cvsearch&utm_medium=cvsearchcard&utm_term=Register&utm_content=searching%20&utm_campaign=CVsearchresultcardRR";
            this.ContactUsCardUTMSource = "https://www.apply4u.co.uk/contact-us?utm_source=cvsearchpartners&utm_medium=cvsearchpartnersbanner&utm_term=Contact-Us&utm_content=searching&utm_campaign=CVSpartnersresultbannerRR";
            this.CandidateCardAltText = "Unlimited free ads";
          } else if (IsNotNullOrEmpty(this.CurrentVisitedPage) && this.CurrentVisitedPage == "CV-Near-Me") {
            this.CandidateCardsUTMSource = "https://www.apply4u.co.uk/search-cv?utm_source=cvsnearme&utm_medium=cvsnearmecard&utm_term=Register&utm_content=searching%20&utm_campaign=CVsnearmecardRR";
            this.ContactUsCardUTMSource = "https://www.apply4u.co.uk/contact-us?utm_source=cvsnearmepartners&utm_medium= cvsnearmepartnerscard&utm_term=Contact-Us&utm_content=searching&utm_campaign=CNMresultpartnerscardRR";
            this.CandidateCardAltText = "Unlimited free ads";
          }
        } else if (IsFalse(this.IsPageEvenNumber)) {
          // Page Number Odd
                    if (IsNotNullOrEmpty(this.CurrentVisitedPage) && this.CurrentVisitedPage == "CV-Search") {
            this.CandidateCardsUTMSource = "https://www.apply4u.co.uk/search-cv?utm_source=cvsearch&utm_medium=cvsearchcard&utm_term=Register&utm_content=searching%20&utm_campaign=CVsearchresultcardRR";
            this.ContactUsCardUTMSource = "https://www.apply4u.co.uk/contact-us?utm_source=cvsearchpartners&utm_medium=cvsearchpartnersbanner&utm_term=Contact-Us&utm_content=searching&utm_campaign=CVSpartnersresultbannerRR";
            this.CandidateCardAltText = "The only job site Apply4U";
          } else if (IsNotNullOrEmpty(this.CurrentVisitedPage) && this.CurrentVisitedPage == "CV-Near-Me") {
            this.CandidateCardsUTMSource = "https://www.apply4u.co.uk/search-cv?utm_source=cvsnearme&utm_medium=cvsnearmecard&utm_term=Register&utm_content=searching%20&utm_campaign=CVsnearmecardRR";
            this.ContactUsCardUTMSource = "https://www.apply4u.co.uk/contact-us?utm_source=cvsnearmepartners&utm_medium= cvsnearmepartnerscard&utm_term=Contact-Us&utm_content=searching&utm_campaign=CNMresultpartnerscardRR";
            this.CandidateCardAltText = "The only job site Apply4U";
          }
        }
      }
    }
  }

  HideBanner(){
    this.ShowBanner = false;
  }
  
  OnClickSeeMoreExperties(candidateId: number, candidate: Candidate) {
    if (IsNotZeroNumber(candidateId)) {
      candidate.IsExpertiesExpended = !candidate.IsExpertiesExpended;

      if (IsNotNull(candidate)) {
        if (IsAny(candidate.Industries)) {
          if (this.contextService.IsMobileView) {
            if (candidate.Industries.length >= 1) {
              candidate.IsMoreIndustries = true;
            } else {
              candidate.IsMoreIndustries = false;
            }
          } else if (this.contextService.IsDesktopView) {
            if (candidate.Industries.length >= 7) {
              candidate.IsMoreIndustries = true;
            } else {
              candidate.IsMoreIndustries = false;
            }
          }
        }

        if (IsAny(candidate.JobTitles)) {
          if (this.contextService.IsMobileView) {
            if (candidate.JobTitles.length >= 1) {
              candidate.IsMoreJobTitles = true;
            } else {
              candidate.IsMoreJobTitles = false;
            }
          } else if (this.contextService.IsDesktopView) {
            if (candidate.JobTitles.length >= 7) {
              candidate.IsMoreJobTitles = true;
            } else {
              candidate.IsMoreJobTitles = false;
            }
          }
        }

        if (IsAny(candidate.Skills)) {
          if (this.contextService.IsMobileView) {
            if (candidate.Skills.length >= 1) {
              candidate.IsMoreSkills = true;
            } else {
              candidate.IsMoreSkills = false;
            }
          } else if (this.contextService.IsDesktopView) {
            if (candidate.Skills.length >= 7) {
              candidate.IsMoreSkills = true;
            } else {
              candidate.IsMoreSkills = false;
            }
          }
        }

        if (IsAny(candidate.Degrees)) {
          if (this.contextService.IsMobileView) {
            if (candidate.Degrees.length >= 1) {
              candidate.IsMoreDegrees = true;
            } else {
              candidate.IsMoreDegrees = false;
            }
          } else if (this.contextService.IsDesktopView) {
            if (candidate.Degrees.length >= 7) {
              candidate.IsMoreDegrees = true;
            } else {
              candidate.IsMoreDegrees = false;
            }
          }
        }
      }
    }
  }

  OnClickScreenedCandidates():void {
    if(IsTrue(this.IsHomePageScreenedCandidate)){
      this.localStorage.SetItem(LoginTypeKeys.RecruiterHomeFocusType,'Screened Candidates');
      this.localStorage.SetItem(LoginTypeKeys.CVSearchOptionId, "HomePageScreenedCandidateId");
    }
  }

  OnClickNotesIcon(noteTypeId: number, noteCandidateId: number, noteForTypeId: number, isShortlisted: boolean): void {
    if (this.contextService.IsUserLoggedIn) {
      if (IsNotZero(noteTypeId)) {
        this.NoteTypeId = noteTypeId;
      }

      if (IsNotZero(noteCandidateId)) {
        this.NoteCandidateId = noteCandidateId;
      }

      if (IsNotZero(noteForTypeId)) {
        this.NoteForTypeId = noteForTypeId;
      }
    } else {
      this.toasterService.LoginMessageError();
    }
  }
  
}
