<div class="row m-0 my-3">
  <div class="col-md-4 p-0 MainCanvas mb-md-0 mb-3">
    <div class="row m-0">
      <div class=" col-md-12 InboxMsg">
          Inbox <span *ngIf="ContactNotifications">{{ContactNotifications}} New</span>
      </div>
      <div class=" col-md-12 ChatSearch">
          <i class="fa fa-search"></i>
          <input class="form-control" placeholder="Search here" type="text" [(ngModel)]="SearchFilter" (keyup)="FilterSearch($event)">
      </div>
      <!--Tabs-->
      <div class=" col-md-12 p-0">
          <ul class="nav nav-pills TabsWrapper" id="pills-tab" role="tablist">
              <li class="nav-item" role="presentation">
                <a class="nav-link active" id="Chat-tab" data-toggle="pill" href="#Chat" #chatClick role="tab" aria-controls="pills-home" aria-selected="true"><i class="fa fa-comment-alt"></i> Chat</a>
              </li>
              <li class="nav-item" role="presentation">
                <a class="nav-link" id="Contacts-tab" data-toggle="pill" id="contactsTab" href="#Contacts" #contactsClick role="tab" aria-controls="pills-profile" aria-selected="false"><i class="fa fa-address-card"></i> Contacts</a>
              </li>
          </ul>
      </div>
    
        <div class="tab-content col-md-12 p-0" id="pills-tabContent">
    
          <!--Chat List-->
          <div class="tab-pane fade show active" id="Chat" role="tabpanel" aria-labelledby="Chat-tab">
            <div class="ChatWrapper">
              <div *ngIf="IsContactsLoaded && UserChatConnections.length == 0" class=" col-md-12 p-0 py-2 start-conversation">
                <a (click)="GoToContactsTab()">Go to contacts tab to start a conversation!</a>
              </div>
            <div class="row m-0" *ngFor="let requests of UserChatConnections;">
              <div class=" col-md-12 Chat" (click)="OnUserClick(requests)" id="{{requests.UserId}}id">
                <div style="height: 65px;">
                <img *ngIf="requests && requests.UserProfile && requests.UserProfile.ProfileImagePath" src="{{requests?.UserProfile.ProfileImagePath}}" alt="User Image">
                <img *ngIf="requests && requests.UserProfile && !requests.UserProfile.ProfileImagePath" src="assets/images/preview/default-staff.png" alt="User Image">
                <div class="h-100 d-flex flex-column justify-content-around">
                  <h5 class="name">{{requests?.UserProfile?.FirstName}} {{requests?.UserProfile?.LastName}} 
                    <span>
                      <p class="time" *ngIf="requests?.NotificationTime" title="{{requests?.NotificationTime | localDateTime | date:'medium'}}"> {{requests?.NotificationTime | localDateTime | timeAgo}}</p> 
                    <span class="badge badge-secondary" *ngIf="requests?.TotalMessagesNotification > 0" title="New messages">{{requests?.TotalMessagesNotification}}</span>
                    </span>
                  </h5>
                  <h6 *ngIf="requests?.CurrentJob?.CompanyName && !requests?.SystemUser?.Company" ><i class="fa fa-solid fa-sitemap"></i><span > {{requests?.CurrentJob?.CompanyName}}</span></h6>
                  <h6 *ngIf="!!requests?.SystemUser?.Company" ><i class="fa fa-solid fa-sitemap"></i><span > {{requests?.SystemUser?.Company?.CompanyName}}</span></h6>
                </div>
                </div>
              </div>
              
            </div>
          </div>
          </div>
          <!--Chat List-->
    
          <!--Contact List-->
          <div class="tab-pane fade" id="Contacts" role="tabpanel" aria-labelledby="Contacts-tab">
            <div class="ChatWrapper">
              <div class="row m-0" *ngFor="let request of UserConnections;">
                <div class=" col-md-12 Chat" (click)="OnUserClick(request)" id="{{request.UserId}}id">
                    <div>
                      <img *ngIf="request && request.UserProfile && request.UserProfile?.ProfileImagePath" src="{{request?.UserProfile.ProfileImagePath}}" alt="User Image">
                    <img *ngIf="request && request.UserProfile && !request.UserProfile?.ProfileImagePath" src="assets/images/preview/default-staff.png" alt="User Image">
                    <h5 class="name">{{request?.UserProfile?.FirstName}} {{request?.UserProfile?.LastName}} 
                      <span>
                        <p class="time" *ngIf="request?.NotificationTime" title="{{request?.NotificationTime | localDateTime | date:'medium'}}"> {{request?.NotificationTime | localDateTime | timeAgo}}</p> 
                      <span class="badge badge-secondary" *ngIf="request?.TotalMessagesNotification > 0" title="New messages">{{request?.TotalMessagesNotification}}</span>
                      </span>
                    </h5>
                    
                    <h6 *ngIf="request?.CurrentJob?.CompanyName && !request?.SystemUser?.Company" ><i class="fa fa-solid fa-sitemap"></i><span > {{request?.CurrentJob?.CompanyName}}</span></h6>
                  <h6 *ngIf="!!request?.SystemUser?.Company" ><i class="fa fa-solid fa-sitemap"></i><span > {{request?.SystemUser?.Company?.CompanyName}}</span></h6>
                    </div>
                </div>
              </div>
              <div class="row m-0">
                <div *ngIf="UserConnections.length == 0" class=" col-md-12 Chat" >
                  Oops! It seems that you haven't made any connections yet!
                </div>
                <div class=" col-md-12 text-center py-3">
                  <a class="btn btn-primary" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="/my-networks/connections" >Manage Connections</a>
                </div>
              </div>
            </div>
          </div>
          <!--Contact List-->
    
        </div>
      <!--Tabs-->
    </div>
  </div>
  <div class="col-md-8 p-0">
    <div class="row m-0 MainChat ml-md-3" *ngIf="ReceiverId && UserConnection">
      <div class=" col-md-12 ChatHeader">
            <img src="assets/images/preview/default-staff.png" alt="User Image" *ngIf="!UserConnection?.UserProfile?.ProfileImagePath">
        <img src="{{UserConnection?.UserProfile.ProfileImagePath}}" alt="User Image" *ngIf="UserConnection?.UserProfile?.ProfileImagePath">
        <h1 class="mb-0"><a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="/users/{{FormattedUrlToRedirect }}">{{UserConnection?.UserProfile?.FirstName}} {{UserConnection?.UserProfile?.LastName}}</a>
          <span>
            <i class="fa fa-ellipsis-h" data-toggle="dropdown" aria-expanded="false" role="button"></i>
            <div class="dropdown-menu">
              <div role="button" (click)="navigateToUserCompanyProfile()"><i class="fas fa-eye"></i> View Profile </div>
              <div role="button" *ngIf="UserConnection?.StatusId != 4"  (click)="BlockConnectedUser()"><i class="fas fa-ban"></i> Block </div>
              <div role="button" *ngIf="UserConnection?.StatusId == 4"  (click)="UnblockConnectedUser()"><i class="fas fa-ban"></i> Unblock </div>
            </div>
          </span>
        </h1>
        
        <h6 *ngIf="UserConnection?.CurrentJob?.CompanyName && !UserConnection?.SystemUser?.Company" ><i class="fa fa-solid fa-sitemap"></i><span > {{UserConnection?.CurrentJob?.CompanyName}}</span></h6>
                  <h6 *ngIf="!!UserConnection?.SystemUser?.Company" ><i class="fa fa-solid fa-sitemap"></i><span > {{UserConnection?.SystemUser?.Company?.CompanyName}}</span></h6>
      </div>
      <div class=" col-md-12 ChatWindow mb-2" #scrollMe>
        <div class="mt-2 text-center" *ngIf="IsLoadMoreMessages == true">
          <a class="previous-messages" (click)="GetPreviousMessages()"><i class="fas fa-redo-alt"></i> Load previous messages</a>
        </div>
        <div class="mt-2" *ngFor="let userchat of UserChatMessage" [ngClass]="userchat.ReceiverId != LoggedUserId ? 'text-right' : ''">
          <div class="message-sent" *ngIf="userchat.ReceiverId != LoggedUserId">
            <span class="hyperlink-text-white" [innerHtml]="userchat.MessageContent | linky:userchat.MessageContent | safeHtml"></span>
            <span class="metadata">
              <span class="time time-color ml-2 mr-1" *ngIf="userchat.SentOn" title="{{userchat.SentOn | localDateTime | date:'medium'}}">{{userchat.SentOn | localDateTime | timeAgo}}</span>
              <span class="time time-color ml-2 mr-1" *ngIf="!userchat.SentOn">Just now</span>
              <span class="tick" *ngIf="userchat.ViewedOn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" id="msg-dblcheck-ack" x="2063" y="2076">
                  <path
                    d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.88a.32.32 0 0 1-.484.032l-.358-.325a.32.32 0 0 0-.484.032l-.378.48a.418.418 0 0 0 .036.54l1.32 1.267a.32.32 0 0 0 .484-.034l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.88a.32.32 0 0 1-.484.032L1.892 7.77a.366.366 0 0 0-.516.005l-.423.433a.364.364 0 0 0 .006.514l3.255 3.185a.32.32 0 0 0 .484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"
                    fill="#4fc3f7" />
                </svg>
              </span>
              <span class="tick" *ngIf="!userchat.ViewedOn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" id="msg-dblcheck-ack" x="2063" y="2076">
                  <path
                    d="M15.01 3.316l-.478-.372a.365.365 0 0 0-.51.063L8.666 9.88a.32.32 0 0 1-.484.032l-.358-.325a.32.32 0 0 0-.484.032l-.378.48a.418.418 0 0 0 .036.54l1.32 1.267a.32.32 0 0 0 .484-.034l6.272-8.048a.366.366 0 0 0-.064-.512zm-4.1 0l-.478-.372a.365.365 0 0 0-.51.063L4.566 9.88a.32.32 0 0 1-.484.032L1.892 7.77a.366.366 0 0 0-.516.005l-.423.433a.364.364 0 0 0 .006.514l3.255 3.185a.32.32 0 0 0 .484-.033l6.272-8.048a.365.365 0 0 0-.063-.51z"
                    fill="#fff" />
                </svg>
              </span>
            </span>
            <i *ngIf="userchat.Id > 0" role="button" class="fa fa-ellipsis-h ml-1" data-toggle="dropdown" aria-expanded="false"></i>
            <div class="dropdown-menu">
              <div role="button" title="Delete Message" (click)="DeleteMessage(userchat)">
                <i class="far fa-trash-alt"></i> Delete
              </div>
              <div role="button" title="Edit Message" (click)="EditMessage(userchat)">
                <i class="fa fa-pencil-alt"></i> Edit
              </div>
            </div>
          </div>
    
          <span *ngIf="userchat.ReceiverId == LoggedUserId">
            <img class="profile-image chat-image" src="{{ReceiverImageSrc}}" *ngIf="ReceiverImageSrc"
                alt="Profile img">
              <img class="profile-image chat-image" src="assets/images/preview/default-staff.png" *ngIf="!ReceiverImageSrc"
                alt="Profile img">
            <span class="message-received" [ngClass]="UserConnection?.StatusId == 3 || UserConnection?.StatusId == 2 ? 'blur-text':''" (click)="MarkAsRead(userchat)">
              {{userchat.MessageContent}}
              <span class="metadata"><span class="time" title="{{userchat.SentOn | localDateTime | date:'medium'}}">{{userchat.SentOn | localDateTime | timeAgo}}</span></span>
            </span>
          </span>
    
        </div>
    
        <div class="cookie-disclaimer" *ngIf="UserConnection?.StatusId == 3">
          <div class="text-center">
            <p>You have a pending connection request from this user</p>
            <button type="button" class="btn btn-success br5 mr-1" (click)="OnConfirmClickHandler(UserConnection)">Accept</button>
             <button type="button" class="btn btn-danger br5 ml-2" (click)="OnRejectClickHandler(UserConnection)">Decline</button>
          </div>
        </div>
      </div>
      <div class=" col-md-12 ChatFooter">
        <div class="row m-0 align-items-center">
          <div class="col-md col-9 p-0" *ngIf="UserConnection?.CanUserSendMessage && UserConnection.StatusId != 4">
            <textarea [disabled]="txtboxDisabled" [(ngModel)]="UserMessage.MessageContent" (keyup.enter)="SendMessage()" class="form-control" id="exampleFormControlTextarea1" rows="2" placeholder="Type your message ..."></textarea>
          </div>
          <!-- Button section for desktop -->
        <div class="col-md-auto p-0 d-none d-md-block MobiltBtnFix mt-md-0 mt-2" *ngIf="UserConnection?.CanUserSendMessage && UserConnection.StatusId != 4">
          <button *ngIf="UserMessage?.Id > 0" (click)="NewMessage()" class="btn btn-secondary ml-2"><i class="fa fa-times"></i> Cancel</button>
          <button (click)="SendMessage()" type="submit" class="btn btn-primary ml-2"><i class="fa fa-paper-plane"></i></button>
      </div>

      <!-- Button section for mobile -->
      <div class="col-2 p-0 d-block d-md-none MobiltBtnFix mt-md-0 mt-2" *ngIf="UserConnection?.CanUserSendMessage && UserConnection.StatusId != 4">
          <button *ngIf="UserMessage?.Id > 0" (click)="NewMessage()" class="btn btn-secondary ml-2 w-100"><i class="fa fa-times"></i></button>
          <button (click)="SendMessage()" type="submit" class="btn btn-primary ml-2 w-100"><i class="fa fa-paper-plane"></i></button>
      </div>
          <div *ngIf="!UserConnection.CanUserSendMessage && UserConnection.StatusId != 4">Oops! you are blocked by this connection.</div>
          <div role="button" *ngIf="UserConnection?.StatusId == 4"  (click)="UnblockConnectedUser()"> <i class="fas fa-ban"></i> Unblock this user to send messages.</div>
        </div>
      </div>
    </div>

    <div class="MainChat ml-md-3" *ngIf="!ReceiverId && NoConnection == false && IsContactsLoaded && !UserChatConnections">
      <div class="ChatHeader">
        <h1 class="text-center">Welcome, {{LoggedInUserFirstName}} </h1>
            <div class="text-center">
              <img *ngIf="UserImage !== undefined && UserImage != null && UserImage != ''" class="profile-image float-none" src="{{UserImage}}" alt="Profile img" title="Profile img">
              <img *ngIf="UserImage === undefined || UserImage == null || UserImage == ''" class="profile-image float-none" src="assets/images/preview/default-staff.png" alt="Profile img" title="Profile img">
            </div>
      </div>
      <div class="text-center ChatArea mt-3">
        
      </div>
    </div>

    <div class="MainChat ml-md-3" *ngIf="!ReceiverId && NoConnection == true && IsContactsLoaded">
      <div class="ChatHeader">
        <h1 class="text-center">Welcome, {{LoggedInUserFirstName}} </h1>
            <div class="text-center">
              <img *ngIf="UserImage !== undefined && UserImage != null && UserImage != ''" class="profile-image float-none" src="{{UserImage}}" alt="Profile img" title="Profile img">
              <img *ngIf="UserImage === undefined || UserImage == null || UserImage == ''" class="profile-image float-none" src="assets/images/preview/default-staff.png" alt="Profile img" title="Profile img">
            </div>
      </div>
      <div class="text-center ChatArea mt-3">
        <h1>To be able to message a user, you must first connect with him </h1> 
        <div>
          <a class="btn btn-primary" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="/my-networks/suggestions" >See Your Suggested Connections</a>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="col-lg-3 col-md-12 p-0">
        <div class="row m-0 MainCanvas">
            <div class=" col-md-12 text-right ProfileSetting">
              <a href=""><i class="fa fa-cog"></i></a>
            </div>
            <div class=" col-md-12 text-center ProfileCard">
              <img src="assets/images/chat-messages/user2x.png" />
              <h2>Olivia Holt</h2>
              <h3>About Olivia Holt</h3>
              <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>
              <p>It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>
              <button type="submit" class="btn btn-primary"><i class="fa fa-user-tie"></i> View Profile</button>
            </div>
        </div>
    </div> -->
</div>
