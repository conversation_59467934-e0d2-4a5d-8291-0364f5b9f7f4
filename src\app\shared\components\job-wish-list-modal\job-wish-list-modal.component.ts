import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChang<PERSON>, ChangeDetectorRef } from "@angular/core";
import { UserJobWishList } from "@apply4u/models/userjobwishlist";
import { UserJobWishListDetail } from "@apply4u/models/userjobwishlistdetail";
import { ContextService } from "@apply4u/services/context-service";
import { LoadingService } from "@apply4u/services/loading.service";
import { LocalstorageService } from "@apply4u/services/localstorage.service";
import { ToasterMessageService } from "@apply4u/services/toaster-message.service";
import { UserjobwishlistService } from "@apply4u/services/userjobwishlist.service";
import { ConfirmationService } from '@apply4u/shared/components/confirmation-modal/confirmation.service';
import { LoginTypeKeys } from "@apply4u/shared/constant/Local-storage-keys/login-type-keys";
import { ConfirmationMessages } from "@apply4u/shared/constant/messages/confirmation-messages";
import { IsAny, IsNotAny, IsNotZero, IsNullOrEmpty } from "@apply4u/shared/helpers/common/type-safe-helpers";

@Component({
  selector: 'job-wish-list-modal',
  templateUrl: './job-wish-list-modal.component.html',
  styleUrls: ['./job-wish-list-modal.component.css']
})
export class JobWishListModalComponent implements OnInit, OnChanges {
  @Input() JobId: number;
  @Output() WishListChange: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() WishListDelete: EventEmitter<number> = new EventEmitter<number>();
  ListSelectedCount: number = 0;
  JobWishLists: UserJobWishList[] = [];
  NewWishlistName: string;
  IsLoading: boolean;
  WishListId: number;
  LoadPopUp: boolean = true;
  DefaultCheckBox:boolean = false;
  InfoAboutDefault: string = "By marking this shortlist as default next time you click (Heart Icon) the record will be automatically added into this Default list. This will continue until you come back to this page and remove the default selection or mark a different list as default."

  constructor(private contextService: ContextService,
    private toasterService: ToasterMessageService,
    private userJobWishListService: UserjobwishlistService,
    private loadingService: LoadingService,
    private confirmService: ConfirmationService,
    private localStorage: LocalstorageService,
    private cdRef: ChangeDetectorRef) {
  }

  ngOnInit(): void {
    localStorage.setItem("DefaultListPopUpOpen", localStorage.getItem("DefaultListPopUpOpen"));
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    if (IsNotZero(changes['JobId'].currentValue)) {
      this.LoadWishLists(changes['JobId'].currentValue);
    }
  }

  LoadWishLists(jobId: number): void {
    this.IsLoading = true;
    this.JobWishLists = [];
    this.userJobWishListService.GetUserJobWishlists(this.contextService.LoggedInUserId, jobId).subscribe(result => {
      if (result.find(x => x.IsDefault == false)) {
        this.JobWishLists = result;
        this.IsLoading = false;
        this.cdRef.detectChanges();
        var element = document.getElementById("JobWishListModalS");
        element.click();
      } else if (!IsAny(result)) {
        var element = document.getElementById("JobWishListModalS");
        element.click();
      } else {
        this.EmitWishListChanged();
      }
    });
  }

  OnJobWishListChange(wishListId: number): void {
    this.WishListId = wishListId;
    var count = Number(localStorage.getItem(wishListId.toString()));
    var wishlistID = Number(localStorage.getItem("WishListId"));
    var IsDefaultListPopUpOpen = Number(localStorage.getItem("DefaultListPopUpOpen"));
    let wishListIndex = this.JobWishLists.findIndex(d => d.Id == wishListId);
    this.loadingService.Show();
    this.loadingService.Hide();

    if (wishlistID == wishListId && count >= 2 && (IsDefaultListPopUpOpen == 0 || Number.isNaN(IsDefaultListPopUpOpen))) {      
      var element = document.getElementById("JobWishListModalS");
      element.click();
      element = null;
      element = document.getElementById("PopUpModalS");
      element.click();
      this.StopDefaultListPopUp();
    } else {      
      localStorage.setItem("WishListId", wishListId.toString());
      const checked = document.querySelector("#chkJobWishList_" + wishListId + ":checked") !== null;

      if (checked) {
        localStorage.setItem(wishListId.toString(), (count + 1).toString());
      }

      this.AddToShortlist(this.WishListId);
    }
  }

  AddListToDefault() {
    //this.AddToShortlist(this.WishListId);
    this.OnJobWishDefaultListChange(this.WishListId);
  }

  AddToShortlist(wishListId: number) {
    let wishListIndex = this.JobWishLists.findIndex(d => d.Id == wishListId);
    if (IsNotZero(wishListId) && IsAny(this.JobWishLists)) {


      if (this.JobWishLists[wishListIndex].IsJobExistInList) {
        this.userJobWishListService.DeleteUserJobWishlistDetail(wishListId, this.contextService.LoggedInUserId, this.JobId).subscribe(result => {
          this.JobWishLists[wishListIndex].IsJobExistInList = false;
          this.JobWishLists[wishListIndex].TotalJobs -= 1;
          this.EmitWishListChanged();
          this.toasterService.Success("Removed from " + this.JobWishLists[wishListIndex].WishListName + " shortlist.");
        });
      } else {        
        let userJobWishListDetail = new UserJobWishListDetail();
        userJobWishListDetail.WishListId = wishListId;
        userJobWishListDetail.JobId = this.JobId;

        this.userJobWishListService.AddUserJobWishlistDetail(userJobWishListDetail).subscribe(result => {
          this.JobWishLists[wishListIndex].IsJobExistInList = true;
          this.JobWishLists[wishListIndex].TotalJobs += 1;
          this.EmitWishListChanged();
          this.toasterService.Success("Saved to " + this.JobWishLists[wishListIndex].WishListName + " shortlist.");
        });
      }
    }
  }

  OnJobWishDefaultListChange(defaultListId: number): void {
    if (IsNotZero(defaultListId) && IsAny(this.JobWishLists)) {

      let userJobWishListDetail = new UserJobWishListDetail();
      userJobWishListDetail.WishListId = defaultListId;
      userJobWishListDetail.LoggedInUserId = this.contextService.LoggedInUserId;

      this.userJobWishListService.AddUserJobWishlistToDefaultList(userJobWishListDetail)
        .subscribe(result => {
          if (result) {
            this.DefaultCheckBox = true;
            // this.JobWishLists.forEach(x => x.IsDefault = false);
            let wishListIndex = this.JobWishLists.findIndex(d => d.Id == defaultListId);
            // this.JobWishLists[wishListIndex].IsDefault = true;
            localStorage.setItem(LoginTypeKeys.JobWishlistName, this.JobWishLists[wishListIndex].WishListName);
            // localStorage.setItem(LoginTypeKeys.JobWishlistId, defaultListId.toString());
            this.localStorage.SetItem(LoginTypeKeys.DefaultJobWishlistId,defaultListId.toString());
            this.toasterService.Success("Added list to default.");
          } else {
            // event.target.checked = false;
            this.DefaultCheckBox = false;
            localStorage.setItem(LoginTypeKeys.JobWishlistName, "");
            localStorage.setItem(LoginTypeKeys.JobWishlistId, "0");
            this.toasterService.Success("Removed from default list.")
          }
        });
    }
  }

  StopDefaultListPopUp() {
    localStorage.setItem("DefaultListPopUpOpen", "1");
    this.AddToShortlist(this.WishListId);
  }

  EmitWishListChanged(): void {
    this.WishListChange.emit(IsAny(this.JobWishLists.filter(d => d.IsJobExistInList)));
    this.userJobWishListService.JobWishlistChanged.emit(this.JobWishLists);
  }

  OnWishListRemove(wishListId: number): void {
    this.confirmService.SetMessage(ConfirmationMessages.ConfirmationOnRemoveWishlist).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.userJobWishListService.DeleteUserJobWishlist(wishListId).subscribe(result => {
          if (IsAny(this.JobWishLists)) {
            let deletedList = this.JobWishLists.filter(d => d.Id == wishListId);

            if (IsAny(deletedList)) {
              if (IsAny(deletedList[0].UserJobWishListDetails)) {
                let otherIndexInList = deletedList[0].UserJobWishListDetails.filter((ind => ind.WishListId == wishListId));

                if (IsAny(otherIndexInList)) {
                  otherIndexInList.forEach(element => {
                    this.WishListDelete.emit(element.JobId);
                  });
                }
              }
            }

            this.JobWishLists = this.JobWishLists.filter(d => d.Id != wishListId);

            if (IsAny(this.JobWishLists)) {
              let indexInOtherList = this.JobWishLists.findIndex((ind => ind.IsJobExistInList &&
                IsAny(ind.UserJobWishListDetails) && ind.UserJobWishListDetails.findIndex(find => find.JobId == this.JobId)));

              if (indexInOtherList == -1) {
                this.EmitWishListChanged();
              }
            } else {
              this.EmitWishListChanged();
            }

            this.toasterService.Success("Shortlist removed.");
          }
        });
      }
    });
  }

  CreateNewWishList(): void {
    if (IsNotAny(this.JobWishLists)) {
      this.JobWishLists = [];
    }

    if (IsNullOrEmpty(this.NewWishlistName)) {
      this.toasterService.Error("List name is empty.");
    } else if (IsAny(this.JobWishLists.filter(d => d.WishListName.toLowerCase().trim() == this.NewWishlistName.toLowerCase().trim()))) {
      this.toasterService.Error("List already exists with this name.");
    } else {
      let newWishlist = new UserJobWishList();
      newWishlist.WishListName = this.NewWishlistName;
      newWishlist.UserId = this.contextService.LoggedInUserId;
      newWishlist.IsJobExistInList = false;

      this.userJobWishListService.AddUserJobWishlist(newWishlist).subscribe(result => {
        newWishlist.Id = result;
        newWishlist.TotalJobs = 0;
        this.JobWishLists.push(newWishlist);
        this.NewWishlistName = "";
        this.toasterService.Success("New shortlist created.");
        this.cdRef.detectChanges();
      });
    }
  }
}
