<!--Blog Banner-->
<div class="row m-0 hide-header-mobile">
  <div class="col-md-12 p-0 BlogHeader">
    <h1 *ngIf="SearchResult?.Title" class="truncate-text">{{SearchResult?.Title}}</h1>
    <img src="assets/images/Groups/blog-header.jpg" />
  </div>
</div>
<!--Blog Banner-->

<!--Body Content-->
<section class="PageBg">
  <div class="container">
    <div class="col-12">
      <breadcrumb-seo [BreadCrumbSchema]="BreadCrumbSchema"></breadcrumb-seo>
    </div>
    <div class="row m-0">
      <!--Blog Posts Listing-->
      <div class="col-xl-8 col-md-12">
        <div class="row pt-4 pb-4 m-0">
          <div class="col-md-12 BlogDetail">
            <div class="row m-0 pt-3 BackNext" *ngIf="SearchResult && SearchResult?.Page">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 " *ngIf="SearchResult?.Page?.PreviousUrl">
                <a class="btn btn-secondary mb-2" title="{{SearchResult?.Page?.PreviousTitle}}"
                  routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{SearchResult?.Page?.PreviousUrl}}"><i class="fa fa-chevron-left"></i> Back</a>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 text-right" *ngIf="SearchResult?.Page?.NextUrl">
                <a class="btn btn-primary mb-2" title="{{SearchResult?.Page?.NextTitle}}"
                  routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{SearchResult?.Page?.NextUrl}}">Next <i class="fa fa-chevron-right"></i></a>
              </div>
            </div>
            <h1 title="{{SearchResult?.Title}}">{{SearchResult?.Title}}</h1>
            <div class="row m-0">
              <div class=" col-md-12 p-0">
                <p class="Date">Posted by: {{SearchResult?.PostedBy}} | <i class="fa fa-calendar-o"></i>
                  {{SearchResult?.CreatedOn | date}}</p>
              </div>
              <div class=" col-md-12 p-0 mb-3">
                <p class="Date cursor"><a (click)="ShareBlogProfile()" data-toggle="modal"
                    data-target="#ShareCompanyProfileModal">
                    <span><i class="fa fa-share-alt"></i> Share</span></a>
                </p>
              </div>
            </div>
            <span [innerHtml]="SearchResult?.BlogContent | safeHtml"></span>
          </div>

          <!--Comments Section-->
          <div class="col-md-12 p-0 BlogDetail">
            <app-blogscomment *ngIf="BlogIdForComment" [blogIdForComment]="BlogIdForComment"></app-blogscomment>
          </div>
          <!--Comments Section-->
            <div class="row">
              <div class="col-md-12">
                <h1 class="mt-3" style="font-size:24px;font-weight:bold;">Want to Know More? <a href="/contact-us" style="color: #14C59C;">Contact Us</a> or
                  <a style="color: #14C59C;"  class="cursor"(click)="OnClickOurPricingPageLink()">Explore our Pricing Plans</a>
                </h1>
              </div>
            </div>      
        </div>
      </div>
      <!--Blog Posts Listing-->

      <!--Blog SideBar-->
      <div class="col-xl-4 col-md-12 p-0">
        <!--Search Post-->
        <div class="col-md-12 p-0">
          <app-search-post [PopularTags]="PopularTags" [SearchTag]="SearchTag"></app-search-post>
        </div>
        <!--Search Post-->

        <!--Popular Posts-->
        <div class="col-md-12 p-0">
          <app-popular-post [PopularPosts]="PopularPosts"></app-popular-post>
        </div>
        <!--Popular Posts-->
        
        <!--Recent Posts-->
        <div class="col-md-12 p-0">
          <app-recent-post [RecentPosts]="RecentPosts"></app-recent-post>
        </div>
        <!--Recent Posts-->

        <!--Categories-->
        <div class="col-md-12 p-0">
          <app-category [Sectors]="Sectors"></app-category>
        </div>
        <!--Categories-->
        
        <!--Tags-->
        <div class="row pb-3 Blogs m-0">
          <div class="col-md-12 BlogCard">
            <div class="card">
              <div class="card-body">
                <h1>Tags</h1>
                <app-tag [PopularTags]="PopularTags" (SearchTermChanged)="OnSearchTermChanged($event)"></app-tag>
              </div>
            </div>
          </div>
        </div>
        <!--Tags-->
      </div>
      <!--Blog SideBar-->
    </div>
  </div>
</section>
<!--Body Content-->

<!-- Share Blogs -->
<span *ngIf="IsBlogShare">
  <app-share-company-modal [PostUrl]="PostURL" [Title]="'Share Blog'"></app-share-company-modal>
</span>
<!-- Share Blogs -->