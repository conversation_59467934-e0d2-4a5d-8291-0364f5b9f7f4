import { Component, ElementRef, OnInit, ViewChild, } from '@angular/core';
import { Router } from '@angular/router';
import { Job } from '@apply4u/models/search/job';
import { ResultsWithOutPagination } from '@apply4u/models/search/results-with-out-pagination';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { ShortlistedJobs } from '@apply4u/models/shorlist/job-shortlist';
import { JobseekerShortlist } from '@apply4u/models/shorlist/shortlist';
import { UserJobShortlist } from '@apply4u/models/shorlist/user-job-shortlist';
import { UserJobWishListDetail } from '@apply4u/models/userjobwishlist';
import { ContextService } from '@apply4u/services/context-service';
import { LoadingService } from '@apply4u/services/loading.service';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { MetaDataService } from '@apply4u/services/meta-data.service';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { JobShortlistService } from '@apply4u/services/shortlist/job-shortlist-service';
import { ShowHideLogsService } from '@apply4u/services/showHideLogs.service';
import { ToasterMessageService } from "@apply4u/services/toaster-message.service";
import { UserjobwishlistService } from '@apply4u/services/userjobwishlist.service';
import { ConfirmationService } from '@apply4u/shared/components/confirmation-modal/confirmation.service';
import { LoginTypeKeys } from '@apply4u/shared/constant/Local-storage-keys/login-type-keys';
import { Alphabetically, BreadCrumbTitleDashBoard, BreadCrumbTitleJobShortList, BreadcrumbsTitleSearchJobs, DatePosted, DefaultPageNumber, DefaultPageSize } from '@apply4u/shared/constant/constant';
import { ManageUserWishlist } from '@apply4u/shared/constant/manage-user-wishlist';
import { AlreadyWishListed_Message } from '@apply4u/shared/constant/messages/common-notifications';
import { ConfirmationMessages } from '@apply4u/shared/constant/messages/confirmation-messages';
import { ManageUserShortlistMessages } from '@apply4u/shared/constant/messages/manage-user-shortlist-messages';
import { DashBoard_RouteUrl, Jobs_RouteUrl, MyJobShortLists_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';
import { IsAny, IsNotAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { environment } from '@environment/environment';
import { error } from 'console';

@Component({
  templateUrl: './job-shortlist.component.html',
  styleUrls: ['./job-shortlist.component.css']
})
export class JobShortlistComponent implements OnInit {
  SelectedShortlistId: number;
  StatusMessage: string = "";
  JobShortlists: UserJobShortlist[];
  UserShortlistedJobs: ResultsWithOutPagination<ShortlistedJobs>;
  IsSearchByNewest: boolean = true;
  IsSearchByAlphabetically: boolean = false;
  SearchBy: string = 'Date';
  UserId: number;
  DropdownPreselected: number = 0;
  PageNumber: number = DefaultPageNumber;
  PageSize: number = DefaultPageSize;
  CreatedShortlistId: number = 0;
  PageHeadingtitle: string = "Job Shortlists";
  HiddenJobId : number = 0;
  TotalRecords: number = null;
  DefaultCheckBox:boolean = false;
  JobShortListName:string="";
  pageTitle:string="Don't get lost in paperwork";
  pageParagraph:string="Effortlessly manage, connect, and apply – all in one place!";
  SelectedShortlistTitle: string;
  BreadCrumbSchema: BreadCrumbs;
  jobList: JobseekerShortlist;
  selectedCheckBox= false;
  @ViewChild('closeBtn') CloseBtn: ElementRef;
    constructor(
    private toasterService: ToasterMessageService,
    private loadingService: LoadingService,
    private confirmService: ConfirmationService,
    private metaService: MetaWrapperService,
    private metadataService: MetaDataService,
    private jobShortlistService: JobShortlistService,
    private contextService: ContextService,
    private localStorage: LocalstorageService,
    private router: Router,
    private showHideLogsService: ShowHideLogsService,
    private userJobWishListService: UserjobwishlistService) {
    this.UserId = this.contextService.LoggedInUserId;
    this.InitSearchResults();
  }

  ngOnInit() {
    this.metaService.SetPageTitle(this.metadataService.ManageUserWishlistComponentTitle);
    this.metaService.UpdateMetaTag('description', this.metadataService.ManageUserWishlistComponentDescription);
    this.GetUserShortlist();
    this.OnContextChange();

    if(this.contextService.IsBrowser) {
      this.PopulateBreadCrumbsDisplayList();
    }

    this.showHideLogsService.NotifyHideJob$.subscribe(isSuccess => {  this.FilterHiddenJobs(isSuccess); });   
      }

  InitSearchResults(): void {
    this.UserShortlistedJobs = new ResultsWithOutPagination<ShortlistedJobs>();
    this.UserShortlistedJobs.Response = [];
    this.UserShortlistedJobs.TotalRecords = 0;
  }

  GetUserShortlist(): void {

    let defaultShortlistId = this.localStorage.GetItem(LoginTypeKeys.DefaultJobWishlistId);
    let shortlistId = this.localStorage.GetItem(LoginTypeKeys.JobWishlistId);

    this.JobShortlists = [];
    this.loadingService.Show();
    this.jobShortlistService.GetUserShortlists(this.UserId).subscribe({
      next: shortlistResult => {
        if (IsAny(shortlistResult)) {
          this.JobShortlists = shortlistResult;
          if (IsNotNull(defaultShortlistId) && defaultShortlistId != 0) {
            this.DropdownPreselected = parseInt(defaultShortlistId);
            this.SelectedShortlistTitle = this.JobShortlists.find(Option => Option.Id === this.DropdownPreselected)?.ListName;
            this.DefaultCheckBox = true;
          } else {
            this.DropdownPreselected = this.JobShortlists[0].Id;
            this.SelectedShortlistTitle = this.JobShortlists[0].ListName;
          }

          //  this.DropdownPreselected = Number(this.localStorage.GetItem(LoginTypeKeys.JobWishlistId)) != undefined || null ? Number(this.localStorage.GetItem(LoginTypeKeys.JobWishlistId)) : 0;

          this.OnShortlistChangeHandler(this.DropdownPreselected, this.PageNumber, this.PageSize, this.SearchBy);
        } else {
          this.DropdownPreselected = 0;
          this.StatusMessage = ManageUserWishlist.OnWishlistNoListCreated;
          if (IsNotNull(defaultShortlistId) && defaultShortlistId != 0) {
            this.DropdownPreselected = parseInt(defaultShortlistId);
            this.DefaultCheckBox = true;
          } else {
            this.DropdownPreselected = shortlistId;
          }
        }        
        this.loadingService.Hide();
      }, error: error => {
        this.loadingService.Hide();
      }
    });
  }

  OnShortlistChanged(event: Event) {
    const selectedValue = +(event.target as HTMLSelectElement).value;
    this.SelectedShortlistTitle = this.JobShortlists.find(Option => Option.Id === selectedValue)?.ListName;
    this.DropdownPreselected = selectedValue;

    if (this.DropdownPreselected === 0) {
      this.StatusMessage = ManageUserWishlist.onWishlistChangeHandlerWishlistMessage;
      this.InitSearchResults();
    } else {
      this.OnShortlistChangeHandler(this.DropdownPreselected, this.PageNumber, this.PageSize, this.SearchBy);
    }
  }

  UpdateShortlist(listId: number, listName: string) {
    if (IsNotNull(listId)) {
      this.loadingService.Show();
      const updatedShortlist: JobseekerShortlist = {
        Id: listId,
        UserId: this.contextService.LoggedInUserId,
        WishListName: listName,
        LastUpdated: undefined
      };
      if (IsNotNullOrEmpty(listName)) {
        this.jobShortlistService.IsJobShortlistExist(this.contextService.LoggedInUserId, listName).subscribe({
          next: isAlreadyExist => {
            if(isAlreadyExist){
              this.loadingService.Hide();
              this.toasterService.Error(AlreadyWishListed_Message);
            } else {
              this.jobShortlistService.UpdateUserShortlist(listId, updatedShortlist).subscribe(
                result => {
                  this.localStorage.SetItem(LoginTypeKeys.JobWishlistName, "");
                  this.toasterService.Success(ManageUserShortlistMessages.OnShortlistUpdatingSuccess, null);
                  this.CloseBtn.nativeElement.click();
                  this.GetUserShortlist();
                  this.loadingService.Hide();
                },  errorResult => {
                  this.loadingService.Hide();
                  this.toasterService.ServerError();
                }
              );
            }
          }, error: error => {
            this.loadingService.Hide();
          }
        });
      }else{
        this.loadingService.Hide();
        this.toasterService.Error('Please provide shortlist name', null);
      }
    }

  }
  

  UpdateJobShortListToDefault(): void {
    let shortlistId = this.DropdownPreselected;
    if (IsNotZero(shortlistId)) {

      let userJobWishListDetail = new UserJobWishListDetail();
      userJobWishListDetail.WishListId = this.DropdownPreselected;
      userJobWishListDetail.LoggedInUserId = this.contextService.LoggedInUserId;

      this.userJobWishListService.AddUserJobWishlistToDefaultList(userJobWishListDetail)
          .subscribe(result => {
            if (result) {
              this.DefaultCheckBox = true;
              this.localStorage.SetItem(LoginTypeKeys.DefaultJobWishlistId,shortlistId.toString());
              if(IsNotNull(this.JobShortlists)){
                this.JobShortListName = this.JobShortlists.find(x=>x.Id==this.DropdownPreselected).ListName;
              }
              this.localStorage.SetItem(LoginTypeKeys.JobWishlistName,this.JobShortListName);
              localStorage.setItem("DefaultListPopUpOpen", "1");
              this.toasterService.Success("Added list to default.");
            }
            else {
             this.DefaultCheckBox = false;
              this.localStorage.SetItem(LoginTypeKeys.DefaultJobWishlistId,"0");
              this.localStorage.SetItem(LoginTypeKeys.JobWishlistName,"");
              localStorage.setItem("DefaultListPopUpOpen", "0");
              this.toasterService.Success("Removed from default list.")
            }
          });
    }
  }

  OnShortlistChangeHandler(eventTargetId: any, pageNumber: number, pageSize: number, sortBy: string): void {
    if (eventTargetId != 0) {
      this.StatusMessage = "";
      this.SelectedShortlistId = parseInt(eventTargetId);
      this.localStorage.SetItem(LoginTypeKeys.JobWishlistId,JSON.stringify(this.SelectedShortlistId));
      this.InitSearchResults();
      this.loadingService.Show();
      //this.DropdownPreselected = Number(this.localStorage.GetItem(LoginTypeKeys.JobWishlistId)) != undefined || null ? Number(this.localStorage.GetItem(LoginTypeKeys.JobWishlistId)) : 0;
      if((Number(this.localStorage.GetItem(LoginTypeKeys.DefaultJobWishlistId)) ?? 0) == Number(eventTargetId) &&this.DropdownPreselected != 0) {
        this.DefaultCheckBox = true;
      } else{
        this.DefaultCheckBox = false;
      }

      //Number(eventTargetId) == Number(localStorage.getItem(LoginTypeKeys.JobWishlistId)) ? this.DefaultCheckBox = true:this.DefaultCheckBox=false;
      this.jobShortlistService.GetUserShortlistDetail(this.UserId, this.SelectedShortlistId, pageNumber, pageSize, sortBy).subscribe({
        next: detailResult => {
            if (IsNotNull(detailResult) && IsAny(detailResult.Response)) {
              this.UserShortlistedJobs = detailResult;
              this.TotalRecords = detailResult.TotalRecords;
            } else {
              this.UserShortlistedJobs.Response = [];
              this.UserShortlistedJobs.TotalRecords = 0;
              this.StatusMessage = ManageUserWishlist.onWishlistChangeHandlerSelectedJobMessage;
            }
          this.loadingService.Hide();
        }, error: error => {
          this.loadingService.Hide();
        }
      });
    } else {
      this.InitSearchResults();
      this.StatusMessage = ManageUserWishlist.onWishlistChangeHandlerWishlistMessage;
    }
  }

  OnShortListRemoveHandler(selectedShortlistId: number): void {
    if (selectedShortlistId != 0) {
      this.confirmService.SetTitle('Confirm Deletion.').SetMessage(ConfirmationMessages.ConfirmationOnRemoveWishlist, "Confirm").OpenConfirmationDialogue(IsOk => {
        if (IsOk) {
          this.loadingService.Show();
          this.jobShortlistService.DeleteUserShortlist(this.UserId, selectedShortlistId).subscribe({
            next: result => {
              this.jobShortlistService.GetUserShortlists(this.UserId).subscribe({
                next: lists => {
                  this.JobShortlists = lists;
                  if (IsAny(lists)) {                    
                    this.DropdownPreselected = this.JobShortlists[0].Id;
                    this.OnShortlistChangeHandler(this.JobShortlists[0].Id, this.PageNumber, this.PageSize, this.SearchBy);
                    // this.localStorage.SetItem(LoginTypeKeys.JobWishlistId,this.JobShortlists[0].Id.toString());
                    if (lists.some(x => x.Id != Number(localStorage.getItem(LoginTypeKeys.DefaultJobWishlistId)))) {
                      localStorage.setItem(LoginTypeKeys.DefaultJobWishlistId, "0");
                      this.localStorage.SetItem(LoginTypeKeys.JobWishlistName, "");
                      localStorage.setItem("DefaultListPopUpOpen", "0");
                      this.DefaultCheckBox = false;
                    }
                  } else {
                    this.UserShortlistedJobs.Response = [];
                    this.UserShortlistedJobs.TotalRecords = 0;
                    this.DropdownPreselected = 0;
                    this.StatusMessage = ManageUserWishlist.OnWishlistNoListCreated;
                  }

                  if (lists.length == 0) {
                    localStorage.setItem(LoginTypeKeys.DefaultJobWishlistId, "0");
                    this.localStorage.SetItem(LoginTypeKeys.JobWishlistName, "");
                    localStorage.setItem("DefaultListPopUpOpen", "0");
                    this.DefaultCheckBox = false;
                  }
                }, error: error => {
                  this.loadingService.Hide();
                }
              });

              this.loadingService.Hide();
              // this.GetUserShortlist();
              this.toasterService.Success(ManageUserShortlistMessages.OnShortlistDeletingSuccess, null);
            }, error: error => {
              this.loadingService.Hide();
            }
          });
        }
      });
    }
  }

  RemoveJobFromListHandler(shortlistDetailId: number): void {
    if (IsNotZeroNumber(shortlistDetailId)) {
      this.confirmService.SetTitle('Confirm Deletion.').SetMessage(ConfirmationMessages.ConfirmationOnRemoveWishlist,"Confirm").OpenConfirmationDialogue(IsOk => {
        if (IsOk) {
          this.jobShortlistService.DeleteUserShortlistDetail(this.UserId, shortlistDetailId).subscribe({
            next: deleteResult => {
              let deletedJob = this.UserShortlistedJobs.Response.findIndex(remove => remove.ShortListDetailId == shortlistDetailId);

              this.UserShortlistedJobs.Response.splice[deletedJob];
              this.loadingService.Show();
              this.jobShortlistService.GetUserShortlistDetail(this.UserId, this.SelectedShortlistId, this.PageNumber, this.PageSize, this.SearchBy).subscribe({
                next: detailResult => {
                  if (IsNotNull(detailResult) && IsAny(detailResult.Response)) {
                    this.UserShortlistedJobs = detailResult;
                    let reduceCount = this.JobShortlists.filter(filter => filter.Id == this.DropdownPreselected);

                    if (IsAny(reduceCount)) {
                      if (reduceCount[0].TotalJobs != 0) {
                        reduceCount[0].TotalJobs -= 1;
                      }
                    }

                  } else {
                    let reduceCount = this.JobShortlists.filter(filter => filter.Id == this.DropdownPreselected);

                    if (IsAny(reduceCount)) {
                      if (reduceCount[0].TotalJobs != 0) {
                        reduceCount[0].TotalJobs -= 1;
                      }
                    }

                    this.toasterService.DeletedSuccessfully();
                    this.UserShortlistedJobs.Response = [];
                    this.UserShortlistedJobs.TotalRecords = 0;
                    this.StatusMessage = ManageUserWishlist.onWishlistChangeHandlerSelectedJobMessage;
                  }

                  this.loadingService.Hide();
                }, error: error => {
                  this.loadingService.Hide();
                }
              });
            }
          });
        }
      });
    }
  }

  PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();

    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleDashBoard;
    listItem.item = `${environment.HostName}${DashBoard_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem1 = new ListItem();
    listItem1.position = 2;
    listItem1.name = BreadcrumbsTitleSearchJobs;
    listItem1.item = `${environment.HostName}${Jobs_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem1);

    let listItem2 = new ListItem();
    listItem2.position = 3;
    listItem2.name = BreadCrumbTitleJobShortList;
    listItem2.item = `${environment.HostName}${MyJobShortLists_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);    
  }

  ShortListCreated(createdShortlist: any): void {
    this.CreatedShortlistId = createdShortlist.Id;
    this.GetUserShortlist();
  }

  OnChangeToggleNewestAlphabetically(event): void {
    this.PageNumber = 1;
    this.PageSize = 10;

    if (event.target.innerText == Alphabetically) {
      this.SearchBy = '';
      this.SearchBy = event.target.innerText;
      this.OnShortlistChangeHandler(this.SelectedShortlistId, this.PageNumber, this.PageSize, this.SearchBy);
      this.IsSearchByAlphabetically = true;
    } else {
      this.SearchBy = DatePosted;
      this.OnShortlistChangeHandler(this.SelectedShortlistId, this.PageNumber, this.PageSize, this.SearchBy);
      this.IsSearchByAlphabetically = false;
    }
  }

  OnPageNumberChanged(pageNumber: number): void {
    this.PageNumber = pageNumber;
    this.OnShortlistChangeHandler(this.SelectedShortlistId, this.PageNumber, this.PageSize, this.SearchBy);
  }

  OnPageSizeChanged(pageSize: number): void {
    this.PageSize = pageSize;
    this.OnShortlistChangeHandler(this.SelectedShortlistId, this.PageNumber, this.PageSize, this.SearchBy);
  }

  OnHideJob(job: Job): void {
    this.HiddenJobId = job.Id;
    this.showHideLogsService.SaveHideJobInfo(job);
  }

  //#region Private Methods

  private FilterHiddenJobs(isSuccess: boolean) {
    if (isSuccess) {
      this.UserShortlistedJobs.Response = this.UserShortlistedJobs.Response.filter(x => x.Id != this.HiddenJobId);
      this.UserShortlistedJobs.TotalRecords = this.UserShortlistedJobs.TotalRecords - 1; 

      if(IsAny(this.JobShortlists)){
        if(IsNotZero(this.SelectedShortlistId)){
          let userSelectedList = this.JobShortlists.filter(x => x.Id == this.SelectedShortlistId);
          userSelectedList[0].TotalJobs = userSelectedList[0].TotalJobs -1;
        }
      }
    }

    if (IsNotAny(this.UserShortlistedJobs.Response) && this.UserShortlistedJobs.Response.length == 0) {
      if (IsNotZero(this.TotalRecords) && this.TotalRecords > 10) {
        if (IsNotZero(this.PageNumber) && this.PageNumber > 1) {
          this.PageNumber = this.PageNumber - 1;
          this.OnPageNumberChanged(this.PageNumber);
        } else {
          this.OnPageNumberChanged(this.PageNumber);
        }
      }else {
        this.StatusMessage = ManageUserWishlist.onWishlistChangeHandlerSelectedJobMessage;
      }
    }
  }

  private OnContextChange(): void {
    this.contextService.OnContextChange.subscribe({
      next: isJobSeeker => {
        if (!isJobSeeker) {
          if (this.router.url == '/jobs-shortlist') {
            this.router.navigateByUrl('/cvs-shortlist');
          }
        }
      }
    });
  }

  //#endregion

}
