import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { CVShortListDetail } from '@apply4u/models/cv-wish-list/cv-short-list-detail';
import { Candidate } from '@apply4u/models/search/candidate';
import { ResultsWithOutPagination } from '@apply4u/models/search/results-with-out-pagination';
import { CVShortlist } from '@apply4u/models/shorlist/cv-shortlist';
import { RecruiterShortlist } from '@apply4u/models/shorlist/shortlist';
import { ShortListedCandidate } from '@apply4u/models/shorlist/shortlisted-candidate';
import { HttpClientWrapper } from "@apply4u/services";
import { ContextService } from '@apply4u/services/context-service';
import { EventsService } from '@apply4u/services/events.service';
import { LoadingService } from '@apply4u/services/loading.service';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { MetaDataService } from '@apply4u/services/meta-data.service';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { SavefileService } from '@apply4u/services/savefile.service';
import { CVShortlistService } from '@apply4u/services/shortlist/cv-shortlist.service';
import { ShowHideLogsService } from '@apply4u/services/showHideLogs.service';
import { ToasterMessageService } from "@apply4u/services/toaster-message.service";
import { AddEmployerShortlistToDefaultDetail } from "@apply4u/services/wish-lists/employer-short-list.service";
import { ConfirmationService } from '@apply4u/shared/components/confirmation-modal/confirmation.service';
import { LoginTypeKeys } from '@apply4u/shared/constant/Local-storage-keys/login-type-keys';
import { Manageemployershortlist } from '@apply4u/shared/constant/manageemployershortlist';
import { CommonMessages } from '@apply4u/shared/constant/messages/common-messages';
import { ConfirmationMessages } from '@apply4u/shared/constant/messages/confirmation-messages';
import { ManageUserShortlistMessages } from '@apply4u/shared/constant/messages/manage-user-shortlist-messages';
import { IsAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber, IsNullOrEmpty, IsTrue } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { ConvertBytesBase64StringToBlob } from '@apply4u/shared/helpers/encoding-helper';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { BreadCrumbTitleDashBoard, BreadcrumbsTitleCVsShortlist, BreadcrumbsTitleSearchCVs, DefaultPageNumber, DefaultPageSize } from '@apply4u/shared/constant/constant';
import { environment } from '@environment/environment';
import { CvSearch_RouteUrl, DashBoard_RouteUrl, MyCVShortList_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';
import { AlreadyWishListed_Message } from '@apply4u/shared/constant/messages/common-notifications';
import { CompanyVerificationService } from '@apply4u/services/company-verification.service';

@Component({
  selector: 'app-cvs-shortlist',
  templateUrl: './cvs-shortlist.component.html',
  styleUrls: ['./cvs-shortlist.component.css']
})
export class CvsShortlistComponent implements OnInit {
  SelectedDateFromOnExportAll: Date;
  SelectedDateToOnExportAll: Date;
  NoOFrecords: number = 10;
  ShortlistIdExport: number;
  ShortListedCandidateResult: ResultsWithOutPagination<ShortListedCandidate>;
  UserCVShortLists: CVShortlist[];
  EmployerShortlist: CVShortlist = new CVShortlist();
  EmployerList: RecruiterShortlist = new RecruiterShortlist();
  IsUserIsAdmin: boolean = false;
  StatusMessage: string = "";
  DropdownPreselected: number = 0;
  ShortListName: string = "";
  ShortListTypeId: number = 0;
  UserId: number;
  PageNumber: number = DefaultPageNumber;
  PageSize: number = DefaultPageSize;
  CreatedShortlistId: number = 0;
  PageHeadingTitle: string = "CV Shortlists";
  HiddenCVId: number = 0;
  DefaultCheckBox: boolean = false;
  BreadCrumbSchema: BreadCrumbs;
  EditedShortList: CVShortlist; 
  EditedShortListName: string = "";
  EditedShortListTypeId: number = 0;
  EditShortListId: number = 0;
  @ViewChild('closeBtn') CloseBtnClick: ElementRef;
  isRecruiter:boolean = false;
  CompanyVerificationStatusId:number = null;

  constructor(
    private toasterService: ToasterMessageService,
    private loadingService: LoadingService,
    private httpClient: HttpClientWrapper,
    private confirmService: ConfirmationService,
    private metadataService: MetaDataService,
    private eventsService: EventsService,
    private metaService: MetaWrapperService,
    private saveFileService: SavefileService,
    private cvShortlistService: CVShortlistService,
    private contextService: ContextService,
    private localStorage: LocalstorageService,
    private router: Router,
    private showHideLogsService: ShowHideLogsService,
    private companyVerificationService: CompanyVerificationService) {
    this.isRecruiter = this.contextService.IsRecruiter;
    this.InitSearchResults();
  }

  ngOnInit() {
    this.metaService.SetPageTitle(this.metadataService.CvsShortlistComponentTitle);
    this.metaService.UpdateMetaTag('description', this.metadataService.CvsShortlistComponentDescription);
    this.UserId = this.contextService.LoggedInUserId;

    let shortlistTypeId = JSON.parse(this.localStorage.GetItem('ShortListType' as string));

    if (IsNotNull(shortlistTypeId)) {
      this.ShortListTypeId = parseInt(shortlistTypeId);
    } else {
      this.ShortListTypeId = 1;
    }

    if (IsNotZeroNumber(this.ShortListTypeId)) {
      this.LoadUserShortLists(this.ShortListTypeId);
    } else {
      this.ShortListTypeId = 1;
      this.DropdownPreselected = 0;
      this.ShortListName = "";
      this.LoadUserShortLists(this.ShortListTypeId);
    }

    const selectedShortListId = JSON.parse(localStorage.getItem('selectedshortListId'));
    if (IsNotNull(selectedShortListId)) {
      this.ShortlistIdExport = parseInt(selectedShortListId);
      this.DropdownPreselected = this.ShortlistIdExport;
      this.OnShortlistChangeHandler(this.ShortlistIdExport, this.PageNumber, this.PageSize);

      this.localStorage.RemoveItem('selectedshortListId');
    }

    this.OnChangeCVShortlistChanged();
    this.OnContextChange();

    if (this.contextService.IsBrowser) {
      this.PopulateBreadCrumbsDisplayList();
    }

    this.showHideLogsService.NotifyHideCV$.subscribe(isSuccess => { this.FilterHiddenCVs(isSuccess); });
    this.GetCompanyVerificationStatus();
  }

  OnShortlistChangeHandler(shortlistId: any, pagenumber: number, pagesize: number): void {
    let shortListIdd = parseInt(shortlistId);

    if (shortListIdd != 0) {
      this.StatusMessage = "";
      this.ShortlistIdExport = shortListIdd;
      this.localStorage.SetItem('SelectedCandidate', JSON.stringify(shortListIdd));
      this.loadingService.Show();
      this.InitSearchResults();

      if ((Number(this.localStorage.GetItem(LoginTypeKeys.ShortlistId)) != undefined || null ? Number(this.localStorage.GetItem(LoginTypeKeys.ShortlistId)) : 0) == Number(shortlistId) && this.DropdownPreselected != 0) {
        this.DefaultCheckBox = true;
      } else {
        this.DefaultCheckBox = false;
      }

      this.cvShortlistService.GetShortlistedCandidates(this.UserId, shortListIdd, pagenumber, pagesize).subscribe({
        next: result => {
          this.ShortListedCandidateResult = result;
          this.ShortListedCandidateResult.Response=this.removeDuplicateCandidate(result.Response);
          this.ShortListedCandidateResult.TotalRecords = result.TotalRecords;

          if (this.ShortListedCandidateResult.TotalRecords == 0) {
            this.ShortListedCandidateResult.TotalRecords = 0;
            this.ShortListedCandidateResult.Response = [];
            this.StatusMessage = Manageemployershortlist.onShortlistChangeHandlerShorlistCandidateMessage;
          }

          if (IsNotNull(this.ShortListName) && IsNotNullOrEmpty(this.UserCVShortLists)) {
            this.ShortListName = this.UserCVShortLists.find(x => x.Id == Number(localStorage.getItem(LoginTypeKeys.ShortlistId))).ListName;
          }
        
          this.loadingService.Hide();
        }, error: error => {
          this.loadingService.Hide();
        }
      });
    } else {
      this.loadingService.Hide();
      this.ShortListedCandidateResult.TotalRecords = 0;
      this.ShortListedCandidateResult.Response = [];
      this.DropdownPreselected = 0;
      this.DefaultCheckBox = false;
      this.StatusMessage = Manageemployershortlist.onShortlistChangeHandlerSelectedShortlistMessage;
    }
  }

  InitSearchResults(): void {
    this.ShortListedCandidateResult = new ResultsWithOutPagination<ShortListedCandidate>();
    this.ShortListedCandidateResult.Response = [];
    this.ShortListedCandidateResult.TotalRecords = 0;
  }

  ExportAll(): void {
    if (this.ShortlistIdExport != 0) {
      this.loadingService.Show();
      this.cvShortlistService.GetExcelToExport(this.ShortlistIdExport, this.UserId, this.SelectedDateFromOnExportAll, this.SelectedDateToOnExportAll, 1, this.NoOFrecords)
        .subscribe({
          next: listResult => {
            if (listResult.TotalRecords > 0) {
              this.saveFileService.DownloadFile(ConvertBytesBase64StringToBlob(listResult.Content), listResult.Name);
            } else {
              this.toasterService.Info('No record found please try with different date!', null);
            }

            this.loadingService.Hide();
          }, error: error => {
            this.toasterService.Error('Please provide correct information.');
            this.loadingService.Hide();
          }
        });
    } else {
      this.toasterService.Error(CommonMessages.SelectShortlistToExportData, null);
    }
  }

  ExportShortlistChangeHandler(id: number): void {
    this.ShortlistIdExport = id;
  }

  LoadUserShortLists(shortListTypeId: number): void {
    this.DefaultCheckBox = false;
    this.loadingService.Show();
    this.cvShortlistService.GetShortlists(shortListTypeId, this.UserId, 0).subscribe({
        next: userShortlistResult => {
          if (IsAny(userShortlistResult)) {
            this.UserCVShortLists = userShortlistResult;
            let shortlistId: number;

            if (IsAny(this.UserCVShortLists)) {
              if (IsNotZeroNumber(this.CreatedShortlistId)) {
                shortlistId = this.CreatedShortlistId
              } else {
                shortlistId = this.UserCVShortLists[0].Id;
              }

              let editedShortlistId = Number(this.localStorage.GetItem(LoginTypeKeys.EditedShortlistId));
              if(IsNotZero(editedShortlistId)){
                this.DropdownPreselected = editedShortlistId;
              }
              else{
                this.DropdownPreselected = IsNotNull(Number(this.localStorage.GetItem(LoginTypeKeys.ShortlistId))) ? Number(this.localStorage.GetItem(LoginTypeKeys.ShortlistId)) : 0;
              }

              if (IsAny(this.UserCVShortLists)) {
                let exsistsinlist = userShortlistResult.find(x => x.Id == this.DropdownPreselected);
                if (IsNullOrEmpty(exsistsinlist)) {
                  this.DropdownPreselected = 0;
                }
                this.localStorage.SetItem(LoginTypeKeys.EditedShortlistId, "0");
              }

              this.OnShortlistChangeHandler(this.DropdownPreselected, this.PageNumber, this.PageSize);
            }
          } else {
            this.UserCVShortLists = [];
            this.ShortListedCandidateResult.TotalRecords = 0;
            this.ShortListedCandidateResult.Response = [];
            this.DropdownPreselected = 0;
            this.StatusMessage = Manageemployershortlist.WhenNoShortlistCreated;
          }

          this.loadingService.Hide();
        }, error: error => {
          this.loadingService.Hide();
        }
      });

    this.IsUserIsAdmin = JSON.parse(localStorage.getItem('IsLoggedInUserCompanyAdmin'))
  }

  ShortlistTypeChangeHandler(): void {
    this.UserCVShortLists = [];
    this.PageNumber = 1;
    this.PageSize = 10;
    this.DropdownPreselected = 0;
    //this.localStorage.SetItem('ShortListType', JSON.stringify(this.ShortListTypeId));
    this.localStorage.RemoveItem('SelectedCandidate');
    this.LoadUserShortLists(this.ShortListTypeId);
  }

  OnChangeCVShortlistChanged(): void {
    this.eventsService.OnDefaultCVShortlistChanged.subscribe(cvShortlistStatus => {
      if (IsNotNull(cvShortlistStatus) && IsNotNull(cvShortlistStatus.IsAddInShortlist) && cvShortlistStatus.IsAddInShortlist == true) {
        // if add in list
        if (IsAny(this.UserCVShortLists)) {
          let filteredCandidate = this.UserCVShortLists.filter(m => m.UserId == cvShortlistStatus.CandidateId);

          if (filteredCandidate[0] !== undefined && filteredCandidate[0] != null) {
            filteredCandidate[0].IsCandidateExistsInList = true;
          }
        }
      } else {
        // if remove from list
        if (IsAny(this.UserCVShortLists)) {
          let filteredCandidate = this.UserCVShortLists.filter(m => m.UserId == cvShortlistStatus.CandidateId);

          if (filteredCandidate[0] !== undefined && filteredCandidate[0] != null) {
            if (cvShortlistStatus.IsExistInList !== undefined && cvShortlistStatus.IsExistInList != null && cvShortlistStatus.IsExistInList == true) {
              filteredCandidate[0].IsCandidateExistsInList = true;
            } else {
              filteredCandidate[0].IsCandidateExistsInList = false;
            }
          }
        }
      }
    });
  }

  EditShortList(shortlistId: number){
    if(IsNotNull(shortlistId)){
      this.EditShortListId = shortlistId;
      const selectedList = this.UserCVShortLists.find(item => item.Id === shortlistId);
      if(selectedList){
        this.EditedShortListTypeId = selectedList.ListTypeId;
        this.EditedShortListName = selectedList.ListName;
      }
    }    
  }

  UpdateShortListHandler(shortlistId: number) {
    if (IsNotNull(shortlistId)) {
      this.loadingService.Show();
      const updatedShortlist: RecruiterShortlist = {
        Id: shortlistId,
        ShortListName: this.EditedShortListName,
        UserId: this.UserId,
        ShortListTypeId: this.EditedShortListTypeId,
        LastUpdated: undefined
      };

      // if(IsNotNullOrEmpty(this.EditedShortListName)){
      //   this.cvShortlistService.IsCVShortlistExist(this.UserId, this.EditedShortListName, this.EditedShortListTypeId).subscribe({
      //     next: isAlreadyExist => {
      //       if(isAlreadyExist){
      //         this.loadingService.Hide();
      //         this.toasterService.Error(AlreadyWishListed_Message);
      //       }else{
      //         this.cvShortlistService.UpdateEmployerShortlist(this.UserId, this.EditShortListId, updatedShortlist).subscribe(
      //           result => {
      //             this.localStorage.SetItem(LoginTypeKeys.EditedShortlistId,JSON.stringify(this.EditShortListId));
      //             this.toasterService.Success(ManageUserShortlistMessages.OnShortlistUpdatingSuccess, null);
      //             this.LoadUserShortLists(this.ShortListTypeId);
      //             this.CloseBtnClick.nativeElement.click();
      //             this.loadingService.Hide();
        
      //           }, errorResult => {
      //             this.CloseBtnClick.nativeElement.click();
      //             this.loadingService.Hide();
      //             this.toasterService.ServerError();
      //           }
      //         );
      //       }
      //     }, error: error => {
      //       this.loadingService.Hide();
      //     }
      //   });

      // }else{
      //   this.loadingService.Hide();
      //   this.toasterService.Error('Please provide shortlist name', null);
      // }

      if (IsNotNullOrEmpty(this.EditedShortListName)) {
        this.cvShortlistService.UpdateEmployerShortlist(this.UserId, this.EditShortListId, updatedShortlist).subscribe(
          result => {
            this.localStorage.SetItem(LoginTypeKeys.EditedShortlistId, JSON.stringify(this.EditShortListId));
            this.toasterService.Success(ManageUserShortlistMessages.OnShortlistUpdatingSuccess, null);
            this.LoadUserShortLists(this.ShortListTypeId);
            this.CloseBtnClick.nativeElement.click();
            this.loadingService.Hide();
          },
          errorResult => {
            this.CloseBtnClick.nativeElement.click();
            this.loadingService.Hide();
            this.toasterService.ServerError();
          }
        );
      } else {
        this.loadingService.Hide();
        this.toasterService.Error('Please provide shortlist name', null);
      }
    }
  }

  OnShortListRemoveHandler(shortlistId: number) {
    if (shortlistId != 0) {
      let isCompanyAdmin: boolean = this.IsUserIsAdmin;

      if (this.ShortListTypeId == 2) {
        if (isCompanyAdmin == true) {
          this.confirmService.SetTitle("Confirm Deletion.").SetMessage(ConfirmationMessages.ConfirmationOnRemoveWishlist, 'Confirm').OpenConfirmationDialogue(IsOk => {
            if (IsOk) {
              this.loadingService.Show();
              this.cvShortlistService.DeleteEmployerShortlist(this.UserId, shortlistId).subscribe({
                next: deleteDesult => {
                  // get fresh data
                  this.cvShortlistService.GetShortlists(this.ShortListTypeId, this.UserId, 0)
                    .subscribe({
                      next: shortList => {
                        this.UserCVShortLists = shortList;

                        if (IsAny(this.UserCVShortLists)) {
                          shortlistId = this.UserCVShortLists[0].Id;
                          this.DropdownPreselected = shortlistId;
                          if (this.UserCVShortLists.some(x => x.Id != Number(localStorage.getItem(LoginTypeKeys.ShortlistId)))) {
                            localStorage.setItem(LoginTypeKeys.ShortlistId, "0");
                            this.localStorage.SetItem(LoginTypeKeys.ShortlistName, "");
                            this.DefaultCheckBox = false;
                          }

                          this.OnShortlistChangeHandler(shortlistId, this.PageNumber, this.PageSize);
                        } else {
                          this.ShortListedCandidateResult.TotalRecords = 0;
                          this.ShortListedCandidateResult.Response = [];
                          this.DropdownPreselected = 0;
                          this.StatusMessage = Manageemployershortlist.WhenNoShortlistCreated;
                        }

                        if (this.UserCVShortLists.length == 0) {
                          localStorage.setItem(LoginTypeKeys.ShortlistId, "0");
                          this.localStorage.SetItem(LoginTypeKeys.ShortlistName, "");
                          this.DefaultCheckBox = false;
                        }
                      }
                    });

                  localStorage.setItem(LoginTypeKeys.ShortlistId, "0");
                  this.localStorage.SetItem(LoginTypeKeys.ShortlistName, "");
                  this.DefaultCheckBox = false;
                  this.loadingService.Hide();
                  this.toasterService.Success(ManageUserShortlistMessages.OnShortlistDeletingSuccess, null);
                }, error: error => {
                  this.loadingService.Hide();
                  this.toasterService.Error(CommonMessages.OnSomethingWentWrongError, null);
                }
              });
            }
          });
        } else {
          this.toasterService.Error("You can not delete this list", null);
        }
      } else {
        this.confirmService.SetTitle("Confirm Deletion.").SetMessage(ConfirmationMessages.ConfirmationOnRemoveWishlist, 'Confirm').OpenConfirmationDialogue(IsOk => {
          if (IsOk) {
            this.loadingService.Show();
            this.cvShortlistService.DeleteEmployerShortlist(this.UserId, shortlistId)
              .subscribe({
                next: deleteDesult => {
                  // get fresh data
                  this.cvShortlistService.GetShortlists(this.ShortListTypeId, this.UserId, 0).subscribe({
                      next: shortList => {
                        this.UserCVShortLists = shortList;

                        if (IsNotNull(this.UserCVShortLists) && this.UserCVShortLists.length == 1) {
                          shortlistId = this.UserCVShortLists[0].Id;
                          this.DropdownPreselected = shortlistId;
                          this.OnShortlistChangeHandler(shortlistId, this.PageNumber, this.PageSize);
                        } else {
                          this.ShortListedCandidateResult.TotalRecords = 0;
                          this.ShortListedCandidateResult.Response = [];
                          this.DropdownPreselected = 0;
                          this.StatusMessage = Manageemployershortlist.WhenShortListHaveMoreThan2Records;
                        }
                      }
                    });

                  this.loadingService.Hide();
                  this.toasterService.Success(ManageUserShortlistMessages.OnShortlistDeletingSuccess, null);
                }, error: error => {
                  this.loadingService.Hide();
                  this.toasterService.Error(CommonMessages.OnSomethingWentWrongError, null);
                }
              });
          }
        });
      }
    }
  }

  OnPageNumberChanged(pageNumber: number): void {
    this.PageNumber = pageNumber;
    this.OnShortlistChangeHandler(this.DropdownPreselected, this.PageNumber, this.PageSize);
  }

  OnPageSizeChanged(pageSize: number): void {
    this.PageSize = pageSize;
    this.OnShortlistChangeHandler(this.DropdownPreselected, this.PageNumber, this.PageSize);
  }

  RemoveCandidateFromShortlistHandler(shortlistDetailId: number): void {
    if (IsNotZeroNumber(shortlistDetailId)) {
      this.confirmService.SetTitle("Confirm Deletion.").SetMessage(ConfirmationMessages.ConfirmationOnRemoveWishlist, 'Confirm').OpenConfirmationDialogue(IsOk => {
        if (IsOk) {
          this.loadingService.Show();
          this.cvShortlistService.DeleteEmployerShortlistDetail(this.UserId, shortlistDetailId).subscribe({
            next: deleteCandidateResult => {
              let deletedCandidate = this.ShortListedCandidateResult.Response.findIndex(remove => remove.ShortListDetailId == shortlistDetailId);

              this.ShortListedCandidateResult.Response.splice[deletedCandidate];
              let deleteFromList = this.UserCVShortLists.filter(x => x.Id == this.DropdownPreselected);

              this.toasterService.Success(`Remove from ${deleteFromList[0].ListName} shortlist`, 'Removed Successfully');
              this.cvShortlistService.GetShortlistedCandidates(this.UserId, this.DropdownPreselected, this.PageNumber, this.PageSize)
                .subscribe({
                  next: shortlistedCandidate => {
                    if (IsAny(shortlistedCandidate.Response)) {
                      this.ShortListedCandidateResult = shortlistedCandidate;

                      let reduceCount = this.UserCVShortLists.filter(short => short.Id == this.DropdownPreselected);

                      if (IsAny(reduceCount)) {
                        if (reduceCount[0].TotalCandidates != 0) {
                          reduceCount[0].TotalCandidates -= 1;
                        }
                      }
                    } else {
                      let reduceCount = this.UserCVShortLists.filter(short => short.Id == this.DropdownPreselected);

                      if (IsAny(reduceCount)) {
                        if (reduceCount[0].TotalCandidates != 0) {
                          reduceCount[0].TotalCandidates -= 1;
                        }
                      }

                      this.ShortListedCandidateResult.TotalRecords = 0;
                      this.ShortListedCandidateResult.Response = [];
                      this.StatusMessage = Manageemployershortlist.onShortlistChangeHandlerShorlistCandidateMessage;
                    }

                    this.loadingService.Hide();
                  }, error: error => {
                    this.loadingService.Hide();
                  }
                });
            }
          });
        }
      });
    } else {
      this.toasterService.ServerError();
    }
  }

  ShortListCreated(createdShortlist: any): void {
    this.ShortListTypeId = createdShortlist.ShortListTypeId;
    this.CreatedShortlistId = createdShortlist.Id;
    this.LoadUserShortLists(this.ShortListTypeId);
  }

  OnHideCV(candidate: Candidate): void {
    this.HiddenCVId = candidate.Id;
    this.showHideLogsService.SaveHideCVInfo(candidate);
  }

  //#region Private Methods

  private FilterHiddenCVs(isSuccess: boolean) {
    if (isSuccess) {
      this.ShortListedCandidateResult.Response = this.ShortListedCandidateResult.Response.filter(x => x.Id != this.HiddenCVId);
      this.ShortListedCandidateResult.TotalRecords = this.ShortListedCandidateResult.TotalRecords - 1;

      if (IsAny(this.UserCVShortLists)) {
        if (IsNotZero(this.DropdownPreselected)) {
          let userSelectedList = this.UserCVShortLists.filter(x => x.Id == this.DropdownPreselected);
          userSelectedList[0].TotalCandidates = userSelectedList[0].TotalCandidates - 1;
        }
      }
    }
  }

  private OnContextChange(): void {
    this.contextService.OnContextChange.subscribe({
      next: isJobSeeker => {
        if (isJobSeeker) {
          if (this.router.url == '/cvs-shortlist') {
            this.router.navigateByUrl('/jobs-shortlist');
          }
        }
      }
    });
  }

  UpdateShortListToDefault() {
    if(IsNotZero(this.DropdownPreselected)){
      let empshortlistdetail = new CVShortListDetail();
      empshortlistdetail.ShortListId = this.DropdownPreselected;
      empshortlistdetail.UserId = this.contextService.LoggedInUserId;
      empshortlistdetail.AddedOn = new Date();
  
      AddEmployerShortlistToDefaultDetail(this.httpClient, empshortlistdetail).subscribe(result => {
        if (result) {
          localStorage.setItem(LoginTypeKeys.ShortlistId, this.DropdownPreselected.toString());
          this.localStorage.SetItem('ShortListType', this.ShortListTypeId.toString());
          
  
          if (IsAny(this.UserCVShortLists)) {
            localStorage.setItem(LoginTypeKeys.ShortlistName, this.UserCVShortLists.find(x => x.Id == this.DropdownPreselected).ListName);
          } else {
            localStorage.setItem(LoginTypeKeys.ShortlistName, this.ShortListName);
          }
  
          this.toasterService.Success("Added list to default.");
        } else {
          localStorage.setItem(LoginTypeKeys.ShortlistId, "0");
          localStorage.setItem(LoginTypeKeys.ShortlistName, "");
          this.toasterService.Success("Removed from default list.")
        }
      });
    }    
  }

  PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();

    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleDashBoard;
    listItem.item = `${environment.HostName}${DashBoard_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem1 = new ListItem();
    listItem1.position = 2;
    listItem1.name = BreadcrumbsTitleSearchCVs;
    listItem1.item = `${environment.HostName}${CvSearch_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem1);

    let listItem2 = new ListItem();
    listItem2.position = 3;
    listItem2.name = BreadcrumbsTitleCVsShortlist;
    listItem2.item = `${environment.HostName}${MyCVShortList_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);
  }

  //#endregion
  
  removeDuplicateCandidate(responce: any[]): any[] {
    return responce.reduce((unique, responce) => {
      const isDuplicate = unique.some(item => item.Id === responce.Id);
      if (!isDuplicate) {
        unique.push(responce);
      }
      return unique;
    }, []);
  }

  GetCompanyVerificationStatus():void {
    if(IsTrue(this.contextService.IsUserLoggedIn) && this.contextService.IsBrowser){
      this.loadingService.Show();
      this.companyVerificationService.GetCompanyVerificationStatusByUser(this.contextService.LoggedInUserId)
      .subscribe({ next: companyStatus => {
        this.CompanyVerificationStatusId = companyStatus;
        this.loadingService.Hide();
      },error: error => {
        this.loadingService.Hide();
      }});
    }
  }
}
