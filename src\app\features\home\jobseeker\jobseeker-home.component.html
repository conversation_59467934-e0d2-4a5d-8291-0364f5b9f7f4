<!--New Section Carosusel-->
<section class="NewTheme">
  <div class="container">   
      <div class="row">
        <div class="col-sm-12 SliderBox1">
          <h2 class="heading">Get Hired Faster</h2>        
          <span class="a4u">with Apply4U<span>.</span></span>
          <h3>Your helpful job site</h3>        
        </div>
      </div>    
  </div>

  <div class="container">
    <div class="row card" [ngClass]="ContextService.IsUserLoggedIn ? 'mt-2' : ''">
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 p-0">
        <div class="row d-flex justify-content-center">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 searchCard">
            <div class="row m-0">
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group mb-0">
                  <label>Describe your ideal role</label>
                  <app-keyword-autocomplete class="formMain" class="form-control-sm p-0"
                  [Classes]="'homepage-search'"
                    [SearchKeywordTitle]="SearchParams.Keywords"
                    (SearchKeywordTitleChanged)="SearchParams.Keywords=$event"
                    (IsEnterKeyPressed)="SearchEnterKeyPressed($event)">
                  </app-keyword-autocomplete>
                </div>
              </div>
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group mb-0">
                  <label>Where</label>
                  <app-smart-location-autocomplete-radius [SearchKeywordLocation]="SearchParams.Location"
                    class="form-control-sm p-0" [ReadCurrentLocation]="ReadCurrentLocation" [IsHomePageSearch]="true"
                    (OnLocationSelectionChanged)="OnLocationSelectionChangedEvent($event)">
                  </app-smart-location-autocomplete-radius>
                </div>
              </div>
              <div class="col-md-4 MobileSearchForm">
                <div class="form-group MobileFix">
                  <button class="btn btn-primary btn-sm" (click)="SearchButtonClickHanler()">Search Jobs</button>
                </div>
              </div>
              <div class="d-flex justify-content-between w-100">
                <div class="col-md-8 fit-width MobileSearchForm">
                  <div class="form-group MobileFix padfix">
                    <a (click)="AdvanceSearchClickHandler()" href="JavaScript:Void(0);" title="Advance Search">Advanced
                      Search<i class="fas fa-chevron-right ml-1"></i></a>
                  </div>
                </div>
                <div class="col-md-4 fit-width MobileSearchForm ">
                  <div class="form-group MobileFix padfix">
                    <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                      routerLink="{{Navigations.BrowseJob}}" class="MobileSPC" title="Browse Jobs">Browse Jobs<i
                        class="fas fa-chevron-right ml-1"></i></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!--New Section Carosusel-->


<!--Job Search Options-->
<section class="NewThemeSearch">
  <div class="container p-0">
    <div class="row searchmore m-0 " #MoreJobSearchOptionId>
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
        <h2><span>More</span> Job Search Options</h2>
      </div>
      <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 job-item pl-0">
        <div id="accordion">
          <div class="accordion" id="PerInfo">
            <div class="card" [ngClass]="ContextService.IsMobileView ? 'mb-0': ''">
              <div [ngClass]="ContextService.IsMobileView ? 'card-header collapsed' : 'card-header show in'" 
              data-toggle="collapse" (click)="toggleJobSearchAccordion()">
                <span class="title">Popular Job Searches</span>
                <span *ngIf="ContextService.IsMobileView" class="accicon"> <i class="fas"
                    [ngClass]="{'fa-angle-down': !IsJobSearchAccordionExpanded, 'fa-angle-up': IsJobSearchAccordionExpanded}"></i></span>
              </div>
              <div [ngClass]="ContextService.IsMobileView ? 'panel-collapse collapse' : 'panel-collapse show'" 
              aria-labelledby="headingThree" [class.show]="IsJobSearchAccordionExpanded">
                <div class="panel-body p-0 listInScroll" *ngIf="ContextService.IsDesktopView || IsJobSearchAccordionExpanded">
                  <ul>
                    <li *ngFor="let popular of IsShowMorePopularSearch && (IsShowMorePopularSearchClicked || ContextService.IsServer) ? TopPopularSearchesAll : TopPopularSearches">
                      <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="popular.SearchUrl" title="{{ popular.SearchName }} Jobs" (click)="OnClickJobseekerHomeNavigation('Search Option')">
                        {{ popular.SearchName }} Jobs
                      </a>
                    </li>
                  </ul>
                  <div class="pb-2">
                    <a href="JavaScript:void(0);" class="btnshowmore" (click)="PopularSearchesExpandHandler()">
                      {{ IsShowMorePopularSearch ? "See less" : "See more" }} Jobs
                      <i [ngClass]="IsShowMorePopularSearch ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Jobs By Location Section -->
      <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 p-0">
        <div class="accordion" id="PerInfo">
          <div class="card" [ngClass]="ContextService.IsMobileView ? 'mb-0': ''">
            <div [ngClass]="ContextService.IsMobileView ? 'card-header collapsed' : 'card-header show in'"
              data-toggle="collapse" (click)="toggleLocationAccordion()">
              <span class="title">Jobs By Location</span>
              <span *ngIf="ContextService.IsMobileView" class="accicon">
                <i class="fas" [ngClass]="{'fa-angle-down': !IsLocationAccordionExpanded, 'fa-angle-up': IsLocationAccordionExpanded}"></i>
              </span>
            </div>
            <div [ngClass]="ContextService.IsMobileView ? 'panel-collapse collapse' : 'panel-collapse show'"
                 aria-labelledby="headingTwo" [class.show]="IsLocationAccordionExpanded">
              <div class="panel-body jobs listInScroll">
                <app-browse-by-location-item [isUnderLine]="true" 
                                             *ngIf="ContextService.IsDesktopView || IsLocationAccordionExpanded"
                                             [LocationsByRegion]="IsShowMoreRegions && (IsShowMoreRegionsClicked || ContextService.IsServer) ? AllRegion : LocationRegion"
                                             [IsJob]="ContextService.IsJobSeeker" 
                                             [IsHomePageDisplay]="true">
                </app-browse-by-location-item>
                <div>
                  <a class="btnshowmore" href="JavaScript:Void(0);" (click)="LocationExpandHandler()">
                    {{ IsShowMoreRegions ? "See less" : "See more" }} Locations
                    <i *ngIf="!IsShowMoreRegions" [ngClass]="{ 'fas fa-chevron-down': !IsShowMoreRegions }"></i>
                    <i *ngIf="IsShowMoreRegions" [ngClass]="{ 'fas fa-chevron-up': IsShowMoreRegions }"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Jobs By Industry -->
      <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12 job-item pr-0">
        <div class="accordion" id="PerInfo">
          <div class="card">
            <div [ngClass]="ContextService.IsMobileView ? 'card-header collapsed': 'card-header show in' "
              data-toggle="collapse" href="#collapseJobByInds">
              <span class="title">Jobs By Industry</span>
              <span *ngIf="ContextService.IsMobileView" class="accicon"><i
                  class="fas fa-angle-down rotate-icon"></i></span>
            </div>
            <div [id]="ContextService.IsMobileView ? 'collapseJobByInds': ''"
              [ngClass]="ContextService.IsMobileView ? 'panel-collapse collapse' : 'panel-collapse collapse show'"
              aria-labelledby="headingOne">
              <div class="panel-body jobs listInScroll">
                <app-browse-by-industry *ngIf="Sectors" [IsHomePageDisplay]="true" [CssClass]="'HomeListing'"
                  [Industries]="Sectors" [IsJob]="ContextService.IsJobSeeker">
                </app-browse-by-industry>
                <div [ngClass]="IsShowMoreIndustry ? 'ShowSlideDown' : 'HideSlideDown'">
                  <app-browse-by-industry *ngIf="AllSectors && (IsShowMoreIndustryClicked || ContextService.IsServer)"
                    [Industries]="AllSectors" [IsJob]="ContextService.IsJobSeeker" [IsHomePageDisplay]="true"
                    [CssClass]="'HomeListing'">
                  </app-browse-by-industry>
                </div>
                <div class="mt-2">
                  <a class="btnshowmore" (click)="IndustryExpandHandler()" href="JavaScript:Void(0);">
                    {{ IsShowMoreIndustry ? "Show less" : "Show more" }} Industries
                    <i *ngIf="!IsShowMoreIndustry" [ngClass]="{ 'fas fa-chevron-down': !IsShowMoreIndustry }"></i>
                    <i *ngIf="IsShowMoreIndustry" [ngClass]="{ 'fas fa-chevron-up': IsShowMoreIndustry }"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!--Job Search Options-->


<!--User Stories-->
<section class="pb-4 Registration">
  <div class="container p-0">
    <h2 class="text-center UserStory"><b>User Stories</b></h2>
    <hr class="midline" />
    <div class="card col-md-12 mt-2 p-0">
      <div id="carouselExampleControls" class="carousel slide mb-5" data-ride="carousel" data-interval="100000">
        <div class="w-100 carousel-inner mb-3">
          <div class="carousel-item active">
            <div class="row">
              <div class="col-md-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2 class="text-white">Dan Robert</h2>
                      <small><q>When I was searching through for jobs, I just couldn’t
                          find the right ones to apply to. They either were below
                          my experience level or just not right. I was about to
                          give up but then Apply4U made me realise you could work
                          backwards, weird I know! I became a verified
                          professional and the jobs I wanted and better came
                          flooding towards me. What makes it even better is these
                          opportunities weren’t even yet advertised on any job
                          site.</q></small><br>
                      <h3 class="smallest mute">Got the job come to him</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2 class="text-white">Adam Ali</h2>
                      <small><q>I was getting interviews but they never led anywhere, I
                          didn’t know what I was doing wrong. I randomly started
                          talking to people in some communities then an Apply4U
                          expert pointed out a few things which I thought were
                          obvious, but apparently they weren’t! would you know it,
                          I mentioned the obvious in my next interview and got the
                          job!</q></small><br>
                      <h3 class="smallest mute">Got an offer after a little interview advice</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2 class="text-white">Sarah Richmond</h2>
                      <small><q>Honestly I wasn’t actively looking for a new job, so I
                          registered on Apply4U just to network and build up my
                          connections. I found it quite useful and it helped me
                          find a few new businesses to network with. One of these
                          businesses ended up messaging me with an interesting
                          opportunity which I just could refuse.</q></small><br>
                      <h3 class="smallest mute">Networked her way up</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md- col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2 class="text-white">Aaron Richardson</h2>
                      <small><q>I didn’t have much time to spend on looking for a new
                          job, so I made my profile on Apply4U after hearing that
                          it does the search for you. Within a few days I started
                          receiving calls and messages from employers and
                          recruiters. I had a few interviews and managed to secure
                          my new position.</q></small><br>
                      <h3 class="smallest mute">Saved time by allowing people to contact him</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2 class="text-white">Shane Gill</h2>
                      <small><q>I was invited to join Apply4U and went ahead in doing
                          so. Very quickly I realised it was a useful tool and
                          since then I’ve been using it most days. Already had
                          several job offers and am waiting on one more before
                          deciding which to go for.</q></small><br>
                      <h3 class="smallest mute">Owes it to a friend</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="carousel-item">
            <div class="row">
              <div class="col-md-12 col-12">
                <div class="carousel-caption">
                  <div class="row">
                    <div class="col-xl-12 col-lg-12 col-md-23 col-sm-12 col-xs-12">
                      <h2 class="text-white">Jaswinder Singh</h2>
                      <small><q>After job searching without any success for months. I
                          decided I needed to try something new. I came across the
                          free CV review offering and decided to take it up. I
                          then decided to get my CV written by a professional and
                          would you know it, within a few weeks I started to get
                          interviews. I really didn’t give it much thought before
                          this, but it really has opened my eyes.</q></small><br>
                      <h3 class="smallest mute">Got a job through a new CV</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
          <span class="carousel-control-prev-icon" aria-hidden="true"><i class="fas fa-chevron-left"></i></span>
          <span class="sr-only">Previous</span>
        </a>
        <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
          <span class="carousel-control-next-icon" aria-hidden="true"><i class="fas fa-chevron-right"></i></span>
          <span class="sr-only">Next</span>
        </a>
      </div>
    </div>
  </div>
</section>
<!--User Stories-->


<!-- Banners section -->
<section class="a4u-banner" #HomePageViewUpgradesId>
  <div class="container">
    <div class="row">
      <div class="col-md-12 p-0">
        <a4u-app-banner [IsHomePage]="true"></a4u-app-banner>
      </div>
    </div>
  </div>
</section>
<!-- Banners section -->


<!--Career Advice-->
<section class="HelpAdvice">
  <div class="container">
    <div class="row" [ngClass]="ContextService.IsMobileView ? 'carousel slide': ''" id="carouselHelp"
      data-ride="carousel">
      <div class="carousel-inner">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
          <h2><span>Career Advice</span></h2>
        </div>
        <div class="row">
          <div
            [ngClass]="ContextService.IsMobileView ? 'carousel-item item active': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
            <div class="accordion" id="PerInfo">
              <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard' : ''">
                <div class="card-header show in" data-toggle="collapse">
                  <span class="title">Your CV (Curriculum Vitae)</span>
                </div>
                <div class="panel-collapse collapse show" aria-labelledby="headingTwo">
                  <div class="panel-body">
                    <div class="row m-0">
                      <div class=" col-md-12 VideoCard">
                        <div class="card-body">
                          <img src="assets/images/home/<USER>" alt="CV" title="Your CV" width="340"
                            height="227">
                          <p>First step in getting the job you want is getting your CV right! Sell yourself on paper. A
                            better CV
                            means more Interviews, guaranteed</p>
                          <div class="adviceBtn"><a class="btn btn-success" title="Continue Reading"
                              routerLink="/your-curriculum-vitae">Find
                              out More</a></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div [ngClass]="ContextService.IsMobileView ? 'carousel-item item': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
            <div class="accordion" id="PerInfo">
              <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard' : ''">
                <div class="card-header show in" data-toggle="collapse">
                  <span class="title">Your Job Search</span>
                </div>
                <div class="panel-collapse collapse show" aria-labelledby="headingOne">
                  <div class="panel-body">
                    <div class="row m-0">
                      <div class=" col-md-12 VideoCard">
                        <div class="card-body">
                          <img loading="lazy" loading="lazy" src="assets/images/home/<USER>"
                            alt="Job Search" title="Your Job Search" width="340" height="227">
                          <p>The whole job search process can become a little more complicated than it looks. Don’t
                            forget, the most
                            important part</p>
                          <div class="adviceBtn adviceBtn2"><a class="btn btn-success" title="Continue Reading"
                              routerLink="/your-job-search">Find out
                              More</a></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div [ngClass]="ContextService.IsMobileView ? 'carousel-item item': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
            <div class="accordion" id="PerInfo">
              <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard' : ''">
                <div class="card-header show in" data-toggle="collapse">
                  <span class="title">Your Interview</span>
                </div>
                <div class="panel-collapse collapse show" aria-labelledby="headingTwo">
                  <div class="panel-body">
                    <div class="row m-0">
                      <div class=" col-md-12 VideoCard">
                        <div class="card-body">
                          <img loading="lazy" src="assets/images/home/<USER>" alt=" Interview"
                            title="Your Interview" width="340" height="227">
                          <p>So you’ve got an amazing CV and used Apply4U to job search and apply to jobs. Now you’ve
                            secured that interview.
                            What now?</p>
                          <div class="adviceBtn"><a class="btn btn-success" title="Continue Reading"
                              routerLink="/your-interview">Find out
                              More</a></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      @if (ContextService.IsMobileView) {
      <ol class="carousel-indicators indicators">
        <li data-target="#carouselHelp" data-slide-to="0" class="active"></li>
        <li data-target="#carouselHelp" data-slide-to="1"></li>
        <li data-target="#carouselHelp" data-slide-to="2"></li>
      </ol>
      }
    </div>
  </div>
</section>
<!--Career Advice-->


<!--Featured Recruiters-->
@defer (on viewport()) {
<div class="row m-0 LogoSliderBox">
  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
    <h2 class="text-center h1">Featured <span>Recruiters</span></h2>
  </div>
  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
    <div id="LogoSlider" class="carousel slide" data-ride="carousel">
      <div class="carousel-inner">
        <div class="carousel-item active">
          <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
              <h2 class="text-center"><img loading="lazy" src="assets/images/home/<USER>" alt="apple"
                  width="200px" height="200px" class="img"></h2>
            </div>
          </div>
        </div>
        <div class="carousel-item">
          <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
              <h2 class="text-center"><img loading="lazy" src="assets/images/home/<USER>" alt="NHS" width="200px"
                  height="200px" class="img"></h2>
            </div>
          </div>
        </div>
        <div class="carousel-item">
          <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
              <h2 class="text-center"><img loading="lazy" src="assets/images/home/<USER>" alt="asics" width="200px"
                  height="200px" class="img"></h2>
            </div>
          </div>
        </div>
        <div class="carousel-item">
          <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
              <h2 class="text-center"><img loading="lazy" src="assets/images/home/<USER>" alt="Adecco"
                  width="200px" height="200px" class="img"></h2>
            </div>
          </div>
        </div>
        <div class="carousel-item">
          <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
              <h2 class="text-center"><img loading="lazy" src="assets/images/home/<USER>" alt="marieCurie"
                  width="200px" height="200px" class="img"></h2>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ol class="carousel-indicators featuredrecruiter">
      <li data-target="#LogoSlider" data-slide-to="0" class="active"></li>
      <li data-target="#LogoSlider" data-slide-to="1"></li>
      <li data-target="#LogoSlider" data-slide-to="2"></li>
      <li data-target="#LogoSlider" data-slide-to="3"></li>
      <li data-target="#LogoSlider" data-slide-to="4"></li>
    </ol>
  </div>
  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
    <a class="btn btn-primary" routerLink="{{ Navigations.CompanyDirectory }}">See All Companies</a>
  </div>
</div>

} @placeholder {
<div class="row m-0 LogoSliderBox"></div>
}

<!--Featured Recruiters-->


<!--blog-->
@defer(on viewport()){
<div>
  <app-our-blogs></app-our-blogs>
</div>
} @placeholder {
<div class="bg-off-white"></div>
}
<!--blog-->


<!--Trustpilot-->
@defer (on viewport()) {
<section class="Trustpilot">
  <div class="container">
    <trust-pilot-widget></trust-pilot-widget>
  </div>
</section>
} @placeholder {
<section class="Trustpilot"></section>
}
<!--Trustpilot-->