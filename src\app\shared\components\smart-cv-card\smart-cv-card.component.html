
<div class="row m-0" *ngIf="Candidates && Candidates.length == 0">
  <div class="mt-1 mb-2 col-12 p-0" *ngIf="IsSearchResultPage">
    <a href="{{ContactUsCardUTMSource}}">
      <img class="img-fluid" src="assets/images/CV-Search-Banner-Cards/Recruiter-Call-Our-Expert-Now-Card.webp" loading="lazy"
        alt="Call our experts now">
    </a>    
  </div>
</div>

<div class="row" *ngFor="let candidate of Candidates; let indx = index;">
   
  <div class="col-12 card mobile-ad-banner" *ngIf="(indx +1)% 6 === 0 && ShowBanner && IsSearchResultPage">
    <div class="image-container">
      <span class="close-btn" (click)="HideBanner()">&times;</span>
      <a href="{{CandidateCardsUTMSource}}">
        <img class="img-fluid" src="assets/images/CV-Search-Banner-Cards/Recruiter-Unlimited-Free-Ads-Card.webp" width="100%" height="100%" loading="lazy" alt="{{CandidateCardAltText}}">
      </a>
    </div>
  </div>
  <!--CV Card-->
  <div class="col-12 card p-3 CvCard"  id="CV-{{candidate.Id}}"  [ngClass]="{'CvCard-Blue':candidate.CandidateTypeId == CandidateType.Normal,
   'CvCard-Dark-Orang':candidate.CandidateTypeId == CandidateType.Internal,
     'CvaCard-Green':candidate.CandidateTypeId == CandidateType.Screened,
     'CvCard-Red':candidate.CandidateTypeId == CandidateType.Featured, 'card-disable': candidate.IsActive == false}" >
    <div class="row m-0">
      <div class=" col-md-12 row m-0 mb-1  p-0" *ngIf="IsScreenedCVsResult == true">
        <div class="col-md-9 p-0">
          Requested Search: {{candidate?.RequestedSearch}}
        </div>
        <div class="col-md-3 p-0 text-right">
          <i title="Unsuitable" (click)="ScreenedCVsService.UpdateCVRank(candidate, 'Unsuitable', 3)"
            class="fa-flag fa-lg redflag mr-2 screening-flag" [ngClass]="candidate?.CvSuitabilityTypeId == 3 ? 'fas' : 'far'"></i>
          <i title="Maybe Suitable" (click)="ScreenedCVsService.UpdateCVRank(candidate, 'Maybe suitable', 2)"
            class="fa-flag fa-lg yellowflag mr-2 screening-flag" [ngClass]="candidate?.CvSuitabilityTypeId == 2 ? 'fas' : 'far'"></i>
           <i class="fa-flag fa-lg greenflag mr-2 screening-flag" (click)="ScreenedCVsService.UpdateCVRank(candidate, 'Suitable', 1)"
            [ngClass]="candidate?.CvSuitabilityTypeId == 1 ? 'fas' : 'far'" title="Suitable"></i>
        </div>
      </div>
      <div class="mr-2 p-0 userImg">
        <i class="userImg">
          <img width="50" height="50" *ngIf="candidate && candidate.ProfileImagePath && candidate.ProfileImagePath!=''"
            src="{{candidate && candidate.ProfileImagePath && candidate.ProfileImagePath!='' ? candidate.ProfileImagePath : assets/images/default-profile.png}}"
            alt="User Profile Pic" class="media-object img-circle img-h-w ">
            <span class="video-cv-icon" *ngIf="candidate?.HasYobsData" title="Video into" (click)="OpenVideoIntro(candidate)">
              <i class="fas fa-video"></i>
            </span>
        </i>
      </div>
      <div class="col p-0">
        <div class="row m-0">
          <div class="col-2 p-0">
            <span class="CandidateType btn btn-outline" *ngIf="candidate?.CandidateTypeId != 1"
              title="{{candidate?.CandidateType}}">{{candidate?.CandidateType}}</span>
            <span *ngFor="let badge of candidate.Badges">
              <span class="pl-mobile ml-2" *ngIf="badge.Value == SelfVerification">
                <img width="20" height="20" class="mb-1" src="assets/images/Badges/Level-One-Verified.webp"
                  title="User has verified his email address">
              </span>
              <span class="pl-mobile ml-2" *ngIf="badge.Value == EmployerVerification">
                <img width="20" height="20" class="mb-1" src="assets/images/Badges/Level-Two-Verified.webp"
                  title="User has received an employer reference">
              </span>
              <span class="pl-mobile ml-2" *ngIf="badge.Value == ExpertVerification">
                <img width="20" height="20" class="mb-1" src="assets/images/Badges/Level-Three.Verified.webp"
                  title="User has been verified by our consultants">
              </span>
            </span>
          </div>
          <div class="col-10 p-0">
            <p class="CvCardIcon text-right">
              <button *ngIf="IsShowIconRequired || IsHideIconRequired" [title]="IsHideIconRequired && !IsShowIconRequired ? 'Hide this CV' : 'Unhide this CV'" class="ml-2 mr-1" 
              (click)="(IsHideIconRequired && !IsShowIconRequired) ? HideCVClick(candidate) : UnhideCVClick(candidate)">
                <i [ngClass]="IsHideIconRequired && !IsShowIconRequired ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
             </button>

              <button *ngIf="IsCVShortlistRequired && candidate?.ShortListDetailId > 0" class="ml-2 mr-1 filled"
                (click)="RemoveCandidateFromShortist(candidate?.ShortListDetailId)" title="Remove." name="RemoveShortlist_{{candidate.Id}}">
                <i class="fa fa-heart"></i>
                </button>
              <!-- <button *ngIf="!IsCVShortlistRequired" class="ml-2 mr-1" title="Shortlist this CV"
                [ngClass]="{'filled':candidate.IsShortListed == true}" (click)="WishListCandidateId=candidate.Id"
                data-toggle="modal" data-target="#CVWishListModal" (click)="IsUserLoggedIn($event)">
                <i *ngIf="candidate.IsShortListed" class="fa fa-heart"></i>
                <i *ngIf="!candidate.IsShortListed" class="far fa-heart"></i>
              </button> -->
              <button class="d-none"  data-toggle="modal" data-target="#CVWishListModal" id="WishListModalId"></button>
              <button *ngIf="!IsCVShortlistRequired" class="ml-2 mr-1" title="Shortlist this CV"
                [ngClass]="{'filled':candidate.IsShortListed == true}" (click)="OnClickShortlistIcon(candidate)">
                <i *ngIf="candidate.IsShortListed" class="fa fa-heart"></i>
                <i *ngIf="!candidate.IsShortListed" class="far fa-heart"></i>
              </button>

              <button class="ml-2 mr-1" (click)="OnClickNotesIcon(1,candidate.Id, candidate.NoteForTypeId, candidate?.IsShortListed)"
                data-toggle="modal" data-target="#SmartNotesModal" title="Notes"><i class="fa fa-file-alt"
                  [ngClass]="{'text-primary': candidate?.IsNotesExists}"
                  [title]="candidate?.IsNotesExists? 'Add notes' : 'Add notes'"></i></button>
              <button class="ml-2 mr-1" (click)="SendRecommendation(candidate.Id,cvDetail?.href)" data-toggle="modal"
                data-target="#ShareCVModal" title="Share this CV"><i class="fa fa-share-alt"></i></button>
              <button class="ml-2 mr-1" (click)="IsUserLoggedIn($event);" *ngIf="(IsScreenedCVsResult == true && IsSearch4me == false)" [ngClass]="{'text-primary': candidate?.IsFeedBackExists}">
                <i data-toggle="modal" data-target="#SmartNotesModal" (click)="NoteTypeId = 3;NoteCandidateId=candidate.Id;" class="far fa-comments"
                  title="Feedback"></i></button>
              <button *ngIf="IsScreenedCVsResult == true" class="ml-2 mr-1" (click)="ScreenedCVsService.DeleteResponse(candidate?.ScreenedCVResponseId)"><i class="far fa-trash-alt"
                  title="Delete"></i></button>
              <button *ngIf="IsScreenedCVsResult == true && candidate?.DownloadedOn" class="ml-2 mr-1" (click)="IsUserLoggedIn($event);"><i data-toggle="modal" data-target="#CandidateContactDetailModal" (click)="OnCandidateDetailsDisplayClick(candidate?.Id)" class="fas fa-phone"
                  title="Contact Details"></i></button>
              <button class="ml-2 mr-1" *ngIf="IsScreenedCVsResult == true && candidate?.ViewedOn"><i class="far fa-eye-slash"
                  title="Last Viewed By You On :{{candidate?.ViewedOn | localDateTime | date:'dd/MM/yyyy hh:mm a'}}"></i></button>
                  <button class="ml-2 mr-1" *ngIf="candidate?.Id != 0 && ContextService.IsApply4UAdmin" id="btnRecommendationList_{{candidate?.Id}}" title="{{candidate?.IsRecommended ? 'Candidate Recommended' : 'Recommend candidate'}}" data-toggle="modal" (click)="OnRecommendationIconClick(candidate?.Id)">
                    <i *ngIf="candidate?.IsRecommended" class="fas fa-thumbs-up"></i>
                    <i *ngIf="!candidate?.IsRecommended" class="far fa-thumbs-up"></i>
                </button>
            </p>
          </div>
        </div>
        <div>
          <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="IsScreenedCVsResult == true ? candidate?.Id : candidate?.JobTitle | cvSearchDetailUrl:candidate?.FormattedAddress:candidate?.Id" #cvDetail (click)="OnClickScreenedCandidates()">
            <h2 innerHtml="{{ candidate?.JobTitle ? (candidate?.JobTitle | highlight: WordsToHighlight) : 'Candidate [' +candidate?.Id + ']' }}"
              title="{{candidate?.JobTitle? candidate?.JobTitle : candidate?.Id}}"></h2>
          </a>
        </div>
        <p class="posted">{{candidate?.CandidateTypeId==3 || (IsScreenedCVsResult == true && candidate?.CandidateTypeId==4) ? 'Added' : 'Logged in'}} {{
          candidate?.LastLoginOn | localDateTime | timeAgo:true }}</p>
        <ul>
          <li *ngIf="candidate?.CompanyName" title="{{candidate?.CompanyName}}" class="ellipsis"><i
              class="fa fa-id-card"></i> {{candidate?.CompanyName}}</li>
          <li *ngIf="candidate?.Id" title="user ID"><i class="fa fa-user-tie"></i> {{candidate?.Id}}</li>
          <li *ngIf="candidate?.FormattedAddress" class="ellipsis">
            <i class="fa fa-map-marker-alt"></i>
            <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{candidate.FormattedAddress | SearchByLocationSmartPipe : false}}" [target]="'_blank'"
              innerHtml="{{candidate.FormattedAddress}}" title="{{ candidate.FormattedAddress }}"></a>
          </li>
          <li *ngIf="candidate.PositionType" title="Employement type"><i class="far fa-clock"></i> {{candidate?.PositionType}}</li>
          <li *ngIf="IsRecentApplicantPage && candidate?.IsActive == true && candidate?.IsSearchAble == true">
            <i class="far fa-circle seeking-now"></i> Seeking Now</li>
        </ul>
      </div>
    </div>
    <div class="row m-0">
      <div class="col-md-12 p-0 ExecutiveBrief">
        <div data-toggle="modal" data-target="#showcandidatePopup" (click)="ShowCandidatePopup(candidate)">
          <p class="brief"
            innerHtml="{{candidate?.ExecutiveBrief | highlight: WordsToHighlight | createCandidateLink:'#'}}"></p>
          <a class="SeeMoreBrief" title="{{candidate.JobTitle}} CV Details"
            routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="candidate?.JobTitle | cvSearchDetailUrl:candidate?.FormattedAddress:candidate?.Id"> see details</a>
        </div>
        
    <div class="row m-0 mt-2">
      <div class="col-lg-12 col-md-12 col-sm-12 p-0 text-right BtnWrapper">
        <app-connect-user *ngIf="candidate?.CandidateTypeId != 4" [CandidateType]="candidate?.CandidateType"
          [UserConnection]="candidate.Connection" [ConnectedToUserId]="candidate?.Id" [CandidateName]="candidate?.FirstName"
          [CandidateJobTitle]="candidate?.JobTitle" [CompanyVerificationId]="CompanyVerificationId"></app-connect-user>



        <a class="btn btn-sm btn-success download" role="button" (click)="DownloadCV(candidate.Id,candidate.CandidateTypeId)"
          title="{{candidate?.DownloadedOn?'Already downloaded':'Download this CV'}}">
        {{candidate?.DownloadedOn?'Downloaded':'Download CV'}}</a>
        <a class="btn btn-link btn-sm button-sm btn-no-shadow gray-border"
          *ngIf="candidate?.Skills || candidate?.Industries || candidate?.Qualifications" data-toggle="collapse"
          href="#cand{{candidate?.Id}}" role="button" aria-expanded="false" aria-controls="Two" (click)="OnClickSeeMoreExperties(candidate?.Id,candidate)">
          {{candidate?.IsExpertiesExpended ? 'See less' : 'Skills'}} <i class="fas rotate-icon" [ngClass]="{'fa-angle-down' : candidate?.IsExpertiesExpended, 'fa-angle-up' : !candidate?.IsExpertiesExpended }"></i>
          <span class="accicon collapse" id="cand{{candidate?.Id}}"><i aria-hidden="true" id="cand{{candidate?.Id}}"></i></span>
        </a>    
      </div>
      <!-- <div class="col-lg-6 col-md-12 col-sm-12 p-0">
                   
      </div> -->
      <!---->
      <div class="col-lg-12 col-md-12 col-sm-12 p-0 collapse mt-2" id="cand{{candidate?.Id}}">
        <div class="card p-2 CvCard-SIQ">
          <div class="row m-0 mt-3">
            <div class="col-md-12 col-sm-12 col-xs-12 bgwhite p-0">
              <div class="accordion" id="JobFilters">
                <!--Industries-->
                <div class="card border-bottom border-white" *ngIf="candidate?.Industries && candidate?.Industries.length>0">
                  <div class="card-header bg-primary">
                    <span class="accicon" *ngIf="candidate.IsMoreIndustries"><i class="fas fa-angle-down rotate-icon text-white"
                        *ngIf="candidate.IsExpandInd" (click)="ExpnadIndustries('ind', candidate)"></i>
                      <i class="fas fa-angle-up rotate-icon text-white" *ngIf="!candidate.IsExpandInd"
                        (click)="ExpnadIndustries('ind', candidate)"></i>
                    </span>

                    <p [ngClass]="{'h30': (candidate.IsMoreIndustries && candidate.IsExpandInd == false)}">
                      <span class="title" class="title" [ngClass]="{'h30': IsExpandInd == false}"><i class="fa fa-industry"></i> Industries</span>
                      <span>
                        <a class="badge badge-pill badge-light industry-pill" *ngFor="let industry of candidate?.Industries"                          
                          routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="industry | cvSearchDetailUrl" target="_blank">{{industry}}
                        </a>
                      </span>
                    </p>
                  </div>
                </div>
                <!--Job Titles-->
                <div class="card border-bottom border-white" *ngIf="candidate?.JobTitles && candidate?.JobTitles.length > 0">
                  <div class="card-header bg-primary">
                    <span class="accicon" *ngIf="candidate.IsMoreJobTitles">
                      <i class="fas fa-angle-down rotate-icon text-white" *ngIf="candidate.IsExpandJobTitles"
                      (click)="ExpnadIndustries('jobtitle', candidate)"></i>
                      <i class="fas fa-angle-up rotate-icon text-white" *ngIf="!candidate.IsExpandJobTitles"
                      (click)="ExpnadIndustries('jobtitle', candidate)"></i>
                    </span>
      
                    <p [ngClass]="{'h30': !candidate.IsExpandJobTitles}">
                      <span class="title" [ngClass]="{'h30': (candidate.IsMoreJobTitles && IsExpandJobTitles == false)}"><i class="fa fa-user-tie"></i> Job Titles</span>
                      <span>
                        <a class="badge badge-pill badge-light jobTitle-pill"                          
                          *ngFor="let title of candidate?.JobTitles;" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="title | cvSearchDetailUrl" target="_blank">
                          {{title}}
                        </a>
                      </span>
                    </p>
                  </div>
                </div>
                <!--Skills-->
                <div class="card border-bottom border-white" *ngIf="candidate?.Skills && candidate?.Skills.length>0">
                  <div class="card-header bg-primary"> 
                    <span class="accicon" *ngIf="candidate.IsMoreSkills">
                      <i class="fas fa-angle-down rotate-icon text-white" *ngIf="candidate.IsExpandSkill" (click)="ExpnadIndustries('skill', candidate)">
                      </i>
                      <i class="fas fa-angle-up rotate-icon text-white" *ngIf="!candidate.IsExpandSkill"
                        (click)="ExpnadIndustries('skill', candidate)">
                      </i>
                    </span>
      
                    <p [ngClass]="{'h30': !candidate.IsExpandSkill}">
                      <span class="title" [ngClass]="{'h30': (candidate.IsMoreSkills && IsExpandSkill == false)}"><i class="fa fa-certificate"></i> Skills</span>
                      <span>
                        <a class="badge badge-pill badge-light skill-pill"                          
                          *ngFor="let skill of candidate?.Skills;" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="skill | cvSearchDetailUrl" target="_blank">
                          {{skill}}
                        </a>
                      </span>
                    </p>
                  </div>
                </div>
                <!--Qualifications-->
                <div class="card border-bottom border-white" *ngIf="candidate?.Degrees && candidate?.Degrees.length>0">
                  <div class="card-header bg-primary" data-toggle="collapse" href="#Qualifications" aria-expanded="false">
                    <span class="accicon" *ngIf="candidate.IsMoreDegrees">
                      <i class="fas fa-angle-down rotate-icon text-white" *ngIf="candidate.IsExpandDegree"
                        (click)="ExpnadIndustries('degree', candidate)">
                      </i>
                      <i class="fas fa-angle-up rotate-icon text-white" *ngIf="!candidate.IsExpandDegree"
                        (click)="ExpnadIndustries('degree', candidate)"></i>
                    </span>

                    <p [ngClass]="{'h30': !candidate.IsExpandDegree}">                       
                      <span class="title" [ngClass]="{'h30': (candidate.IsMoreDegrees && IsExpandDegree == false)}"><i class="fa fa-graduation-cap"></i> Qualifications
                      </span>
                      <span>
                        <a class="badge badge-pill badge-light qualification-pill"                         
                          *ngFor="let degree of candidate?.Degrees" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" [routerLink]="degree | cvSearchDetailUrl" target="_blank">{{degree}}
                        </a>
                      </span>
                    </p>
                  </div>
                  <!--Skills-->        
                </div>                
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="watermark" *ngIf="IsRecentApplicantPage && candidate?.IsActive == false">Deleted</div>
  </div>
  <!--CV Card-->
</div>


<cv-short-list-modal *ngIf="WishListCandidateId" [CandidateId]="WishListCandidateId"
  (WishListChange)="OnWishListChange($event)"></cv-short-list-modal>
<share-cv-modal *ngIf="RecommendationModel?.RecommendedUrl" [RecommendationModel]="RecommendationModel">
</share-cv-modal>
<smart-notes-modal *ngIf="NoteCandidateId && NoteCandidateId == candidate?.Id" [CandidateId]="NoteCandidateId" [NoteTypeId]="NoteTypeId" [NoteForTypeId]="NoteForTypeId" 
 (OnNoteChange)="OnNoteChange($event)"></smart-notes-modal>
<candidate-request-popup *ngIf="ShowCandidateRequestPopup"
  (ShowCandidateRequestPopupEvent)="ShowCandidateRequestPopupEvent($event)"
  [ShowCandidateRequestPopup]="ShowCandidateRequestPopup" [Candidate]="Candidate" [CandidateTitle]="CandidateTitle">
</candidate-request-popup>
<recommendations-modal *ngIf="RecommendedId" [RecommendedId]="RecommendedId" [RecommendationTypeId]="RecommendationType.Candidate" (RecommendationChange)="OnRecommendationChange($event)"></recommendations-modal>