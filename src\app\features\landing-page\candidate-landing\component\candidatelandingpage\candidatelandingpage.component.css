.MainCanvas {
  padding: 15px;
  background: #fff;
  margin-bottom: 15px;
  border-radius: 8px;
  box-shadow: rgb(0 0 0 / 24%) 0px 3px 8px;
}

.top-userform .carousel-control-prev {
  left: 37%;
}
.top-userform .carousel-control-next {
  right: 37%;
}

.PageBg {
  background: #f6faff;
}
.apply4u-choose-section{
  padding-left: 1rem;
  padding-right: 1rem;
}
.apply4u-choose-section .avatar{
  width: 100px;
  height: 100px;
}
.JobSearchForm {
  padding: 40px 0;
}

.BannerBg {
  padding: 15px 0;
  background: #0e1b5d;
  display: inline-block;
}

.ratings-container {
  padding: 10px 0;
  background: linear-gradient(to top, #f6faff, #0e1b5d);
  padding-top: 5rem !important
}
.ratings-container1 {
  padding: 10px 0;
  padding-top: 5rem !important
}
.midline {
  width: 60px;
  border-top: 2px solid #0e1b5d;
}

.banner-wrap {
  position: relative;
}

.banner-container {
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100%;
}

.banner {
  opacity: .2;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
}

.JobSearchForm h1 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 29px;
  color: #fff;
  margin-bottom: 10px;
  font-weight: 600 !important;
}

.JobSearchForm h2 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 18px;
  color: #fff;
  margin-bottom: 10px;
  font-weight: 600 !important;
}

.JobSearchForm label {
  color: #fff;
  font-size: 14px;
}

.JobSearchForm p {
  color: #fff;
}

.ratings-container {
  padding: 10px 0;
  background: #dbe9ff;
}

.ratings-box {
  color: #0e1b5d;
}

.ratings-box h1 {
  font-size: 22px;
  margin: 0;
  font-weight: 600 !important;
  line-height: 26px;
}

.ratings-box p {
  margin: 8px 0 0;
}

.CardsWrapper {
  padding: 40px 0px;
}

.CardsWrapper .card {
  text-align: center;
  margin-bottom: 15px;
}

.CardsWrapper .card img {
  width: 100px;
  height: 100px;
  margin-bottom: 15px;
}

.CardsWrapper h1 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 29px;
  color: #000;
  text-align: center;
  margin-bottom: 40px;
  font-weight: 600 !important;
}

.CardsWrapper h1 a {
  color: #223a6b;
  text-decoration: underline;
}

.CardsWrapper h1 a:hover {
  color: #223a6b;
  text-decoration: none;
}

.CardsWrapper .carousel-control-prev {
  top: 10%;
  left: 0px;
}

.CardsWrapper .carousel-control-next {
  top: 10%;
  right: 0px;
}

.CardsWrapper h2 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 18px;
  color: #223a6b;
  text-align: center;
  margin-bottom: 10px;
  font-weight: 600 !important;
}

.CardsWrapper p {
  margin: 0 auto;
  padding: 0;
  font-size: 15px;
}

.liveJobs {
  padding: 40px 0;
  background: #f2f2f2;
  border-bottom: 1px solid #fff;
}

.liveJobs h1 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 29px;
  color: #000;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 600 !important;
}

.liveJobs h2 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 18px;
  color: #fff;
  text-align: center;
  margin-bottom: 10px;
  font-weight: 600 !important;
}

.liveJobs h3 {
  color: #fff;
}

.liveJobs .card {
  margin-bottom: 15px;
  background-color: #0e1b5d;
}

.liveJobs .carousel-indicators li{
  width:12px!important;
  height:12px!important;
  border-radius: 100%;
  position: relative;
  top: 38px;
  background-color: #14C59C;
}

.liveJobs img {
  width: 100%;
  height: auto;
  margin-bottom: 15px;
}

.liveJobs ul {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-bottom: 15px;
}

.liveJobs ul li {
  text-align: center;
}

.liveJobs ul li a {
  color: #333;
  text-decoration: none;
}

.liveJobs ul li a:hover {
  color: #333;
  text-decoration: underline;
}

.liveJobs p {
  color: #333333;
  text-align: center;
}

.FAQs {
  padding: 40px 0px;
}

.FAQs h1 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 29px;
  color: #000;
  text-align: center;
  margin-bottom: 40px;
  font-weight: 600 !important;
}

.FAQs img {
  width: 100%;
  height: auto;
}

.FAQs .accordion {
  width: 100%;
}

.FAQs .card-header .title {
  font-size: 17px;
  color: #000;
}

.FAQs .card-header .accicon {
  float: right;
  font-size: 20px;
  width: 1.2em;
}

.FAQs .card-header {
  cursor: pointer;
  border-bottom: none;
}

.FAQs .card {
  border: 1px solid #ddd;
}

.FAQs .card-body {
  border-top: 1px solid #ddd;
}

.FAQs .card-header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

.Testimonials {
  padding: 40px 0;
  background: #f2f2f2;
  border-bottom: 1px solid #fff;
}

.RegForm input {
  border-radius: 7px;
  padding-right: 60px;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.NewTheme {
  padding: 40px 0;
  background: #0e1b5c;
}

.NewTheme .card {
  border: 1 px solid;
  background-color: #14C59C;
  border-color: #14C59C;
}

.image-2 {
  width: 10% !important;
  height: auto !important;
  position: relative;
  top: -14px;
}

.NewTheme h1 {
  margin: 0;
  padding: 0;
  color: #ffffff;
  font-size: 60px;
  text-align: left;
  font-weight: 900 !important;
}

.NewTheme h2 {
  margin: 0;
  padding: 0;
  color: #fff;
  font-size: 60px;
  text-align: left;
  margin-top: 10px;
  font-weight: 400 !important;
}

.NewTheme span {
  color: #14C59C;
  /* font-size: 50px; */
  text-align: left;
  font-weight: 700 !important;
}

.NewTheme span.Two {
  color: #14C59C;
  font-size: 140px;
  position: absolute;
  top: -36px;
  left: 270px;
  transform: rotate(5deg);
}

.NewTheme h3.NewJobs {
  color: #fff;
  font-size: 30px;
  top: -30px;
  position: relative;
  font-weight: 700 !important;
}

.carousel-indicators li {
  width: 15px !important;
  height: 15px !important;
  border-radius: 100%;
}

.NewTheme .carousel-indicators li {
  background-color: #14C59C !important;
}

.carousel-indicators li {
  background-color: #081039 !important;
}

.TrustedBy {
  padding: 0;
  margin: 0;
  color: #fff;
  font-size: 26px;
  /* New Theme */
  font-weight: 600 !important;
}

.btn btn-light {
  color: #0e1b5c;
  border-color: #343a40;
}

.Testimonials h1 {
  margin: 0;
  padding: 0;
  width: 100%;
  font-size: 29px;
  color: #0e1b5c;
  text-align: center;
  margin-bottom: 25px;
  font-weight: 600 !important;
}

.Testimonials p {
  color: black;
  text-align: center;
}

.bg-blue {
  background-color: #0e1b5d !important;
}

.Testimonials .card {
  padding: 30px;
  background-color: #f8f9fa;
}

.Testimonials .card .carousel-item {
  height: 110px;
}

.Testimonials .card .carousel-caption {
  padding: 0;
  right: 0;
  left: 0;
  top: 0px;
  color: #fff;
}

.Testimonials .card .carousel-caption h2 {
  color: #3d3d3d;
}

.Testimonials .card .carousel-caption p {
  line-height: 30px;
}

.Testimonials .card .carousel-caption .col-sm-3 {
  display: flex;
  align-items: center;
}

.Testimonials .card .carousel-caption .col-sm-9 {
  text-align: left;
}

.Testimonials .navi a {
  text-decoration: none;
}

.Testimonials a>.ico {
  color: #0e1b5d;
  border-radius: 5px;
  background-color: #fff;
}

.Testimonials a:hover>.ico {
  color: #ccc;
  background-color: #222;
}

.Testimonials span.ico {
  padding: 0 8px;
}

.btn-start {
  color: #fff;
  background: #14C59C;
  border-color: #14C59C;
  border-radius: 4px;
}

.btn-start:hover {
  color: #fff;
  background: #64e7c9;
  border-color: rgb(109, 218, 154);
}

.field-icon {
  float: right;
  left: -15px;
  margin-top: -35px;
  position: relative;
  z-index: 2;
  cursor: pointer;
}

.margin-top-eye
{
  margin-top: -25px !important;
}

.uploadfiler {
  padding: 2px 10px;
  border-radius: 3px;
  background: #14C59C;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
}
.uploadfiler:hover {
  color: #fff;
  background: #64e7c9;
  border-color: rgb(109, 218, 154);
}

.top-userform .text-underline {
  color: #14C59C !important;
  font-weight: 400;
}
.top-userform .text-underline:hover {
  text-decoration: underline;
  color: #30dab2 !important; 
}

.modal-body {
  padding: 25px 30px 30px;
}

.img-responsive {
  width: 80%;
  height: auto;
  display: block;
}

.modal-content {
  border-radius: 10px;
}

.backgroundImage {
  background: linear-gradient(150deg, rgba(21, 43, 89, 1) 0%, rgba(50, 76, 128, 1) 50%, rgba(112, 145, 179, 1) 100%);
}

.carousel-control-prev,
.carousel-control-next {
  bottom: 80%;
  color: #0e1b5c;
  font-size: 2.3em;
}

.slider-h3 {
  color: #000;
  font-weight: 900 !important;
  font-size: 20px;
  margin-bottom: 20px;
}

.cursor {
  cursor: pointer;
}

/* registration  */
.Registration {
  padding-top: 50px;
  background: #ffffff;
}

.Registration h1 {
  margin: 0 0 25px;
  padding: 0;
  width: 100%;
  font-size: 29px;
  color: #000;
  text-align: center;
  font-weight: 600 !important
}

.Registration h1 span {
  color: #0e1b5c;
  font-weight: bold !important;
}

.Registration h2 {
  padding: 0;
  margin: 0 0 15px 0;
  color: #0e1b5c;
  font-size: 30px;
  text-align: center;
}

.Registration h2 span {
  color: #0e1b5c;
  font-weight: bold !important;
}

.Registration p span {
  color: #0e1b5c;
  font-weight: bold;
}

.Registration p strong {
  color: #434343;
  font-weight: bold;
}

.Registration a {
  background-color: #0e1b5c !important;
  border-color: #0e1b5c;
}

/* slider */
.card {
  margin: 0 auto;
  border: none;
}

.size {
  width: 800px;
  margin: 0 auto;
}

.card .carousel-item {
  min-height: 190px;
}

.card2 .carousel-item {
  min-height: 120px;
}

.CardBorder {
  padding: 40px 20px;
  text-align: center;
  border-radius: 6px;
  background: #0e1b5c;
}

.card .carousel-caption {
  padding: 0;
  right: 0 !important;
  left: 0 !important;
  top: 0px;
  color: #ffffff;
  background-color: #0e1b5c;
  border: 1px solid #0e1b5c;
  min-height: 215px;
  padding: 15px;
  border-radius: 4px;
  position: relative;
  margin-bottom: 10px;
}

.card .carousel-caption h2 {
  color: #14c59c !important;
  font-size: 25px;
  font-weight: 600 !important;
  margin-bottom: 10px;
}

.card .carousel-caption .col-sm-3 {
  display: flex;
  align-items: center;
}

.card .carousel-caption .col-sm-9 {
  text-align: left;
}

.smallest {
  color: #14C59C;
  font-weight: 600 !important;
}

.card .carousel-control-prev,
.card .carousel-control-next {
  color: #3d3d3d !important;
  opacity: 1 !important;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  background-image: none;
  color: #0e1b5c;
  font-size: 14px;
  background-color: #14C59C;
  height: 32px;
  line-height: 32px;
  width: 32px;
  border-radius: 50%;
}

.carousel-control-prev-icon:hover,
.carousel-control-next-icon:hover {
  opacity: 0.85;
}

.carousel-control-prev {
  left: 40%;
  top: 110%;
}

.carousel-control-next {
  right: 40%;
  top: 110%;
}

.midline {
  width: 60px;
  border-top: 2px solid #0e1b5d;
}

.midline2 {
  width: 60px;
  border-top: 2px solid #fff;
}

.a4u-banner {
  padding: 20px 125px;
  background-color: #0e1b5c;
}

.Trustpilot {
  padding: 50px 0;
}

.form-control {
  margin-bottom: 10px;
}
.top-userform h1.form-heading{
  font-size: 35px;
  font-weight: 600;
}
.top-userform label,
.top-userform label p{
  font-weight: 400 !important;
}

.contact-links{
  color: #14C59C;
}

.contact-links:hover{
  text-decoration: underline;
}

@media (min-width: 320px) and (max-width: 575px) {
  .carousel-control-prev {
    left: 35%;
    top: 105%;
  }

  .carousel-control-next {
    right: 35%;
    top: 105%;
  }

  .card .carousel-caption h3 {
    margin-top: 0;
    font-size: 16px;
    font-weight: 700;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .carousel-control-prev {
    left: 35%;
    top: 105%;
  }

  .carousel-control-next {
    right: 35%;
    top: 105%;
  }
}

@media (min-width: 767px) and (max-width: 991px) {

  .card .carousel-caption h3 {
    margin-top: 0;
    font-size: 16px;
    font-weight: 700;
  }

}
@media (min-width: 992px) and (max-width: 1100px) {

  .NewTheme .top-section-heading span.Two {
    left: 289px !important;
    font-size: 200px;
  }
  .NewTheme .top-section-heading span.a4u {
    font-size: 90px;
  }
  .NewTheme .top-section-heading h1 {
    font-size: 70px;
  }
}

@media screen and (max-width:400px) {
  .NewTheme .top-section-heading h1{
    font-size: 38px;
  }
  .NewTheme .top-section-heading span.Two {
    color: #14C59C;
    font-size: 133px;
    position: absolute;
    top: -36px;
    left: 251px;
    transform: rotate(5deg);
  }
  .NewTheme .top-section-heading span.a4u {
    top: -25px;
    color: #14C59C;
    font-size: 75px;
    position: relative;
  }
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .all-section-maxwidth{
    max-width: 90vw;
  }
  .PageBg {
    padding: 40px 0;
  }

  .JobSearchForm {
    padding: 40px 15px;
  }

  .ratings-box img {
    width: 70px;
    height: auto;
  }

  .liveJobs {
    padding: 40px 0;
  }

  .liveJobs .btn {
    color: #0e1b5d;
  }

  .CardsWrapper {
    padding: 40px 15px;
  }

  .Testimonials .card .carousel-item {
    height: 265px;
  }

  .TrustedBy {
    font-size: 15px;
    margin-top: 0px !important;
  }

  .RegForm {
    margin: 0 10px;
  }

  /* new theme */
  .NewTheme {
    padding: 20px 0 50px;
  }

  .NewTheme h1 {
    font-size: 43px;
  }

  .NewTheme h2 {
    font-size: 60px;
  }

  .NewTheme h3 {
    color: #fff;
    font-size: 30px;
    position: relative;
    font-weight: 100 !important;
  }

  .NewTheme h3.NewJobs {
    color: #fff;
    font-size: 30px;
    top: -30px;
    position: relative;
    font-weight: 700 !important;
  }

  .NewTheme span.a4u {
    top: -25px;
    color: #14C59C;
    font-size: 80px;
    position: relative;
  }

  .NewTheme span.Two {
    color: #14C59C;
    font-size: 140px;
    position: absolute;
    top: -36px;
    left: 270px;
    transform: rotate(5deg);
  }

  .NewTheme span.Experts {
    top: -10px;
    color: #fff;
    font-size: 26px;
    position: relative;
  }

  .NewTheme h3.Jobs10 {
    color: #fff;
    font-size: 39px;
    top: -20px;
    position: relative;
    font-weight: 700 !important;
  }

  .NewTheme h1.CallUs {
    color: #fff;
    font-size: 45px;
  }

  .NewTheme h2.CallUs a {
    color: #14C59C;
    font-size: 40px;
  }

  .NewTheme img {
    width: 25%;
    height: auto;
    margin-bottom: 15px;
  }

  .NewTheme .card {
    padding: 10px 0px;
  }

  .NewTheme .card label {
    font-size: 15px;
    font-weight: 700 !important;
    color: #0e1b5d;
    margin-top: 0px;
    margin-bottom: 5px !important;
  }

  .NewTheme .card a {
    color: #0e1b5d;
    font-size: 15px !important;
    text-decoration: underline;
    margin-left: 5px;
  }

  .NewTheme .carousel-indicators {
    bottom: -35px;
  }

  .ratings-container {
    padding: 10px 0;
    background: linear-gradient(to top, #f6faff, #0e1b5d);
    padding-top: 0rem !important;
  }

  .container {
    max-width: 550px !important;
  }

  .size {
    width: 100%;
    margin: 0 auto;
  }

  .CardsWrapper[_ngcontent-apply4u-web-c71] p[_ngcontent-apply4u-web-c71] {
    margin: 0;
    padding: 0;
    font-size: 15px;    
    word-break: break-word;
}


}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .all-section-maxwidth{
    max-width: 90vw;
  }
  .PageBg {
    padding: 0px 0;
  }

  .JobSearchForm {
    padding: 40px 15px;
  }

  .liveJobs {
    padding: 40px 0;
  }

  .CardsWrapper {
    padding: 40px 15px;
  }

  .Testimonials .card .carousel-item {
    height: 150px;
  }

  .TrustedBy {
    font-size: 15px;
    margin-top: 0px !important;
  }

  .RegForm {
    margin: 0 10px;
  }

  /* New Theme */
  .NewTheme {
    padding: 20px 0 50px;
  }

  .NewTheme h1 {
    font-size: 73px;
  }

  .NewTheme h2 {
    font-size: 60px;
  }

  .NewTheme h3 {
    color: #fff;
    font-size: 48px;
    position: relative;
    font-weight: 100 !important;
  }

  .NewTheme h3.NewJobs {
    color: #fff;
    font-size: 30px;
    top: -30px;
    position: relative;
    font-weight: 700 !important;
  }

  .NewTheme span.a4u {
    top: -20px;
    color: #14C59C;
    font-size: 100px;
    position: relative;
  }

  .NewTheme span.Two {
    color: #14C59C;
    font-size: 240px;
    position: absolute;
    top: -83px;
    left: 385px;
    transform: rotate(5deg);
  }

  .NewTheme span.Experts {
    top: -10px;
    color: #fff;
    font-size: 30px;
    position: relative;
  }

  .NewTheme h3.Jobs10 {
    color: #fff;
    font-size: 52px;
    top: -20px;
    position: relative;
    font-weight: 700 !important;
  }

  .NewTheme h1.CallUs {
    color: #fff;
    font-size: 60px;
  }

  .NewTheme h2.CallUs a {
    color: #14C59C;
    font-size: 60px;
  }

  .NewTheme img {
    width: 15%;
    height: auto;
    margin-bottom: 10px;
  }

  .NewTheme .card {
    padding: 10px 0px;
  }

  .NewTheme .card label {
    font-size: 15px;
    font-weight: 700 !important;
    color: #0e1b5d;
    margin-top: 0px;
    margin-bottom: 5px !important;
  }

  .NewTheme .card .btn-sm {
    border-radius: 20px;
    background-color: #0e1b5d;
    color: fff !important;
    font-size: 15px !important;
  }

  .NewTheme .card a {
    color: #0e1b5d;
    font-size: 15px !important;
    text-decoration: underline;
    margin-left: 5px;
  }

  .NewTheme .carousel-indicators {
    bottom: -35px;
  }

  .ratings-container {
    padding: 10px 0;
    background: linear-gradient(to top, #f6faff, #0e1b5d);
    padding-top: 0rem !important;
  }

  .container {
    max-width: 600px !important;
  }

  .size {
    width: 100%;
    margin: 0 auto;
  }

}

/* Medium devices (landscape tablets, 768px and down) */
@media only screen and (max-width: 768px) {
  .TrustedBy {
    margin-top: 30px;
  }

  .size {
    width: 100%;
    margin: 0 auto;
  }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .CardsWrapper .card .card-body {
    min-height: 330px;
  }

  .Testimonials .card .carousel-item {
    height: 220px;
  }

  .liveJobs .card-body {
    min-height: 350px;
  }

  .TrustedBy {
    margin-top: 210px !important;
  }

  .RegForm {
    margin: 0 10px;
  }

  .NewTheme img {
    width: 32%;
    height: auto;
    margin-bottom: 10px;
  }

  .ratings-container {
    padding: 10px 0;
    background: linear-gradient(to top, #f6faff, #0e1b5d);
    padding-top: 0rem !important;
  }

  .container {
    max-width: 900px !important;
  }

  .size {
    width: 100%;
    margin: 0 auto;
  }

}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .all-section-maxwidth{
    max-width: 900px;
  }
  .RegForm {
    margin: 0 10px;
  }

  .NewTheme img {
    width: 25%;
    height: auto;
    margin-bottom: 10px;
  }

  .container {
    max-width: 1000px !important;
    display: contents;
  }

  .size {
    width: 800px;
    margin: 0 auto;
  }

  .top-userform .carousel-control-prev {
    left: 38%;
  }
  .top-userform .carousel-control-next {
    right: 38%;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .all-section-maxwidth{
    max-width: 1366px;
    width: 90%;
  }
  .PageBg {
    padding: 0;
    background: #f6faff;
  }

  .container {
    max-width: 1280px;
  }

  .JobSearchForm {
    padding: 20px 85px;
  }

  .liveJobs {
    padding: 40px 0;
  }

  .CardsWrapper {
    padding: 40px 0px;
  }

  .Testimonials .card .carousel-item {
    height: 110px;
  }

  .TrustedBy {
    margin-top: 60px;
  }

  .RegForm {
    margin: 0 150px;
  }

  /* new theme */
  .NewTheme {
    padding: 20px 0;
  }

  /* .NewTheme .HomeSlider {
    margin: 0 140px;
  } */

  .NewTheme .HomeSlider img {
    width: 48%;
    height: auto;
  }

  .NewTheme .card {
    margin: 0 140px;
    border-radius: 20px;
  }

  .NewTheme .card label {
    font-size: 20px;
    font-weight: 700 !important;
    color: #0e1b5d;
  }

  .NewTheme .card a {
    color: #0e1b5d;
    font-size: 15px !important;
    text-decoration: underline;
    margin-left: 5px;
  }

  .ratings-container {
    padding: 10px 0;
    background: linear-gradient(to top, #f6faff, #0e1b5d);
    padding-top: 50px !important;
  }

  .container {
    max-width: 1280px !important;
    display: block;
  }

  .size {
    width: 800px;
    margin: 0 auto;
  }

}

/* Extra extra large devices (extra large desktops, 1400px and up)  */
@media (min-width: 1400px) {
  .NewTheme {
    padding: 20px 0 10px;
  }

  .NewTheme .carousel-indicators {
    bottom: -60px !important;
  }

  .NewTheme h2 {
    font-size: 60px;
  }

  .NewTheme h3.NewJobs {
    color: #fff;
    font-size: 36px;
    top: -35px;
    position: relative;
    font-weight: 700 !important;
  }

  .NewTheme span.Experts {
    top: 0px;
    color: #fff;
    font-size: 30px;
    position: relative;
  }

  .NewTheme h3.Jobs10 {
    color: #fff;
    font-size: 52px;
    top: 0px;
    position: relative;
    font-weight: 700 !important;
  }

  .NewTheme h1.CallUs {
    color: #fff;
    font-size: 60px;
  }

  .NewTheme h2.CallUs a {
    color: #14C59C;
    font-size: 60px;
  }

  .TrustWrap {
    left: 100px;
    position: relative;
  }

  .TrustedBy {
    margin-top: 210px !important;
  }

  .NewTheme .HomeSlider img {
    width: 35%;
    height: auto;
  }

  .size {
    width: 800px;
    margin: 0 auto;
  }

  .CardsWrapper p {
    margin: 0 auto;
    padding: 0;
    font-size: 15px;
    max-width: 900px!important;
    width: 100%;
    word-break: break-all;
  }


  .top-userform .carousel-control-prev {
    left: 39%;
  }
  .top-userform .carousel-control-next {
    right: 39%;
  }

}

/* .header-cv-btn{
  font-size: 1rem;
} */


.top-userform h2{
  font-size: 24px !important;
  margin-top: 0;
}
.top-userform .carousel-control-prev-icon,
.top-userform .carousel-control-next-icon {
  background-image: none;
  color: #0e1b5c;
  font-size: 14px;
  background-color: #14C59C;
  height: 32px;
  line-height: 32px;
  width: 32px;
  border-radius: 50%;
  text-align: center;
}

.top-userform .carousel-control-prev,
.top-userform .carousel-control-next {
  opacity: 1 !important;
  top: 100% !important;
}