import { Component, OnInit,ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CandidateType } from '@apply4u/models/cvsearchresult';
import { Duration } from '@apply4u/models/duration';
import { Jobcategory } from '@apply4u/models/jobcategory';
import { Jobtype } from '@apply4u/models/jobtype';
import { Positiontype } from '@apply4u/models/positiontype';
import { SearchDuration } from '@apply4u/models/search/search-duration';
import { SearchParams } from '@apply4u/models/search/search-params';
import { Sector } from '@apply4u/models/sector';
import { UserAutoSaveSearch } from '@apply4u/models/user-save-search/user-auto-save-search';
import { ContextService } from '@apply4u/services/context-service';
import { AutoSearchService } from '@apply4u/services/data-services/auto-search.service';
import { LoadingService } from '@apply4u/services/loading.service';
import { MasterDataService } from '@apply4u/services/masterdata.service';
import { SearchQueryParamService } from '@apply4u/services/search/search-queryparam.service';
import { SearchService } from '@apply4u/services/search/search.service';
import { SweetAlertService } from '@apply4u/services/sweet-alert.service';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { UserSaveSearchService } from '@apply4u/services/user-searches/user-save-search.service';
import { EditSavedSearchMessages } from '@apply4u/shared/constant/messages/edit-saved-search-messages';
import { SearchDurations } from '@apply4u/shared/constant/search-within/search-durations';
import { IsEqualStrings, ToLowerCase } from '@apply4u/shared/helpers/common/string-helpers';
import { IsAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNullOrEmpty, IsZeroNumber } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { FormatSeoUrl } from '@apply4u/shared/helpers/seo-helper';
import { Navigation } from '@apply4u/shared/constant/navigation/navigation';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { timer } from 'rxjs/internal/observable/timer';
import { CommonMessages } from '@apply4u/shared/constant/messages/common-messages';
import { AutoComplete } from '@apply4u/models/autocomplete';
import { DefaultPageNumber} from '@apply4u/shared/constant/constant';
import { JobSearchDurations } from '@apply4u/shared/constant/search-within/job-search-duration';

@Component({
  selector: 'advance-search-modal',
  templateUrl: './advance-search-modal.component.html',
  styleUrls: ['./advance-search-modal.component.css']
})
export class AdvanceSearchModalComponent implements OnInit {  
  SearchParams = new SearchParams();
  AutoSaveSearch = new UserAutoSaveSearch();
  ContextService: ContextService;
  SearchService: SearchService;
  JobTypes: Jobtype[] = [];
  JobCategories: Jobcategory[] = [];
  SalaryDurations: Duration[] = [];
  CandidateTypes: CandidateType[] = [];
  PositionTypes: Positiontype[] = [];
  Sectors: Sector[] = [];
  SearchWithInFilterCollection: SearchDuration[] = SearchDurations;
  JobSearchWithInFilterCollection: SearchDuration[] = JobSearchDurations;
  LocationId: number;
  AutoSaveSearchId: number = 0;
  IsBooleanTabFocused: boolean = true;
  IsQueryStringCreated:boolean = false;
  isSaveButtonClicked: boolean = false;
 // SelectedJobTitles: string[] = [];

  Navigation=Navigation
  @ViewChild('searchForm') searchForm: NgForm;

  constructor(
    private _searchService: SearchService,
    private masterDataService: MasterDataService,
    private contextService: ContextService,
    private routParamsService: ActivatedRoute,
    private router: Router,
    private searchQueryParamService: SearchQueryParamService,
    private toasterService: ToasterMessageService,
    private userSaveSearchService: UserSaveSearchService,
    private loadingService: LoadingService,
    private autoSaveSearchService : AutoSearchService,
    private sweetAlertService: SweetAlertService,
    private localStorageService: LocalstorageService,) {
    this.SearchParams = this._searchService.SearchParams;
    this.ContextService = this.contextService;
    this.SearchService = this._searchService;
  }

  ngOnInit(): void {
    // if (this.contextService.IsJobSeeker) {
    //   this.SearchWithInFilterCollection = [
    //     ...this.SearchWithInFilterCollection,
    //     { Id: 0, Description: "Anytime" }
    //   ];
    // }
    
    this.ReadSearchQueryParams();
    this.SearchService.CheckRadiusDisableStatus();

    const pendingBooleanSearch = this.localStorageService.GetItem('pendingBooleanSearch');
    if (pendingBooleanSearch === 'true') {
      const booleanSearchParams = this.localStorageService.GetItem('booleanSearchParams');

      if (booleanSearchParams) {
        this.SearchParams = JSON.parse(booleanSearchParams);
        this.IsBooleanTabFocused = true;
        this.localStorageService.RemoveItem('pendingBooleanSearch');
        this.localStorageService.RemoveItem('booleanSearchParams');
      }
    }
  }

  ReadSearchQueryParams() {
    this.routParamsService.paramMap.subscribe(param => {
      if (param.has('Id')) {
        this.AutoSaveSearchId = parseInt(param.get('Id'));
        this.GetSaveSearchById(this.AutoSaveSearchId);
      } else {
        this.routParamsService.queryParamMap
          .subscribe((queryParamMap) => {
            this.searchQueryParamService.GetAdvanceSearchQueryParamsMap(this.routParamsService.snapshot);
            this.InitializeFilters();

            if (IsNotNullOrEmpty(this.SearchParams.AllofTheseWords)) {
              this.SearchParams.Keywords = this.SearchService.BooleanQueryStringBuilder(this.SearchParams);
            }
          });
      }
    });
  }

  GetSaveSearchById(searchId: number) {
    if (IsNotZero(searchId)) {
      this.loadingService.Show();
      this.userSaveSearchService.GetUserSaveSearchById(this.ContextService.LoggedInUserId, searchId).subscribe({
        next: saveSearch => {
          this.loadingService.Hide();
          this.SwitchTab(saveSearch.IsBooleanSearch);
          this.InitializeSearchParams(saveSearch);
          this.InitializeFilters();
        }, error: error => {
          this.loadingService.Hide();
        }
      });
    }
  }

  InitializeSearchParams(saveSearch: UserAutoSaveSearch) {
    this.SearchParams = new SearchParams();
    this.SearchParams.Keywords = saveSearch.KeyWords;
    this.SearchParams.AllofTheseWords = saveSearch.AllofTheseWords;
    this.SearchParams.ExactPhrae = saveSearch.ExactPharse;
    this.SearchParams.AnyOfTheseWords = saveSearch.AnyofTheseWords;
    this.SearchParams.NoneOfTheseWords = saveSearch.NoneofTheseWords;
    this.SearchParams.Location = saveSearch.LocationText;
    this.SearchParams.LocationId = saveSearch.LocationId;
    this.SearchParams.SalaryDuration = saveSearch.SalaryDurationId ?? 0;
    this.SearchParams.SalaryFrom = saveSearch.SalaryFrom;
    this.SearchParams.SalaryTo = saveSearch.SalaryTo;
    this.SearchParams.IsActive = saveSearch.IsActive;
    this.SearchParams.EmailFrequencyId = saveSearch.EmailFrequencyId ?? 2;

    // if (IsNotNullOrEmpty(this.SearchParams.Keywords)) {
    //   if (this.contextService.IsJobSeeker) {
    //     this.SelectedJobTitles.push(this.SearchParams.Keywords);
    //     this.SearchParams.Keywords = null;
    //   }
    // }

    if (IsNotNullOrEmpty(saveSearch.JobTypeIds)) {
      this.SearchParams.JobTypes = saveSearch.JobTypeIds.split('|').join(',');
    }
    
    if (IsNotNullOrEmpty(saveSearch.JobCategoryIds)) {
      this.SearchParams.JobStatus = saveSearch.JobCategoryIds.split('|').join(',');
    }

    if (IsNotNullOrEmpty(saveSearch.PositionTypeIds)) {
      this.SearchParams.PositionTypes = saveSearch.PositionTypeIds.split('|').join(',');
    }

    if (IsNotNullOrEmpty(saveSearch.CandidateTypeIds)) {
      this.SearchParams.ProfileTypes = saveSearch.CandidateTypeIds.split('|').join(',');
    }

    if(IsNotNullOrEmpty(this.SearchParams.Location)){
      this.SearchService.SearchParams.Location = this.SearchParams.Location;
      this.SearchService.CheckRadiusDisableStatus();
    }

    this.SearchParams.SectorId = saveSearch.SectorId;
    this.SearchParams.SearchWithIn = saveSearch.SearchWithInLast ?? -1;
    this.SearchParams.Radius = saveSearch.Radius;
    this.SearchParams.SearchInJobTitle = saveSearch.IsSearchInTitle;
    this.SearchParams.IsFilterSearch = saveSearch.IsFilterApplyed;

    if (saveSearch.IsJobSearch) {
      this.SearchParams.IsJobSearch = true;
    } else if (saveSearch.IsCvSearch) {
      this.SearchParams.IsJobSearch = false;
    }

    this.SearchParams.IsBooleanSearch = saveSearch.IsBooleanSearch;
    this.SearchParams.IsSearchByLocation = saveSearch.IsSearchByLocation;
    this.SearchParams.IsSearchBySector = saveSearch.IsSearchByIndustry;
  }

  InitializeFilters(): void {
    if (this.ContextService.IsJobSeeker) {
      this.masterDataService.getJobType().subscribe(jobTypes => {
        let jtypes = jobTypes.filter(f => f.Id != 1);
        let jobTypesSort = IsAny(jtypes) ? JSON.parse(JSON.stringify(jtypes)) : [];

        this.JobTypes = jobTypesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? -1 : 1);

        if (IsNotNullOrEmpty(this.SearchParams.JobTypes)) {
          let selectedJobTypes = this.SearchParams.JobTypes.split(',').map(d => parseInt(d));

          this.JobTypes.filter(d => selectedJobTypes.indexOf(d.Id) > -1).forEach(jobType => {
            jobType.isChecked = true;
          });
        }
      });

      this.masterDataService.getJobCategory().subscribe(jobCategories => {
        let jobCategoriesSort = IsAny(jobCategories) ? JSON.parse(JSON.stringify(jobCategories)) : [];

        this.JobCategories = jobCategoriesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? -1 : 1);

        if (IsAny(this.JobCategories)) {
          this.JobCategories.unshift({ Id: 500, Description: 'Any', isChecked: false, EnumKey: 'Any' });
        }

        if (IsNotNullOrEmpty(this.SearchParams.JobStatus)) {
          let selectedJobCategories = this.SearchParams.JobStatus.split(',').map(d => parseInt(d));

          this.JobCategories.filter(d => selectedJobCategories.indexOf(d.Id) > -1).forEach(jobCategory => {
            jobCategory.isChecked = true;
          });
        }
      });
    }

    this.masterDataService.getDuration().subscribe(salaryDurations => {
      this.SalaryDurations = IsAny(salaryDurations) ? JSON.parse(JSON.stringify(salaryDurations)) : [];
    });

    if (this.ContextService.IsRecruiter) {
      this.masterDataService.getCandidateTypes().subscribe(candidateTypes => {
        let candidateTypesSort = IsAny(candidateTypes) ? JSON.parse(JSON.stringify(candidateTypes)) : [];

        this.CandidateTypes = candidateTypesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? -1 : 1);

        if (IsNotNullOrEmpty(this.SearchParams.ProfileTypes)) {
          let selectedProfileTypes = this.SearchParams.ProfileTypes.split(',').map(d => parseInt(d));

          this.CandidateTypes.filter(d => selectedProfileTypes.indexOf(d.Id) > -1).forEach(profileType => {
            profileType.IsChecked = true;
          });
        }
      });

      this.masterDataService.getPositionType().subscribe(positionTypes => {
        let postypes = positionTypes.filter(pos => pos.Id != 8);
        let positionTypesSort = IsAny(postypes) ? JSON.parse(JSON.stringify(postypes)) : [];

        this.PositionTypes = positionTypesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? -1 : 1);

        if (IsNotNullOrEmpty(this.SearchParams.PositionTypes)) {
          let selectedPositionTypes = this.SearchParams.PositionTypes.split(',').map(d => parseInt(d));

          this.PositionTypes.filter(d => selectedPositionTypes.indexOf(d.Id) > -1).forEach(positionType => {
            positionType.IsChecked = true;
          });
        }
      });
    }

    this.LoadSectors();
  }

  LoadSectors() {
    this.masterDataService.getSector(null).subscribe(sectors => {
      this.Sectors = IsAny(sectors) ? sectors : [];

      if (IsAny(this.Sectors)) {
        let index = this.Sectors.filter(sector => ToLowerCase(sector.SectorName) == ToLowerCase(this.SearchService.SearchParams.SectorName))[0];

        if (IsNotNull(index)) {
          this.AutoSaveSearch.SectorId = index.Id;
        }
        if (IsNotNullOrEmpty(this.SearchService.SearchParams.SectorName) && !IsEqualStrings(this.SearchService.SearchParams.SectorName, "selectPlease")) {
          this.OnSectorChange(this.SearchService.SearchParams.SectorName);
        } else {
          this.SearchParams.SectorName = 'selectPlease';
        }
      }

      if (IsNotZero(this.AutoSaveSearchId)) {
        if (IsAny(this.Sectors)) {
          if (IsNotZero(this.SearchParams.SectorId)) {
            let index = this.Sectors.filter(sector => sector.Id == this.SearchParams.SectorId)[0];

            if (IsNotNull(index)) {
              this.OnSectorChange(index.SectorSeoName);
            }
          }
        }
      }
    });
  }

  OnSectorChange(sectorName: string): void {
    sectorName = ToLowerCase(sectorName);
    this.SearchParams.SectorName = sectorName.trim();
    return;
  }

  OnSelectionChangeSector(): void {
    if (IsNotNullOrEmpty(this.SearchParams.SectorName)) {
      if (IsAny(this.Sectors)) {
        let selectedSector = this.Sectors.filter(sector => sector.SectorSeoName.toLowerCase() === this.SearchParams.SectorName);

        if (IsAny(selectedSector)) {
          this.SearchParams.SectorIds = selectedSector[0].Id.toString();
        }
      }
    }
  }

  OnJobTypeChange(typeId: number): void {
    if (IsAny(this.JobTypes)) {
      if (typeId == 1) {
        this.JobTypes.filter(d => d.Id != 1).forEach(element => {
          element.isChecked = false;
        });
      } else if (typeId != 1) {
        this.JobTypes.filter(d => d.Id == 1).forEach(element => {
          element.isChecked = false;
        });
      }

      let selectedJobTypes = this.JobTypes.filter(d => d.isChecked);

      if (selectedJobTypes.length > 0) {
        this.SearchParams.JobTypes = selectedJobTypes.map(d => d.Id).join(',');
      } else {
        this.SearchParams.JobTypes = "";
      }
    }
  }

  OnJobCategoryChange(typeId: number): void {
    if (IsAny(this.JobCategories)) {
      if (typeId == 500) {
        this.JobCategories.filter(d => d.Id != 500).forEach(element => {
          element.isChecked = false;
        });
      } else if (typeId != 500) {
        this.JobCategories.filter(d => d.Id == 500).forEach(element => {
          element.isChecked = false;
        });
      }

      let selectedJobCategories = this.JobCategories.filter(d => d.isChecked);

      if (selectedJobCategories.length > 0) {
        this.SearchParams.JobStatus = selectedJobCategories.map(d => d.Id).join(',');
      } else {
        this.SearchParams.JobStatus = "";
      }
    }
  }

  OnPositionTypeChange(positiontypeId: number): void {
    if (IsAny(this.PositionTypes)) {
      if (positiontypeId == 8) {
        this.PositionTypes.filter(d => d.Id != 8).forEach(element => {
          element.IsChecked = false;
        });
      } else if (positiontypeId != 8) {
        this.PositionTypes.filter(d => d.Id == 8).forEach(element => {
          element.IsChecked = false;
        });
      }
    }

    let selectedPositionTypes = this.PositionTypes.filter(d => d.IsChecked);

    if (selectedPositionTypes.length > 0) {
      this.SearchParams.PositionTypes = selectedPositionTypes.map(d => d.Id).join(',');
    } else {
      this.SearchParams.PositionTypes = "";
    }
  }

  OnProfileTypeChange(profiletypeId: number): void {
    let selectedProfileTypes = this.CandidateTypes.filter(d => d.IsChecked);

    if (selectedProfileTypes.length > 0) {
      this.SearchParams.ProfileTypes = selectedProfileTypes.map(d => d.Id).join(',');
    } else {
      this.SearchParams.ProfileTypes = "";
    }
  } 

  OnClearFiltersHandler(): void {
    this.SearchService.SearchParams = new SearchParams();
    this.SearchParams = new SearchParams();

    this.PositionTypes.forEach(element => {
      element.IsChecked = false;
    });

    this.JobTypes.forEach(element => {
      element.isChecked = false;
    });

    this.JobCategories.forEach(element => {
      element.isChecked = false;
    });

    this.CandidateTypes.forEach(element => {
      element.IsChecked = false;
    });

    this.SearchParams.Keywords = "";
    this.SearchParams.Location = "";
    this.SearchParams.Radius = 0;
    this.SearchParams.SalaryDuration = 0;
    this.SearchParams.SearchWithIn = -1;
    this.SearchParams.SectorName = "selectPlease";
    this.SearchParams.SalaryFrom = null;
    this.SearchParams.SalaryTo = null;
    this.SearchParams.AllofTheseWords = "";
    this.SearchParams.AnyOfTheseWords = "";
    this.SearchParams.ExactPhrae = "";
    this.SearchParams.NoneOfTheseWords = "";
    this.SearchParams.SearchInJobTitle = false;
    this.SearchService.SearchParams.Location = "";
    this.SearchService.CheckRadiusDisableStatus();
  }

  InitializeSaveSearch() {
    if (IsNotNull(this.SearchParams)) {     
      this.AutoSaveSearch.AllofTheseWords = this.SearchParams.AllofTheseWords;
      this.AutoSaveSearch.ExactPharse = this.SearchParams.ExactPhrae;
      this.AutoSaveSearch.AnyofTheseWords = this.SearchParams.AnyOfTheseWords;
      this.AutoSaveSearch.NoneofTheseWords = this.SearchParams.NoneOfTheseWords;
      this.AutoSaveSearch.LocationText = this.SearchParams.Location;
      this.AutoSaveSearch.SalaryDurationId = this.SearchParams.SalaryDuration ?? 0;
      this.AutoSaveSearch.SalaryFrom = this.SearchParams.SalaryFrom;
      this.AutoSaveSearch.SalaryTo = this.SearchParams.SalaryTo;
      this.AutoSaveSearch.JobTypeIds = this.SearchParams.JobTypes;
      this.AutoSaveSearch.JobCategoryIds = this.SearchParams.JobStatus;
      this.AutoSaveSearch.PositionTypeIds = this.SearchParams.PositionTypes;
      this.AutoSaveSearch.CandidateTypeIds = this.SearchParams.ProfileTypes;
      this.AutoSaveSearch.SearchWithInLast = this.SearchParams.SearchWithIn ?? -1;
      this.AutoSaveSearch.Radius = this.SearchParams.Radius;
      this.AutoSaveSearch.IsSearchInTitle = this.SearchParams.SearchInJobTitle;
      this.AutoSaveSearch.IsFilterApplyed = this.SearchParams.IsFilterSearch;
      this.AutoSaveSearch.IsSearchByLocation = this.SearchParams.IsSearchByLocation;
      this.AutoSaveSearch.IsSearchByIndustry = this.SearchParams.IsSearchBySector;
      this.AutoSaveSearch.UserId = this.contextService.LoggedInUserId;
      this.AutoSaveSearch.SavedSearchTypeId = 2;
      this.AutoSaveSearch.LocationId = this.LocationId;
      this.AutoSaveSearch.IsBroadMatch = false;
      this.AutoSaveSearch.CreatedOn = new Date();
      this.AutoSaveSearch.KeyWords = this.SearchParams.Keywords;

      // if (this.contextService.IsJobSeeker) {
      //   if (IsAny(this.SelectedJobTitles)) {
      //     // Create Boolean Query againt User Selected Multiple Job Titles.
      //     this._searchService.SearchParams.Keywords = "";
      //     if (this.SelectedJobTitles.length > 1) {
      //       this._searchService.SearchParams.Keywords = this.autoSaveSearchService.CreateMultiJobTitlesBooleanQuery(this.SelectedJobTitles);
      //     } else if (this.SelectedJobTitles.length == 1) {
      //       if (!this.SelectedJobTitles[0].includes('AND')) {
      //         this._searchService.SearchParams.Keywords = this.autoSaveSearchService.CreateMultiJobTitlesBooleanQuery(this.SelectedJobTitles);
      //       }else {
      //         this._searchService.SearchParams.Keywords = this.SelectedJobTitles[0];
      //       }
      //     }

      //     this.SearchParams.Keywords = this._searchService.SearchParams.Keywords;
      //   }        
      // }
      
      if(IsZeroNumber(this.AutoSaveSearchId)){
        this.AutoSaveSearch.IsActive = true;
      }else {
        this.AutoSaveSearch.IsActive = this.SearchParams.IsActive;
      }     
      
      this.AutoSaveSearch.IsDefault = false;
      this.AutoSaveSearch.EmailFrequencyId = this.SearchParams.EmailFrequencyId ?? 2;

      if (this.IsBooleanTabFocused && IsNullOrEmpty(this.AutoSaveSearch.AllofTheseWords)) {
        this.AutoSaveSearch.IsBooleanSearch = true;
      }else if (this.IsBooleanTabFocused && IsNotNullOrEmpty(this.AutoSaveSearch.AllofTheseWords)){
        this.AutoSaveSearch.IsBooleanSearch = false;
      } else {
        this.AutoSaveSearch.IsBooleanSearch = false;
      }

      if (IsNotNullOrEmpty(this.AutoSaveSearch.JobTypeIds)) {
        this.AutoSaveSearch.JobTypeIds = this.AutoSaveSearch.JobTypeIds.replace(/,/g, '|');
      }

      if (IsNotNullOrEmpty(this.AutoSaveSearch.JobCategoryIds)) {
        this.AutoSaveSearch.JobCategoryIds = this.AutoSaveSearch.JobCategoryIds.replace(/,/g, '|');
      }

      if (IsNotNullOrEmpty(this.AutoSaveSearch.PositionTypeIds)) {
        this.AutoSaveSearch.PositionTypeIds = this.AutoSaveSearch.PositionTypeIds.replace(/,/g, '|');
      }

      if (IsNotNullOrEmpty(this.AutoSaveSearch.CandidateTypeIds)) {
        this.AutoSaveSearch.CandidateTypeIds = this.AutoSaveSearch.CandidateTypeIds.replace(/,/g, '|');
      }

      if (this.contextService.IsJobSeeker) {
        this.AutoSaveSearch.IsJobSearch = true;
        this.AutoSaveSearch.IsCvSearch = false;
      } else {
        this.AutoSaveSearch.IsCvSearch = true;
        this.AutoSaveSearch.IsJobSearch = false;
      }

      if (IsAny(this.Sectors)) {
        if (IsNotNullOrEmpty(this.SearchParams.SectorName) && this.SearchParams.SectorName != 'selectPlease') {
           let selectedSector = this.Sectors.filter(sector => sector.SectorSeoName.toLowerCase() === this.SearchParams.SectorName);
           if (IsAny(selectedSector)) {
            this.AutoSaveSearch.SectorId = selectedSector[0].Id;
            }
        }
      }
    }
  }

  OnSaveSearchHandler(): void {
    if(!!this.SearchParams.SalaryFrom && !!this.SearchParams.SalaryTo && this.SearchParams.SalaryTo < this.SearchParams.SalaryFrom){
      this.toasterService.Error(CommonMessages.OnValidationSalaryFromAndToError,null)
      return
    }

    if (this.isSaveButtonClicked) {
      return; // If clicked, do nothing
    }

    this.isSaveButtonClicked = true;
    if (this.contextService.IsUserLoggedIn) {
      this.InitializeSaveSearch();
      if (IsNotZero(this.AutoSaveSearchId)) {
        this.AutoSaveSearch.Id = this.AutoSaveSearchId;
        // call to update save search
        this.loadingService.Show();
        let modelToUpdate = {...this.AutoSaveSearch};

        if(!modelToUpdate.SalaryDurationId || modelToUpdate.SalaryDurationId == 0){
          modelToUpdate.SalaryDurationId = null;
        }

        this.userSaveSearchService.UpdateUserSaveSearch(modelToUpdate.Id,this.ContextService.LoggedInUserId,modelToUpdate).subscribe({
          next: update => {
            this.loadingService.Hide();
            this.sweetAlertService.UpdatedAlert(null, EditSavedSearchMessages.OnUpdatingSavedSearchSuccess);
            // Redirect user to Save Search Listing page
            let listPageUrl = '/my-auto-searches';
            this.router.navigateByUrl(listPageUrl);
          }, error: error => {
            this.loadingService.Hide();
          }
        });
      } else {
        // call Data service Method to Create New Save Search.
        if(IsNotNullOrEmpty(this.AutoSaveSearch.KeyWords) || IsNotNullOrEmpty(this.AutoSaveSearch.AllofTheseWords)){

          let modelToInsert = {...this.AutoSaveSearch};

          if(!modelToInsert.SalaryDurationId || modelToInsert.SalaryDurationId == 0){
            modelToInsert.SalaryDurationId = null;
          }

          this.autoSaveSearchService.CreateConfirmAutoSaveSearchhandler(modelToInsert, true);
        }else {
          this.toasterService.Error('Please fill job title and location.');
        }
      }
    } else {
      this.localStorageService.SetItem('booleanSearchParams', JSON.stringify(this.SearchParams));
      this.localStorageService.SetItem('pendingBooleanSearch', 'true');
      this.sweetAlertService.ConfirmAlertWithBtnText('Please log in first to save this auto search.', 'You are not logged in !', 'Log in').then((result) => {
        if (result.isConfirmed) {
          this.contextService.SignInRedirect();
        }
      })
    }
    timer(1000).subscribe(() => {
      this.isSaveButtonClicked = false;
    }); // Adjust the delay time as needed
  }

  OnSearchHandler(): void {
    if(!!this.SearchParams.SalaryFrom && !!this.SearchParams.SalaryTo && this.SearchParams.SalaryTo < this.SearchParams.SalaryFrom){
      this.toasterService.Error(CommonMessages.OnValidationSalaryFromAndToError,null)
      return
    }

    if (this.contextService.IsJobSeeker) {
      if(this.IsBooleanTabFocused && this.IsQueryStringCreated){
        this._searchService.SearchParams.IsBooleanSearch = true;
        this._searchService.SearchParams.IsBoolean = false;
        this._searchService.SearchParams.IsSimpleSearch = true;
      }
      
      // if (IsAny(this.SelectedJobTitles)) {
      //   // Create Boolean Query againt User Selected Multiple Job Titles.
      //   this._searchService.SearchParams.Keywords = null;       
      //   this._searchService.SearchParams.Keywords = this.autoSaveSearchService.CreateMultiJobTitlesBooleanQuery(this.SelectedJobTitles);
      //   this._searchService.SearchParams.IsSimpleSearch = true;
      // } 

      this._searchService.SearchParams.IsFilterSearch = true;
      this._searchService.SearchParams.PageNumber = DefaultPageNumber;
      this.router.navigateByUrl(this.SearchService.GetJobSearchUrl());
    } else {
      if(this.IsBooleanTabFocused && this.IsQueryStringCreated){
        this._searchService.SearchParams.IsBooleanSearch = true;
        this._searchService.SearchParams.IsBoolean = false;
        this._searchService.SearchParams.IsSimpleSearch = true;
      }

      this._searchService.SearchParams.IsFilterSearch = true;
      this._searchService.SearchParams.PageNumber = DefaultPageNumber;
      this.router.navigateByUrl(this.SearchService.GetCVSearchUrl(this._searchService.SearchParams));
    }
  }

  OnClickTab(isBooleanSearch: boolean = true): void {
    this.IsBooleanTabFocused = isBooleanSearch;
  }

  SwitchTab(isboolean: boolean) {
    if (isboolean) {
      let element = document.getElementById('Boolean-Search-tab');

      if (IsNotNull(element)) {
        element.click();
      }
    } else {
      let element = document.getElementById('Help-With-Search-tab');

      if (IsNotNull(element)) {
        element.click();
      }
    }
  }

  CreateBooleanSearchQueryString(): void {
    if (IsNotNull(this.SearchParams)) {
      let booleanQuery = this.SearchService.BooleanQueryStringBuilder(this.SearchParams);

      if (IsNotNullOrEmpty(booleanQuery)) {
        this.SearchParams.Keywords = booleanQuery;
        this.IsQueryStringCreated = true;
        this.SwitchTab(true);
      } else {
        this.toasterService.Error("Please provide keywords to create boolean search.");
      }
    } 
  }

  scrollToTips() {
    const tipsElement = document.getElementById('accordionCol1');
    
    if (tipsElement) {
        tipsElement.scrollIntoView({ behavior: 'smooth' });
    }
  }

  closePopup() {
    if (this.contextService.IsRecruiter) {
    const formValue = this.searchForm.value;
    if (this.isFormEmpty(formValue)) {
   
    } else {
      if (confirm("Are you sure you want to discard changes?")) {
        this.searchForm.resetForm();
      }
    }
  }
  
}

  isFormEmpty(formValue: any): boolean {
    if (this.contextService.IsRecruiter) {
      for (const field in formValue) {
        if (typeof formValue[field] === 'string' && formValue[field].trim() !== '') {
          return false;
        }
      }
      
      return true;
    }
  }

  OnCancelFiltersHandler() {
    this.router.navigate([this.Navigation.MyAutoSearches]);
  }

  OnLocationSelectionChangedEvent(event: AutoComplete): void {
    if (IsNotNull(event)) {
      this.SearchParams.Location = event.DisplayValue;
      this._searchService.SearchParams.Location = event.DisplayValue;

      if (IsNotNullOrEmpty(event.Key)) {
        this.LocationId = parseInt(event.Key);
      }

      if (IsNotZero(event.DefaultRadius)) {        
        this.SearchParams.Radius = event.DefaultRadius;
        this._searchService.SearchParams.Radius = event.DefaultRadius;
        this._searchService.CheckRadiusDisableStatus();
      } else {
        this.SearchParams.Radius = 0;
        this._searchService.SearchParams.Radius = 0;
        this._searchService.CheckRadiusDisableStatus();
      }
    }
  }

  // JobTitlesSelectionChanged(selectedJobTitles: string[]) {
  //   this.SelectedJobTitles = selectedJobTitles;
  // }
  

}
