.timeline {
    margin-bottom: 50px;
    position: relative;
  }
  .timeline:before {
    background-color: #dee2e6;
    bottom: 0;
    content: "";
    left: 50%;
    position: absolute;
    top: 30px;
    width: 2px;
    z-index: 0;
  }
  .timeline .time-show {
    margin-bottom: 30px;
    margin-top: 30px;
    position: relative;
  }
  .timeline .timeline-box {
    background: #fff;
    display: block;
    margin: 15px 0;
    position: relative;
    padding: 20px;
    border-radius: 0.25rem;
    box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.16),
      0 2px 6px 0 rgba(0, 0, 0, 0.12);
  }
  .timeline .timeline-album {
    margin-top: 12px;
  }
  .timeline .timeline-album a {
    display: inline-block;
    margin-right: 5px;
  }
  .timeline .timeline-album img {
    height: 36px;
    width: auto;
    border-radius: 3px;
  }
  @media (min-width: 768px) {
    .timeline .time-show {
      margin-right: -69px;
      text-align: right;
    }
    .timeline .timeline-box {
      margin-left: 45px;
    }
    .timeline .timeline-icon {
      background: #dee2e6;
      border-radius: 50%;
      display: block;
      height: 20px;
      left: -54px;
      margin-top: -10px;
      position: absolute;
      text-align: center;
      top: 50%;
      width: 20px;
    }
    .timeline .timeline-icon i {
      color: #98a6ad;
      font-size: 13px;
      position: absolute;
      left: 4px;
      margin-top: 1px;
    }
    .timeline .timeline-desk {
      display: table-cell;
      vertical-align: top;
      width: 50%;
    }
    .timeline-item {
      display: table-row;
    }
    .timeline-item:before {
      content: "";
      display: block;
      width: 50%;
    }
    .timeline-item .timeline-desk .arrow {
      border-bottom: 12px solid transparent;
      border-right: 12px solid #fff !important;
      border-top: 12px solid transparent;
      display: block;
      height: 0;
      left: -12px;
      margin-top: -12px;
      position: absolute;
      top: 50%;
      width: 0;
    }
    .timeline-item.timeline-item-left:after {
      content: "";
      display: block;
      width: 50%;
    }
    .timeline-item.timeline-item-left .timeline-desk .arrow-alt {
      border-bottom: 12px solid transparent;
      border-left: 12px solid #fff !important;
      border-top: 12px solid transparent;
      display: block;
      height: 0;
      left: auto;
      margin-top: -12px;
      position: absolute;
      right: -12px;
      top: 50%;
      width: 0;
    }
    .timeline-item.timeline-item-left .timeline-desk .album {
      float: right;
      margin-top: 20px;
    }
    .timeline-item.timeline-item-left .timeline-desk .album a {
      float: right;
      margin-left: 5px;
    }
    .timeline-item.timeline-item-left .timeline-icon {
      left: auto;
      right: -56px;
    }
    .timeline-item.timeline-item-left:before {
      display: none;
    }
    .timeline-item.timeline-item-left .timeline-box {
      margin-right: 45px;
      margin-left: 0;
      text-align: right;
    }
  }
  @media (max-width: 767.98px) {
    .timeline .time-show {
      text-align: center;
      position: relative;
    }
    .timeline .timeline-icon {
      display: none;
    }
  }
  .timeline-sm {
    padding-left: 110px;
  }
  .sub-timeline-sm {
    padding-left: 14px;
  }
  .timeline-sm .timeline-sm-item {
    position: relative;
    padding-bottom: 20px;
    padding-left: 40px;
    border-left: 2px solid #dee2e6;
  }
  .timeline-sm .timeline-sm-item:after {
    content: "";
    display: block;
    position: absolute;
    top: 2px;
    left: -10px;
    width: 19px;
    height: 19px;
    border-radius: 50%;
    /* background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="1.2em" height="1.2em" viewBox="0 0 25 24" fill="none"%3E%3Cpath fill-rule="evenodd" clip-rule="evenodd" d="M12.5 24c6.627 0 12-5.373 12-12s-5.373-12-12-12S.5 5.373.5 12s5.373 12 12 12ZM7.812 13.312a1.312 1.312 0 1 1 0-2.624 1.312 1.312 0 0 1 0 2.624Zm4.688 0a1.312 1.312 0 1 1 0-2.624 1.312 1.312 0 0 1 0 2.624Zm4.688 0a1.312 1.312 0 1 1 0-2.624 1.312 1.312 0 0 1 0 2.624Z" fill="currentColor" /%3E%3C/svg%3E'); */
    background: #fff;
    border: 2px solid #6658dd;
  }
  .timeline-sm .timeline-sm-item .timeline-sm-date {
    position: absolute;
    left: -104px;
  }
  @media (max-width: 420px) {
    .timeline-sm {
      padding-left: 0;
    }
    .timeline-sm .timeline-sm-date {
      position: relative !important;
      display: block;
      left: 0 !important;
      margin-bottom: 10px;
    }
  }

  
.Card-Profile {
    width: 100%;
    padding: 15px;
    background: #fff;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .Card-Profile button {
    left: 50%;
    top: 150px;
    width: 30px;
    height: 30px;
    z-index: 100;
    border: none;
    font-size: 15px;
    padding-top: 3px;
    background: #fff;
    text-align: center;
    border-radius: 50%;
    position: absolute;
    margin: 0 0 0 -20px;
  }
  .Card-Profile ul.List1 {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .Card-Profile ul.List1 li {
    width: 36px;
    height: 36px;
    float: left;
    margin: 5px 10px;
    font-size: 14px;
    padding: 6px 0;
    border-radius: 50%;
    text-align: center;
  }
  .Card-Profile ul.List1 li:nth-child(1) {
    color: #fff;
  }
  .Card-Profile ul.List1 li:nth-child(2) {
    color: #fff;
  }
  .Card-Profile ul.List1 li:nth-child(3) {
    color: #fff;
  }
  .Card-Profile h1 {
    margin: 0;
    padding: 0;
    font-size: 24px;
    font-weight: 600!important;
    text-align: center;
  }
  .Card-Profile p {
    margin: 0;
    padding: 0;
    font-size: 14px;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-weight: 600!important;
  }
  .Card-Profile p a {
    color: #0070e9;
    text-decoration: none;
  }
  .Card-Profile p a:hover {
    color: #0056b3;
    text-decoration: none;
  }
  .Card-Profile ul.List2 {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .Card-Profile ul.List2 li {
    width: 36px;
    height: 36px;
    float: left;
    margin: 5px 10px;
    font-size: 14px;
    padding: 0;
    text-align: center;
  }
  .Card-Profile ul.List2 li a{
   color: #000;
  }
  .UserImg {
    top: 0px;
    width: 180px;
    height: 180px;
    margin: 0 auto;
    position: relative;
    border-radius: 50%;
    margin-bottom: 15px;
    background-position: 50%;
    background-repeat: no-repeat;
    border: 5px solid #e1e1e1;
    object-fit: cover;
  }
  .video-thumb-play-new {
    position: relative;
    color: #fff;
    left: -21%;
    cursor: pointer;
  }
  .btn-secondary:not(:disabled):not(.disabled).active,
  .btn-secondary:not(:disabled):not(.disabled):active,
  .show>.btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #005dc0;
    border-color: #005dc0;
  }
  .btn-group-toggle .btn-secondary:not(.active) {
    color: #333;
    background-color: #fff;
    border-color: #005dc0;
  }
  .btn-group-toggle .btn-secondary:hover {
    color: #333;
    background-color: #005dc010;
  }
  .PageBg {
    background: #f6faff;
  }
  
  .Profile-Visibility {
    width: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .Profile-Visibility h2 {
    font-size: 18px;
    padding: 0 0 10px 0;
    font-weight: 600!important;
    border-bottom: 1px solid #d3d3d3;
  }
  .Profile-Visibility h2 span{
    float: right;
    margin-right: 10px;
  }
  .Profile-Visibility ul {
    padding: 0;
    width: 100%;
    list-style: none;
    margin: 0px !important;
  }
  .Profile-Visibility ul li {
    width: 100%;
    font-size: 14px;
  }
  .Profile-Visibility ul li i {
    color: #4cbd6c;
    font-size: 15px;
    width: 25px;
    text-align: center;
  }
  .Profile-Visibility ul li a {
    color: #333;
    padding: 10px 0px;
    text-decoration: none;
    display: block;
  }
  .Profile-Visibility ul li a:hover {
    color: #4285f4;
    padding: 10px 0px;
    text-decoration: none;
    background: #ececec;
  }
  .Profile-Visibility .DropDown {
    width: 100%;
    margin: 15px;
  }
  .btn-secondary:not(:disabled):not(.disabled).active,
  .btn-secondary:not(:disabled):not(.disabled):active,
  .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #005dc0;
    border-color: #005dc0;
  }
  .Profile-Visibility .btn-secondary {
    color: #6c757d;
    background-color: #fff;
    border-color: #6c757d;
  }
  .form-check-label {
    font-size: 14px;
  }
  
  .Box-Header{
    cursor: pointer;
    border-bottom: none;
  }
  .Box-Header:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }
  .accordion .Profile-Visibility .card-body {
    padding: 0px!important;
    border-top: 0px!important;
  }
  /*Right Col*/
  .Newcard {
    width: 100%;
  }
  .Newcard .btn{
    font-weight: 600!important;
  }    
  .box2 {
    padding:15px;
    background: #fff;
    border-radius: 8px;
    margin:15px 0px!important;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .nav-pills {
    /* padding: 10px; */
    font-size: 14px;
    /* background: #fff;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; */
  }
  .nav-box {
    padding: 10px;  
    background: #fff;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    margin: 0 !important;
  }
  .card-header {
    height: 35px;
    padding: 10px;
  }
  .IndustriesCont .badge {
    margin-bottom: 5px;
    width: auto !important;
  }
  .IndustriesCont .badge-light {
    font-size: 12px;
    color: #646464;
    font-weight: normal;
    background-color: #f0f0f0;
  }
  .IndustriesCont .badge-pill {
    padding: 5px 10px;
    border-radius: 20px !important;
  }
  .IndustriesCont i {
    top: -1px;
    position: relative;
    margin: 0 0px 0 15px;
    font-size: 10px !important;
  }
  .IndustriesCont span.badge-light a {
    color: #646464;
  }
  .IndustriesCont span.badge-light a:hover {
    color: #4285f4;
  }
  .bannerWrapper {
    padding: 15px;
  }
  
  .Newcard ul li a {
    color: #223a6b;
  }
  .Newcard ul li a.active {
    color: #fff;
  }
  
  .Newcard ul li a.active:hover {
    color: #ccc;
  }
  .nav-pills .nav-link.active,
  .nav-pills .show > .nav-link {
    background-color: #005dc0;
    /* border-bottom: 2px solid #fec400; */
  }
  .Newcard ul li a:hover {
    color: #007bff;
  }
  .details-EMP p span {
    text-align: left;
    width: 58% !important;
  }
  .P-Detail label {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }
  .P-Detail span {
    font-size: 14px;
  }
  .P-Detail span p {
    font-size: 14px;
    white-space: break-spaces;
  }
  .PD .row:nth-child(odd) {
    /* background: #f4f4f4; */
  }

  .PD .row div:last-child hr{
    display: none;
  }
  
  .accordion .card-header .title {
    color: #000;
  }
  .accordion .card-header .accicon {
    /* float: right; */
    font-size: 20px;
    width: 1.2em;
    text-align: center;
  }
  .accordion .card-header {
    cursor: pointer;
    border-bottom: none;
  }
  .accordion .card {
    border: 1px solid #ddd;
  }
  .accordion .card-body {
    padding: 10px;
    border-top: 1px solid #ddd;
  }
  .accordion .card-header:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }

  .PersonalDetails .package-accordian:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }
  .yearlyPayments .yearlyPayments-accordian:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }
  .monthlyPayments .monthlyPayments-accordian:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }
  .monthlyPaymentsDetails .monthlyPayments-Details:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }

  span.accicon {
    /* float: right; */
    position: relative;
  }
  
  /* Current CV */
  .CurrentCV {
    padding-left: 10px;
    padding-right: 10px;
    border-right: 1px solid #e2e4e7;
  }
  .CurrentCV h2 {
    padding: 0px;
    font-size: 18px;
    color: #223a6b;
    margin: 0 0 10px 0;
    font-weight: 600!important;
  }
  .CurrentCV p {
    padding: 0;
    color: #333;
    margin: 10px 0;
    font-size: 14px;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .CurrentCV .btn-outline-primary {
    color: #005dc0;
    border-color: #005dc0;
  }
  .CurrentCV .btn-outline-primary:hover {
    color: #fff;
    background-color: #005dc0;
    border-color: #005dc0;
  }
  .cv-item:not(:last-child) { 
    border-bottom: 1px solid #e2e4e7;
   }
   .cv-scroll {
    height: auto; 
    max-height: 140px; 
    overflow-y: auto; 
    overflow-x: hidden;
   }
  .CurrentCV button:not(:last-child) { 
    margin-right: 10px;
   }
  /* Current CV */
  
  /* Upload New CV */
  .UploadNewCV {
    padding-left: 15px;
  }
  .UploadNewCV h2 {
    padding: 0px;
    font-size: 18px;
    color: #223a6b;
    margin: 0 0 10px 0;
    font-weight: 600!important;
  }
  .UploadNewCV p {
    padding: 0;
    color: #333;
    margin: 10px 0;
    font-size: 14px;
    text-align: left;
  }
  .UploadNewCV .btn{
    background:#005dc0;
    border:1px solid #005dc0;
  }
  .UploadNewCV .btn:hover{
    background: #02468f;
    border:1px solid #02468f;
  }
  .small{
    font-size: 15px;
    font-weight: 400;
  }
  /* Upload New CV */
  
  /* Cv Distribution */
  .CvDistribution{
    padding: 10px;
    margin-right: 10px;
    border-radius: 8px;
    background: #f2f5f9;
    border: 1px solid #e9e9e9;
  }
  .CvDistribution h3 {
    padding: 0px;
    font-size: 18px;
    color: #223a6b;
    margin: 0 0 10px 0;
    font-weight: 600!important;
  }
  .CvDistribution h3 button{
    width: 24px;
    float: right;
    height: 24px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #f2f5f9;
  }
  .CvDistribution h3 button i{
    color: #005dc0;
    margin: 4px 0px;
  }
  .CvDistribution h3 button:hover{
    display: block;
    background: #014d9e;
    color: #fff;
  }
  .CvDistribution p {
    padding: 0;
    color: #333;
    margin: 10px 0;
    font-size: 14px;
    text-align: left;
  }
  .CvDistribution .btn{
    background:#077e36;
    border:1px solid #077e36;
  }
  .CvDistribution .btn:hover{
    background:#42ad60;
    border:1px solid #42ad60;
  }
  /* Cv Distribution */
  
  /* Cv Review */
  .CvReview{
    padding: 10px;
    margin-left: 10px;
    border-radius: 8px;
    background: #f2f5f9;
    border: 1px solid #e9e9e9;
  }
  .CvReview h3 {
    padding: 0px;
    font-size: 18px;
    color: #223a6b;
    margin: 0 0 10px 0;
    font-weight: 600!important;
  }
  .CvReview h3 button{
    width: 24px;
    float: right;
    height: 24px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #f2f5f9;
  }
  .CvReview h3 button i{
    color: #005dc0;
    margin: 4px 0px;
  }
  .CvReview h3 button:hover{
    display: block;
    background: #014d9e;
    color:#fff
  }
  .CvReview p {
    padding: 0;
    color: #333;
    margin: 10px 0;
    font-size: 14px;
    text-align: left;
  }
  .CvReview .btn{
    background:#077e36;
    border:1px solid #077e36;
  }
  .CvReview .btn:hover{
    background:#42ad60;
    border:1px solid #42ad60;
  }
  /* Cv Review */
  
  /* Personal Summary */
  .Summary {  
    position: relative;
    top: 7px;  
    padding: 15px;
    overflow: hidden;
    background: #fff;
    border-radius: 8px;
    /* margin: 15px 0 0 0; */
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .Summary p {
    margin: 0;
    padding: 0 10px;
    font-size: 14px;
    text-align: left;
    white-space: break-spaces;
  }
  .Summary h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .Summary h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background:#fff;
  }
  .Summary h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .Summary h2 button i:hover {
    color: #fff;
  }
  .Summary h2 button:hover {
    display: block;
    color: #fff;
    background: #014d9e;
  }
  /* Personal Summary */
  
  /* Personal Details */
  .PersonalDetails {
    padding: 2px 0px 20px 0px;
    overflow: hidden;
    background: #fff;
    margin: 0px 0 0 0;
    border-radius: 0px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 0px 0px;
  }
  .PerDetailBox {
    border-radius: 6px;
    background: #fff;
    margin-bottom: 10px;
  }
  .P-Detail label {
    display: block;
    /* text-align: right; */
  }
  .PerDetailBox .BoxHeader {
    height: auto;
    overflow: hidden;
    padding: 0 5px 10px 5px;
  }
  .PerDetailBox .BoxHeader h3 {
    margin: 0;
    float: left;
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
  }
  .PerDetailBox .BoxHeader button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    color: #005dc0;;
    font-size: 15px;
    border-radius: 50%;
    text-align: center;
    margin: 0px 0 0 5px;
   background-color: #fff;  
  }
  .PerDetailBox .BoxHeader button:hover {
    display: block;
    background: #014d9e;
    color: #fff;
  }
  .PerDetailBox .BoxHeader span.Icons {
    width: auto;
    float: right;
    font-size: 14px;
    display: inline;
    margin: 3px 0 0 8px;
  }
  .PerDetailBox .BoxHeader span a {
    color: #0070e9;
  }
  .PerDetailBox .BoxHeader span a:hover {
    color: #0260c5;
  }
  .PersonalDetails h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .PersonalDetails h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .PersonalDetails h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .PersonalDetails h2 button i:hover {
    color: #fff;
  }
  .PersonalDetails h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .PersonalDetails .card-header {
    height: auto;
  }
  /* Personal Details */
  
  /* Professional Information */
  .ProInfo {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .ProInfo h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .ProInfo h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .ProInfo h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .ProInfo h2 button i:hover {
    color: #fff
  }
  .ProInfo h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .ProInfo .card-header {
    height: auto;
  }
  /* Professional Information */
  
  /* Looking For Work */
  .LookingWork {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .WorkDetailBox {
    border-radius: 6px;
    background: #fff;
    margin-bottom: 10px;
  }
  .WorkDetailBox .BoxHeader {
    height: auto;
    overflow: hidden;
    padding: 0 5px 10px 5px;
  }
  .WorkDetailBox .BoxHeader h3 {
    margin: 0;
    float: left;
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
  }
  .WorkDetailBox .BoxHeader button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    color: #005dc0;
    font-size: 15px;
    border-radius: 50%;
    text-align: center;
    margin: 0px 0 0 5px;
    background: #fff;
  }
  .WorkDetailBox .BoxHeader button:hover {
    display: block;
    background: #014d9e;
    color:#fff;
  }
  .WorkDetailBox .BoxHeader span.Icons {
    margin: 0;
    width: auto;
    float: right;
    font-size: 14px;
    display: inline;
    margin-left: 8px;
  }
  .WorkDetailBox .BoxHeader span a {
    color: #646464;
  }
  .WorkDetailBox .BoxHeader span a:hover {
    color: #4285f4;
  }
  .LookingWork h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .LookingWork h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .LookingWork h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .LookingWork h2 button i:hover {
    color: #fff;
  }
  .LookingWork h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .LookingWork .card-header {
    height: auto;
  }
  /* Looking For Work */
  
  /* Looking to Recruit */
  .LookingRecruit {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .RecruitDetailBox {
    border-radius: 6px;
    background: #fff;
  }
  .RecruitDetailBox .BoxHeader {
    height: auto;
    overflow: hidden;
    padding: 0 5px 10px 5px;
  }
  .RecruitDetailBox .BoxHeader h3 {
    margin: 0;
    float: left;
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
  }
  .RecruitDetailBox .BoxHeader button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    color: #005dc0;;
    font-size: 15px;
    border-radius: 50%;
    text-align: center;
    margin: 0px 0 0 5px;
    background: #fff
  }
  .RecruitDetailBox .BoxHeader button:hover {
    display: block;
    background: #014d9e;
    color: #fff;
  }
  .RecruitDetailBox .BoxHeader span.Icons {
    margin: 0;
    width: auto;
    float: right;
    font-size: 14px;
    display: inline;
    margin-left: 8px;
  }
  .RecruitDetailBox .BoxHeader span a {
    color: #646464;
  }
  .RecruitDetailBox .BoxHeader span a:hover {
    color: #4285f4;
  }
  .LookingRecruit h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .LookingRecruit h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .LookingRecruit h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .LookingRecruit h2 button i:hover {
    color: #fff
  }
  .LookingRecruit h2 button:hover {
    display: block;
    background: #014d9e;
    color: #fff
  }
  .LookingRecruit .card-header {
    height: auto;
  }
  /* Looking to Recruit */
  
  /* Employment History */
  .EmpHist {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .EmpDetailBox {
    border-radius: 6px;
    background: #fff;
  }
  .EmpDetailBox .BoxHeader {
    height: auto;
    overflow: hidden;
    padding: 0 5px 10px 5px;
  }
  .EmpDetailBox .BoxHeader h3 {
    margin: 0;
    float: left;
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
  }
  .EmpDetailBox .BoxHeader button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    color: #005dc0;
    font-size: 15px;
    border-radius: 50%;
    text-align: center;
    margin: 0px 0 0 5px;
    background: #fff;
  }
  .EmpDetailBox .BoxHeader button:hover {
    display: block;
    background: #014d9e;
    color: #fff
  }
  .EmpDetailBox .BoxHeader span.Icons {
    margin: 0;
    width: auto;
    float: right;
    font-size: 14px;
    display: inline;
    margin-left: 8px;
  }
  .EmpDetailBox .BoxHeader span a {
    color: #646464;
  }
  .EmpDetailBox .BoxHeader span a:hover {
    color: #4285f4;
  }
  .EmpHist h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .EmpHist h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .EmpHist h2 button i {
    color: #014d9e;
    margin: 4px 0px;
  }
  .EmpHist h2 button i:hover {
    color: #fff;
  }
  .EmpHist h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .EmpHist .card-header {
    height: auto;
  }
  /* Employment History */
  
  /* Education History */
  .EducationHist {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .EduDetailBox {
    border-radius: 6px;
    background: #fff;
  }
  .EduDetailBox .BoxHeader {
    height: auto;
    overflow: hidden;
    padding: 0 5px 10px 5px;
  }
  .EduDetailBox .BoxHeader h3 {
    margin: 0;
    float: left;
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
  }
  .EduDetailBox .BoxHeader button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    color: #005dc0;
    font-size: 15px;
    border-radius: 50%;
    text-align: center;
    margin: 0px 0 0 5px;
    background: #fff;
  }
  .EduDetailBox .BoxHeader button:hover {
    display: block;
    background: #014d9e;
    color: #fff;
  }
  .EduDetailBox .BoxHeader span.Icons {
    margin: 0;
    width: auto;
    float: right;
    font-size: 14px;
    display: inline;
    margin-left: 8px;
  }
  .EduDetailBox .BoxHeader span a {
    color: #646464;
  }
  .EduDetailBox .BoxHeader span a:hover {
    color: #4285f4;
  }
  .EducationHist h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .EducationHist h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff
  }
  .EducationHist h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .EducationHist h2 button i:hover {
    color: #fff;
  }
  .EducationHist h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .EducationHist .card-header {
    height: auto;
  }
  /* Education History */
  
  /* Skills & Industries */
  .SkillsAndIndus {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .SkillsAndIndus h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .SkillsAndIndus h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .SkillsAndIndus h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .SkillsAndIndus h2 button:hover {
    display: block;
    background: #014d9e;
    color: #fff;
  }
  .SkillsAndIndus .card-header {
    height: auto;
  }
  /* Skills & Industries */
  
  /* Achievements */
  .Achievements {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .Achievements p {
    margin: 0;
    padding: 0 10px;
    font-size: 14px;
    text-align: left;
    white-space: break-spaces;
  }
  .Achievements h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .Achievements h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .Achievements h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .Achievements h2 button i:hover {
    color: #fff;
  }
  .Achievements h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .Achievements .card-header {
    height: auto;
  }
  /* Achievements */
  
  /* Languages */
  .Languages {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .Languages h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .Languages h2 button {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
  }
  .Languages h2 button i {
    color: #005dc0;
    margin: 4px 0px;
  }
  .Languages h2 button i:hover {
    color: #fff;
    margin: 4px 0px;
  }
  .Languages h2 button:hover {
    display: block;
    background: #014d9e;
  }
  .Languages .card-header {
    height: auto;
  }
  /* Languages */
  
  /* Hobbies & Interests */
  .Hobbies {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  .Hobbies p {
    margin: 0;
    padding: 0 10px;
    font-size: 14px;
    text-align: left;
    white-space: break-spaces;
  }
  .Hobbies h2 {
    margin: 0px;
    border: none;
    font-size: 18px;
    color: #223a6b;
    font-weight: 600!important;
    padding: 0px 10px 15px 10px;
  }
  .Hobbies h2 button, .EditButton {
    width: 28px;
    float: right;
    height: 28px;
    border: none;
    font-size: 15px;
    display: block;
    margin-top: 0px;
    border-radius: 50%;
    text-align: center;
    background: #fff;
    color: #005dc0;
  }
  .Hobbies h2 button i, .EditButton i {
    margin: 4px 0px;
  }
  .Hobbies h2 button:hover, .EditButton:hover {
    display: block;
    background: #014d9e;
    color: #fff;
  }
  .Hobbies .card-header {
    height: auto;
  }
  /* Hobbies & Interests */
  th, tr, td {
    border: none;
    text-align: center;
  }
  /* Posts */
  .Posts {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    margin: 15px 0 0 0;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  }
  /* Posts */
  
  .ImgBanner img{
    width: 100%!important;
    height: auto!important;
    border-radius: 8px;
  }
  /*Right Col*/
  
  /* Modal Styling*/
  .modal-title{
    font-size: 15px;
    color: #223a6b;
    font-weight: 600!important;
  }
  .EditProInfo .form-control{
    margin-bottom: 8px;
  }
  .modal-body h2{
    font-size: 15px;
    color: #223a6b;
    margin: 0 0 10px 0;
    font-weight: 600!important;
  }
  
  
  /* Extra small devices (phones, 600px and down) */
  @media only screen and (max-width: 600px) {
    .PageBg {
      padding: 0 15px;
    }
    .FixMobile{
      padding: 0;
    }
    .MobileFix-M-0{
      margin-right: 0;
    }
    .CurrentCV{
      border-right: 0px;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #e2e4e7;
    }
    .CurrentCV span{
      display: block;
      margin-top: 10px;
    }
    .card-header span {
      padding: 0;
      display: inline-block;
      margin: 0 5px !important;
    }
    .accordion .title {
      font-size: 14px !important;
    }
    .CurrentCv p {
      text-align: center;
    }
    .CurrentCV .btn {
      width: 100%;
      display: block;
      margin-bottom: 6px;
    }
    .UploadNewCV{
      padding: 0;
    }
    .UploadNewCV .btn {
      width: 100%;
      display: block;
    }
    .ExpertScreen {
      margin-top:10px;
    }
    .CvDistribution{
      margin-right:0;
    }
    .CvDistribution .btn {
      width: 100%;
      display: block;
    }
    .CvReview{
      margin-left: 0;
    }
    .CvReview .btn {
      width: 100%;
      display: block;
    }
    .P-Detail label {
      text-align: left;
    }
    .ProInfo h2 {
      font-size: 17px;
    }
    .User-Icon {
      text-align: right;
      position: relative;
      top: -30px;
    }
    .IndustriesCont {
      overflow-x: auto;
    }
    .rightModalBtn{
      width: 100%;
      margin-left: 20px;
    }
  }
  
  /* Small devices (portrait tablets and large phones, 600px and up) */
  @media only screen and (min-width: 600px) {
    .PageBg {
      padding: 0 15px;
    }
    .FixMobile{
      padding: 0;
    }
    .MobileFix-M-0{
      margin-right: 0;
    }
    .CurrentCV{
      border-right: 0px;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #e2e4e7;
    }
    .CurrentCV span{
      display: block;
      margin-top: 10px;
    }
    .card-header span {
      padding: 0;
      display: inline-block;
      margin: 0 5px !important;
    }
    .accordion .title {
      font-size: 14px !important;
    }
    .CurrentCv p {
      text-align: center;
    }
    .CurrentCV .btn {
      width: 100%;
      display: block;
      margin-bottom: 6px;
    }
    .UploadNewCV{
      padding: 0;
    }
    .UploadNewCV .btn {
      width: 100%;
      display: block;
    }
    .ExpertScreen {
      margin-top:10px;
    }
    .CvDistribution{
      margin-right:0;
    }
    .CvDistribution .btn {
      width: 100%;
      display: block;
    }
    .CvReview{
      margin-left: 0;
    }
    .CvReview .btn {
      width: 100%;
      display: block;
    }
    .P-Detail label {
      text-align: left;
    }
  }
  
  /* Medium devices (landscape tablets, 768px and up) */
  @media only screen and (min-width: 768px) {
    .ExpertScreen {
      margin-top: 0px;
    }
    .CvDistribution {
      margin-right: 6px;
    }
    .CvReview {
      margin-left: 6px;
    }
    .User-Icon {
      text-align: right;
      position: relative;
      top: 5px;
     }
  }
  
  /* Large devices (laptops/desktops, 992px and up) */
  @media only screen and (min-width: 992px) {
    .Newcard {
      padding: 0 0 0 15px;
    }
  }
  
  /* Extra large devices (large laptops and desktops, 1200px and up) */
  @media only screen and (min-width: 1200px) {
    .PageBg {
      padding: 0 90px;
      background: #f6faff;
    }
    .FixMobile{
      padding-right: 15px;
    }
    .CvDistribution{
      margin-right:10px;
    }
    .CvReview{
      margin-left: 10px;
    }
    .CurrentCV {
      padding-bottom: 0;
      margin-bottom: 0;
      border-bottom: 0;
      border-right: 1px solid #e2e4e7;
    }
    .CurrentCV span{
      display: inline-block;
    }
    .UploadNewCV {
      padding-left: 15px;
    }
    .CurrentCV .btn {
      width: auto;
      display: inline-block;
    }
    .UploadNewCV .btn {
      width:125px!important;
      display: inline-block;
    }
    .CvDistribution .btn {
      width:105px!important;
      display: inline-block;
    }
    .CvReview .btn {
      width:105px!important;
      display: inline-block;
    }
    .ExpertScreen{
      margin-top: 0;
    }
    .PD label {
      display: block;
      /* text-align: right; */
    }
    .SoftSkillsReport img{
      width: 40%;
    }
    .accordion .card-header .title {
      font-size: 14px!important;
      font-weight: 600!important;
    }
    .Newcard {
      padding: 0px;
    }
  }
  .no-underline:hover {
    text-decoration: none;
  }

  .underline:hover {
    text-decoration: underline;
  }

  .Subscript ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  
  .Subscript ul li {
    color: #333;
    display: block;
    text-decoration: none;
    font-size: 14px;
    line-height: 25px;
    margin: 10px 0px;
    padding-left: 15px;
  }
  
  .Subscript ul li:hover {
    display: block;
    text-decoration: none;
  }

  .SubscriptAddOns ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  
  .SubscriptAddOns ul li {
    color: #333;
    display: block;
    text-decoration: none;
    font-size: 14px;
    line-height: 25px;
    padding-left: 15px;
  }
  
  .SubscriptAddOns ul li:hover {
    display: block;
    text-decoration: none;
  }

  .PaymentDetails ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  
  .PaymentDetails ul li {
    color: #333;
    display: block;
    text-decoration: none;
    background: #f2f2f2;
    border-bottom: 1px solid #ccc;
    font-size: 14px;
    line-height: 25px;
    padding: 10px;
  }

  .bg-blue {
    padding: 8px;
    background-color: #0e1b5d !important;
  }
  [data-tooltip] {
    position: relative;
    z-index: 5;
    cursor: default;
    white-space: initial;
    text-align: left !important;
  }
  /* Hide the tooltip content by default */
  
  [data-tooltip]:before,
  [data-tooltip]:after {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: progid: DXImageTransform.Microsoft.Alpha(Opacity=0);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }
  
  /* Position tooltip above the element */
  
  
  
  [data-tooltip]#radius:before {
    bottom: auto;
  }
  
  /* Triangle hack to make tooltip look like a speech bubble */
  
  [data-tooltip]:after {
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    width: 0;
    border-top: 5px solid #000;
    border-top: 5px solid hsla(0, 0%, 20%, 0.9);
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    content: "";
    font-size: 0;
    line-height: 0;
  }
  
  /* Show tooltip content on hover */
  
  [data-tooltip]:hover:before,
  [data-tooltip]:hover:after {
    visibility: visible;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: progid: DXImageTransform.Microsoft.Alpha(Opacity=100);
    opacity: 1;
  }
  
  /* Extra small devices (phones, 600px and down) */
  @media only screen and (max-width: 600px) {
    [data-tooltip]:before {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-bottom: 5px;
      margin-left: -35px;
      padding: 7px;
      width: 160px;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
      background-color: #000;
      background-color: hsla(0, 0%, 20%, 0.9);
      color: #fff;
      content: attr(data-tooltip);
      text-align: center;
      font-size: 12px;
      line-height: 1.2;
    }
  }
  
  /* Small devices (portrait tablets and large phones, 600px and up) */
  @media only screen and (min-width: 600px) {
    [data-tooltip]:before {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-bottom: 5px;
      margin-left: -35px;
      padding: 7px;
      width: 160px;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
      background-color: #000;
      background-color: hsla(0, 0%, 20%, 0.9);
      color: #fff;
      content: attr(data-tooltip);
      text-align: center;
      font-size: 12px;
      line-height: 1.2;
    }
  }
  /* Medium devices (landscape tablets, 768px and up) */
  @media only screen and (min-width: 768px) {   
    [data-tooltip]:before {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-bottom: 5px;
      margin-left: -50px;
      padding: 7px;
      width: 160px;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
      background-color: #000;
      background-color: hsla(0, 0%, 20%, 0.9);
      color: #fff;
      content: attr(data-tooltip);
      text-align: center;
      font-size: 12px;
      line-height: 1.2;
    }
  }
    
  /* Large devices (laptops/desktops, 992px and up) */
  @media only screen and (min-width: 992px) {   
    [data-tooltip]:before {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-bottom: 5px;
      margin-left: -60px;
      padding: 7px;
      width: 160px;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
      background-color: #000;
      background-color: hsla(0, 0%, 20%, 0.9);
      color: #fff;
      content: attr(data-tooltip);
      text-align: center;
      font-size: 12px;
      line-height: 1.2;
    }
  }
    
  /* Extra large devices (large laptops and desktops, 1200px and up) */
  @media only screen and (min-width: 1200px) {
    [data-tooltip]:before {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-bottom: 5px;
      margin-left: -80px;
      padding: 7px;
      width: 160px;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
      background-color: #000;
      background-color: hsla(0, 0%, 20%, 0.9);
      color: #fff;
      content: attr(data-tooltip);
      text-align: center;
      font-size: 12px;
      line-height: 1.2;
    }
  }
  
  