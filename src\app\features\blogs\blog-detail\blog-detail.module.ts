import { NgModule } from '@angular/core';
import { BlogDetailRoutingModule } from './blog-detail-routing.module';
import { CommonModule } from '@angular/common';
import { LocalDateTimePipe } from '@apply4u/shared/pipes/local-date-time.pipe';
import { TimeagoPipe } from '@apply4u/shared/pipes/timeago.pipe';
import { FormsModule } from '@angular/forms';
import { ShareCompanyModalModule } from '@apply4u/shared/components/share-company-modal/share-company-modal.module';
import { BlogCardModule } from '@apply4u/features/blogs/shared/blog-card/blog-card.module';
import { CategoryModule } from '@apply4u/features/blogs/shared/category/category.module';
import { PopularPostModule } from '@apply4u/features/blogs/shared/popular-post/popular-post.module';
import { RecentPostModule } from '@apply4u/features/blogs/shared/recent-post/recent-post.module';
import { SearchPostModule } from '@apply4u/features/blogs/shared/search-post/search-post.module';
import { TagModule } from '@apply4u/features/blogs/shared/tag/tag.module';
import { BreadCrumbSeoModule } from '@apply4u/shared/components/breadcrumb-seo/breadcrumb-seo.module';
import { SafeHtmlPipe } from '@apply4u/shared/pipes/safehtml.pipe';

@NgModule({
  declarations: [BlogDetailRoutingModule.components],
  imports: [
    CommonModule,
    FormsModule,
    BlogDetailRoutingModule,
    SafeHtmlPipe,
    LocalDateTimePipe,
    TimeagoPipe,
    ShareCompanyModalModule,
    BlogCardModule,
    CategoryModule,
    PopularPostModule,
    RecentPostModule,
    SearchPostModule,
    TagModule,
    BreadCrumbSeoModule
  ]
})
export class BlogDetailModule { }
