import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BlogCardComponent } from './blog-card.component';
import { FormsModule } from '@angular/forms';
import { ShareCompanyModalModule } from '@apply4u/shared/components/share-company-modal/share-company-modal.module';
import { RouterModule } from '@angular/router';

@NgModule({
  declarations: [
    BlogCardComponent
  ],
  imports: [
    CommonModule,
    FormsModule,    
    ShareCompanyModalModule,
    RouterModule
  ],exports:[
    BlogCardComponent
  ]
})
export class BlogCardModule { }
