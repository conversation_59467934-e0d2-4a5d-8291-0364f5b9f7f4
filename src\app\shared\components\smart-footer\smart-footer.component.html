<div class="container-fluid footerBg" id="footer">
  <div class="container">
    <footer class="footer" *ngIf="ContextService.ShowFooter">
      <div class="row m-0 footerNav">
        @if (ContextService.IsMobileView) {
        @if (ContextService.IsJobSeeker && ContextService.IsUserLoggedIn) {
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <a class="hide show Mobile" data-toggle="collapse" href="#LookingForWork" role="button" aria-expanded="false"
            aria-controls="LookingForWork" (click)="toggleIconRotationJob()">
            <b>Job Seeker</b> <i class="fa fa-caret-down" [ngClass]="{'rotate-180' : isIconRotatedJob}"></i>
          </a>
          <div class="collapse" id="LookingForWork">
            <looking-for-work-items></looking-for-work-items>
          </div>
        </div>
        }

        @if (!ContextService.IsUserLoggedIn) {
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <a class="hide show Mobile" data-toggle="collapse" href="#LookingForWork" role="button" aria-expanded="false"
            aria-controls="LookingForWork" (click)="toggleIconRotationJob()">
            <b>Job Seeker</b> <i class="fa fa-caret-down" [ngClass]="{'rotate-180' : isIconRotatedJob}"></i>
          </a>
          <div class="collapse" id="LookingForWork">
            <looking-for-work-items></looking-for-work-items>
          </div>
        </div>
        }

        @if (ContextService.IsJobSeeker==false && ContextService.IsUserLoggedIn) {
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <a class="hide show Mobile" data-toggle="collapse" href="#LookingtoRecruit" role="button"
            aria-expanded="false" aria-controls="LookingtoRecruit" (click)="toggleIconRotation()">
            <b>Recruiter</b> <i class="fa fa-caret-down" [ngClass]="{'rotate-180' : isIconRotated}"></i>
          </a>
          <div class="collapse" id="LookingtoRecruit">
            <looking-to-recruite-items></looking-to-recruite-items>
          </div>
        </div>
        }

        @if (ContextService.IsUserLoggedIn==false) {
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <a class="hide show Mobile" data-toggle="collapse" href="#LookingtoRecruit" role="button"
            aria-expanded="false" aria-controls="LookingtoRecruit" (click)="toggleIconRotation()">
            <b>Recruiter</b> <i class="fa fa-caret-down" [ngClass]="{'rotate-180' : isIconRotated}"></i>
          </a>
          <div class="collapse" id="LookingtoRecruit">
            <looking-to-recruite-items></looking-to-recruite-items>
          </div>
        </div>
        }
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <a class="hide show Mobile" data-toggle="collapse" href="#HelpandAdvice" role="button" aria-expanded="false"
            aria-controls="HelpandAdvice" (click)="toggleIconRotationHelp()">
            <b>Help and Advice</b> <i class="fa fa-caret-down" [ngClass]="{'rotate-180' : isIconRotatedHelp}"></i>
          </a>
          <div class="collapse" id="HelpandAdvice">
            <community-items></community-items>
          </div>
        </div>
        <!-- <div class="col-12 Mobile-P-0 Desktop-D-None" *ngIf="!ContextService.IsDesktopView">
          <a class="hide show Mobile" data-toggle="collapse" href="#Community" role="button" aria-expanded="false"
            aria-controls="Community">
          <b>Community</b> <i class="fa fa-caret-down"></i>
          </a>
          <div class="collapse" id="Community">
            <community-items></community-items>
          </div>
        </div> -->
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <a class="hide show Mobile" data-toggle="collapse" href="#Apply4U" role="button" aria-expanded="false"
            aria-controls="Apply4U" (click)="toggleIconRotationAbout()">
            <b>About Us</b> <i class="fa fa-caret-down" [ngClass]="{'rotate-180' : isIconRotatedAbout}"></i>
          </a>
          <div class="collapse" id="Apply4U">
            <apply4u-items></apply4u-items>
          </div>
        </div>
        <div class="col-12 Mobile-P-0 Desktop-D-None">
          <span><b>See Our Reviews</b></span>
          <div class="row footerLogos">
            <div class="col-12 p-0">
              <a target="_blank" href="https://www.google.com/search?q=apply4u+reviews&amp;rlz=1C1CHBD_enPK923PK923&amp;
              sxsrf=ALiCzsajB2e5BkGmy_1m53OUSZx637gH-A%3A1654693306959&amp;ei=up2gYqeKOqqXxc8PhMSeuAk&amp;oq=Apply4U+&amp;
              gs_lcp=Cgdnd3Mtd2l6EAEYAzIECCMQJzIECCMQJzIFCAAQgAQyCggAEIAEEIcCEBQyBQgAEIAEMgUIABCABDIFCAAQgAQyBQgAEIAEMgUIABCABDIFCAAQgAQ6BAgAEENKBAhBGABKBAhGGABQAFi7G2DwOWgAcAF4AIABnQKIAZYEkgEDMi0ymAEAoAECoAEBwAEB&amp;
              sclient=gws-wiz#lrd=0x487613d58b14afcb:0x5e4663db231e5e79,1,,," title="Google reviews">
                <img ngSrc="assets/images/a4u-ratings/google.webp" loading="lazy" alt="Google" class="img" width="91"
                  height="42">
              </a>
              <a target="_blank" href="https://www.glassdoor.com/Overview/Working-at-Apply4U-EI_IE1921687.11,18.htm"
                title="glassdoor rating">
                <img ngSrc="assets/images/a4u-ratings/glassdoor.webp" loading="lazy" alt="glassdoor" class="img"
                  width="91" height="42" />
              </a>
            </div>
          </div>
        </div>
        } @else {
        @if (ContextService.IsJobSeeker && ContextService.IsUserLoggedIn) {
        <div class="col showDesktop" [ngClass]="ContextService.IsUserLoggedIn ? 'col-lg-3' : 'col'">
          <span><b>Job Seeker</b></span>
          <looking-for-work-items></looking-for-work-items>
        </div>
        <div class="col-lg-3 col-md-6 showDesktop">
        <span><b>Recuiter</b></span>
        <looking-to-recruite-items></looking-to-recruite-items>
      </div>
        }@else if (ContextService.IsUserLoggedIn==false) {
        <div class="col showDesktop">
          <span><b>Job Seeker</b></span>
          <looking-for-work-items></looking-for-work-items>
        </div>
        <div class="col showDesktop" *ngIf="ContextService.IsDesktopView && !ContextService.IsUserLoggedIn">
          <span><b>Recruiter</b></span>
          <looking-to-recruite-items></looking-to-recruite-items>
        </div>
        }
    
        
        <div class="col showDesktop" [ngClass]="ContextService.IsUserLoggedIn ? 'col-lg-3' : 'col'">
          <span><b>Help and Advice</b></span>
          <community-items></community-items>
        </div>
        <div class="col showDesktop" [ngClass]="ContextService.IsUserLoggedIn ? 'col-lg-3' : 'col'">
          <span><b>About Us</b></span>
          <apply4u-items></apply4u-items>
        </div>
        <div class="col showDesktop" [ngClass]="ContextService.IsUserLoggedIn ? 'col-lg-3' : 'col'">
          <span><b>See Our Reviews</b></span>
          <div class="row footerLogos">
            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 p-0">
              <a target="_blank" href="https://www.google.com/search?q=apply4u+reviews&amp;rlz=1C1CHBD_enPK923PK923&amp;
              sxsrf=ALiCzsajB2e5BkGmy_1m53OUSZx637gH-A%3A1654693306959&amp;ei=up2gYqeKOqqXxc8PhMSeuAk&amp;oq=Apply4U+&amp;
              gs_lcp=Cgdnd3Mtd2l6EAEYAzIECCMQJzIECCMQJzIFCAAQgAQyCggAEIAEEIcCEBQyBQgAEIAEMgUIABCABDIFCAAQgAQyBQgAEIAEMgUIABCABDIFCAAQgAQ6BAgAEENKBAhBGABKBAhGGABQAFi7G2DwOWgAcAF4AIABnQKIAZYEkgEDMi0ymAEAoAECoAEBwAEB&amp;
              sclient=gws-wiz#lrd=0x487613d58b14afcb:0x5e4663db231e5e79,1,,," title="Google reviews">
                <img ngSrc="assets/images/a4u-ratings/google.webp" alt="Google" loading="lazy" class="img marginfix"
                  width="91" height="42">
              </a>
            </div>
            <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 p-0">
              <a target="_blank" href="https://www.glassdoor.com/Overview/Working-at-Apply4U-EI_IE1921687.11,18.htm"
                title="glassdoor rating">
                <img ngSrc="assets/images/a4u-ratings/glassdoor.webp" alt="glassdoor" loading="lazy" class="img"
                  width="91" height="42" />
              </a>
            </div>
          </div>
        </div>
        }
      </div>
    </footer>
  </div>
</div>
<div class="container-fluid BottomFtr">
  <div class="container">
    <div class="row">
      <div class="col-lg-7 col-md-12 ">
        <div class="row PartnerBox">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 text-center">
            <div #carousel id="carouselPartners" class="carousel slide" data-ride="carousel" data-bs-pause="false"
              data-interval="3000">
              <div class="carousel-inner">
                <div class="carousel-item item active">
                  <a *ngIf="ContextService.IsJobSeeker && !ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="LoginURL" (click)="ContextService.OnRegisterClick()">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Job_Site_(Job_Seekers1).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Sign Up" alt="Apply4U | Sign Up"
                      class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsJobSeeker && ContextService.IsUserLoggedIn" class="footerBanner"
                    routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                    routerLink={{Navigations.VideoCVIntro}}>
                    <img ngSrc="assets/images/footer-banner/Apply4U_Job_Site_(Job_Seekers_Signed_In).webp"
                      loading="lazy" width="728" height="90" alt="Apply4u"
                      title="Apply4U | Experienced recruiters on hand to help"
                      alt="Apply4U | Experienced recruiters on hand to help" class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsRecruiter && !ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="Navigations.RecruiterJoinNow" (click)="ContextService.OnRegisterClick()">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Recruiter_Site_(Recruiter1).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Searching Candidates looking for work"
                      alt="Apply4U | Searching Candidates looking for work" class="img-responsive" />
                  </a>

                  <!-- <a *ngIf="ContextService.IsRecruiter && ContextService.IsUserLoggedIn" class="footerBanner" [href]="ContactUsBannerURL">
                    <img  ngSrc="assets/images/footer-banner/Need_a_Tailored_Solution_(Recruiter).webp'" width="728" height="90" alt="Apply4u"
                      title="Apply4U | Need a tailored solution" alt="Apply4U | Need a tailored solution" class="img-responsive" />
                  </a> -->
                  <a *ngIf="ContextService.IsRecruiter && ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="PricingPageUrl">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Job_Site_(Job_Seekers_Signed_In) (2).webp"
                      loading="lazy" width="728" height="90" alt="Apply4u" title="Apply4U | Pricing"
                      alt="Apply4U | Pricing" class="img-responsive" />
                  </a>

                </div>
                <div class="carousel-item item">
                  <a *ngIf="ContextService.IsJobSeeker && !ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="ContactUsBannerURL">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Expert_Recruiters_(Job_Seekers).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Contact Us" alt="Apply4U | Contact Us"
                      class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsJobSeeker && ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="ContactUsBannerURL">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Expert_Recruiters_(Job_Seekers).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Contact Us" alt="Apply4U | Contact Us"
                      class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsRecruiter && !ContextService.IsUserLoggedIn" class="footerBanner"
                    routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                    routerLink={{Navigations.CvSearch}}>
                    <img ngSrc="assets/images/footer-banner/Apply4U_Relevant_Cvs_(Recruiter).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | CV Search Services"
                      alt="Apply4U | CV Search" class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsRecruiter && ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="Navigations.CvSearch">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Relevant_Cvs_(Recruiter).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | CV Search Services"
                      alt="Apply4U | CV Search" class="img-responsive" />
                  </a>
                </div>
                <div class="carousel-item item">
                  <a *ngIf="ContextService.IsJobSeeker && !ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="Navigations.JobSeekerJoinNow" (click)="ContextService.OnRegisterClick()">
                    <img ngSrc="assets/images/footer-banner/Apply4U_FREE_CV_REVIEW_(Job_Seekers1).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Sign up" alt="Apply4U | Sign up"
                      class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsJobSeeker && ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="PricingPageUrl">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Job_Site_(Job_Seekers_Signed_In) (2).webp"
                      loading="lazy" width="728" height="90" alt="Apply4u" title="Apply4U | Pricing"
                      alt="Apply4U | Pricing" class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsRecruiter && !ContextService.IsUserLoggedIn" class="footerBanner"
                    routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                    routerLink={{Navigations.JobPost}}>
                    <img ngSrc="assets/images/footer-banner/Apply4U_Unlimited_Ads_(Recruiter).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Unlimited Free Job Ads"
                      alt="Apply4U | Unlimited_Free_Job_Ads" class="img-responsive" />
                  </a>
                  <a *ngIf="ContextService.IsRecruiter && ContextService.IsUserLoggedIn" class="footerBanner"
                    [href]="Navigations.JobPost">
                    <img ngSrc="assets/images/footer-banner/Apply4U_Unlimited_Ads_(Recruiter).webp" loading="lazy"
                      width="728" height="90" alt="Apply4u" title="Apply4U | Unlimited Free Job Ads"
                      alt="Apply4U | Unlimited_Free_Job_Ads" class="img-responsive" />
                  </a>
                </div>
              </div>
              <ol class="carousel-indicators d-none">
                <li data-target="#carouselPartners" data-slide-to="0" class="active"></li>
                <li data-target="#carouselPartners" data-slide-to="1"></li>
                <li data-target="#carouselPartners" data-slide-to="2"></li>
                <li data-target="#carouselPartners" data-slide-to="3"></li>
              </ol>
            </div>
          </div>
        </div>
        <div class="row mt-2">
          <div class=" col-md-12 d-flex justify-content-center">
            <a target="_blank" href="https://apps.apple.com/gb/app/apply4u/id1531296867" title="App Store" class="mr-2">
              <img ngSrc="assets/images/a4u-app-banner/ios-app.webp" loading="lazy" alt="App store" width="140"
                height="45" /> </a>
            <a target="_blank" href="https://play.google.com/store/apps/details?id=com.apply4u&hl=en_GB"
              title="Google Play Store" class="mr-2">
              <img ngSrc="assets/images/a4u-app-banner/gplay-app.webp" loading="lazy" alt="Play Store" width="140"
                height="45" />
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-5 col-md-12 SocialMedia">
        <p>Follow Us</p>
        <ul>
          <li>
            <a href="https://www.facebook.com/apply4u.official/" target="_blank" rel="noreferrer" title="facebook"><i
                class="fab fa-facebook-f"></i></a>
          </li>
          <li>
            <a href="https://twitter.com/apply4u" target="_blank" rel="noreferrer" title="twitter"><i
                class="fab fa-twitter"></i></a>
          </li>
          <li>
            <a href="https://www.linkedin.com/company/apply4u.co.uk" title="linkedin" rel="noreferrer"
              target="_blank"><i class="fab fa-linkedin-in"></i></a>
          </li>
          <li>
            <a href="https://www.youtube.com/channel/UCvXNoVMxl2Ga6ewBHg---7g" title="youtube" rel="noreferrer"
              target="_blank"><i class="fab fa-youtube"></i></a>
          </li>
          <li>
            <a href="https://instagram.com/apply4u_?igshid=YmMyMTA2M2Y=" target="_blank" rel="noreferrer"
              title="instagram"><i class="fab fa-instagram"></i></a>
          </li>
        </ul>
        <p *ngIf="!ContextService.IsUserLoggedIn">
          <a href="JavaScript:Void(0);" on-click="SignInHandler()">Sign In</a> |
          <a href="/registration-type">Join Now</a>
        </p>
        <p>
          <a class="navbar-brand" routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
            [routerLink]="ContextService.IsJobSeeker ? '/' : '/recruiter'">
            <img ngSrc="assets/images/Apply4U_logo_new45.webp" loading="lazy" width="45" height="45" alt="Apply4U"
              title="Apply4U | The Social Recruitment Jobsite" alt="Apply4U | The Social Recruitment Jobsite"
              class="img-responsive" />
          </a>
          Apply4U, London - All Rights Reserved - 2024
        </p>
      </div>
    </div>
  </div>
</div>