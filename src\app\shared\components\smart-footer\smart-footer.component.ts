import { <PERSON><PERSON><PERSON>, NgIf, NgOptimizedImage } from '@angular/common';
import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ContextService } from '@apply4u/services/context-service';
import { Navigation } from '@apply4u/shared/constant/navigation/navigation';
import { environment } from '@environment/environment';
import { LookingForWorkItemsComponent } from './looking-for-work-items/looking-for-work-items.component';
import { LookingToRecruiteItemsComponent } from './looking-to-recruite-items/looking-to-recruite-items.component';
import { CommunityItemsComponent } from './community-items/community-items.component';
import { Apply4UItemsComponent } from './apply4u-items/apply4u-items.component';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { timer } from 'rxjs/internal/observable/timer';

declare var $: any;
@Component({
  selector: 'smart-footer',
  templateUrl: './smart-footer.component.html',
  styleUrls: ['./smart-footer.component.css'],
  standalone: true,
  imports: [NgIf, RouterLink, RouterLinkActive,
    NgOptimizedImage,
    LookingForWorkItemsComponent,
    LookingToRecruiteItemsComponent,
    CommunityItemsComponent,
    Apply4UItemsComponent, NgClass
  ]
})
export class SmartFooterComponent implements OnInit {
  isIconRotated: boolean = false;
  isIconRotatedHelp: boolean = false;
  isIconRotatedAbout: boolean = false;
  isIconRotatedJob: boolean = false;
  Navigations = Navigation;
  LoginURL:string = environment.IdentityServerUrl + 'jobseeker?utm_source=footerjobseekerbanner&utm_medium= footerjobseekerbanner&utm_term=Register&utm_content=searching%20&utm_campaign=footerjobseekerbannerRR';
  ContactUsBannerURL:string = environment.clientSideBaseUrl + 'contact-us?utm_source=footercontactusbanner&utm_medium=%20footercontactusbanner&utm_term=Register&utm_content=searching%20&utm_campaign=footercontactusbannerRR';
  PricingPageUrl: string ="/our-pricing/jobseeker";
  @ViewChild('carousel') CarouselId: ElementRef;

  @Input({required:true})
  ContextService: ContextService;

  constructor() {
  }

  toggleIconRotation() {
    this.isIconRotated = !this.isIconRotated
  }

  toggleIconRotationHelp() {
    this.isIconRotatedHelp = !this.isIconRotatedHelp
  }

  toggleIconRotationAbout() {
    this.isIconRotatedAbout = !this.isIconRotatedAbout
  }

  toggleIconRotationJob() {
    this.isIconRotatedJob = !this.isIconRotatedJob
  }

  ngOnInit() {
    if (this.ContextService.IsBrowser && this.ContextService.IsClientSideLoading == false) {
      timer(200).subscribe(() => {
        this.ContextService.IsClientSideLoading = true;
      });
    }
  }
  ngAfterViewInit(): void {
    // if (this.ContextService.IsBrowser && IsNotNull(this.CarouselId)) {
    //   const myCarousel = this.CarouselId.nativeElement;
    //   const carousel = $(myCarousel).carousel();
    // }
  }

  SignInHandler(): void {
    this.ContextService.SignInRedirect();
  }

}
