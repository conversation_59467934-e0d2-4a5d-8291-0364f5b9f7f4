import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, ParamMap } from '@angular/router';
import { LoadingService } from '@apply4u/services/loading.service';
import { BlogsService } from '@apply4u/services/blogs.service';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { Shared } from '@apply4u/models/shared';
import { Meta, Title } from '@angular/platform-browser';
import { BlogSearchResult } from '@apply4u/models/blogs/blog-search-result';
import { IsAny, IsNotNull, IsNotNullOrEmpty } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { BlogSearchPost } from '@apply4u/models/blogs/blog-search-post';
import { Sector } from '@apply4u/models/sector';
import { PopularTagSearches } from '@apply4u/shared/constant/tag-search/popular-tags';
import { MasterDataService } from '@apply4u/services/masterdata.service';
import { ContextService } from '@apply4u/services/context-service';
import { UtilService } from '@apply4u/services/util.service';
import { Router } from '@angular/router';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { BreadCrumbTitleHome, BreadcrumbsTitleBlogs } from '@apply4u/shared/constant/constant';
import { environment } from '@environment/environment';
import { Blog_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';

@Component({
  selector: 'app-blog-detail',
  templateUrl: './blog-detail.component.html',
  styleUrls: ['./blog-detail.component.css']
})
export class BlogDetailComponent implements OnInit {
  serverBaseUrl: string = Shared.baseUrl;
  SearchResult = new BlogSearchResult();
  PopularPosts: BlogSearchPost[] = [];
  RecentPosts: BlogSearchPost[] = [];
  Sectors: Sector[] = [];
  PopularTags = PopularTagSearches;
  IsBlogShare: boolean = false;
  PostURL: string = "";
  SearchTag: string = "";
  BlogIdForComment: number;
  ContextServices: ContextService;
  BreadCrumbSchema: BreadCrumbs;

  constructor(
    private route: ActivatedRoute,
    private loadingService: LoadingService,
    private blogService: BlogsService,
    private metaService: MetaWrapperService,
    private meta: Meta,
    private title: Title,
    private masterDataService: MasterDataService,
    private contextServices: ContextService,
    private utilService: UtilService,
    private router: Router
  ) { 
    this.ContextServices = this.contextServices;
  }

  ngOnInit() {
    this.route.paramMap.subscribe((params: ParamMap) => {
      if (params.has('title')) {
        let blogSearchUrl = params.get('title');

        if (IsNotNullOrEmpty(blogSearchUrl)) {
          this.PostURL = blogSearchUrl;
          this.GetBlogBySearchUrl('/' + blogSearchUrl);
        }
      }
    });

    this.BlogsDataOnLoad();
  }

  ShareBlogProfile() {
    if (IsNotNullOrEmpty(this.PostURL)) {
      if (!this.PostURL.includes('/blogs')) {
        this.PostURL = `/blogs/${this.PostURL}`;
      }

      this.IsBlogShare = true;
    }
  }

  OnSearchTermChanged(searchTag: string): void {
    if (IsNotNullOrEmpty(searchTag)) {
      this.SearchTag = searchTag;
    }
  }

  private GetBlogBySearchUrl(searchUrl: string): void {
    this.loadingService.Show();
    this.blogService.GetBlogBySearchUrl(searchUrl).subscribe({
      next: result => {
        this.loadingService.Hide();

        if (IsNotNull(result)) {
          this.SearchResult = result;
          this.BlogIdForComment = result.Id;
          this.AddingDynamicSEOMetaStuff(this.SearchResult.Title, this.SearchResult.QuickSummary);
          
          if(this.contextServices.IsBrowser){
            this.PopulateBreadCrumbsDisplayList();
          }

          if (IsNotNullOrEmpty(this.SearchResult.Page.PreviousUrl)) {
            if (!this.SearchResult.Page.PreviousUrl.includes('/blogs')) {
              this.SearchResult.Page.PreviousUrl = `/blogs${this.SearchResult.Page.PreviousUrl}`;
            }
          }

          if (IsNotNullOrEmpty(this.SearchResult.Page.NextUrl)) {
            if (!this.SearchResult.Page.NextUrl.includes('/blogs')) {
              this.SearchResult.Page.NextUrl = `/blogs${this.SearchResult.Page.NextUrl}`;
            }
          }
        }
      }, error: error => {
        this.loadingService.Hide();
      }
    });
  }

  private BlogsDataOnLoad(): void {
    this.GetPopularPosts();
    this.GetRecentPosts();
    this.GetSectors();
  }

  private GetPopularPosts(): void {
    this.blogService.BlogsPopularPost().subscribe({
      next: popular => {
        if (IsAny(popular)) {
          this.PopularPosts = [];
          this.PopularPosts = popular;
          this.PopularPosts.forEach(element => {
            if (!element.RedirectUrl.includes('/blogs/')) {
              element.RedirectUrl = `/blogs${element.RedirectUrl}`;
            }
          });
        }
      }, error: error => {
        this.PopularPosts = [];
      }
    });
  }

  private GetRecentPosts(): void {
    this.blogService.BlogsRecentPost().subscribe({
      next: recent => {
        if (IsAny(recent)) {
          this.RecentPosts = [];
          this.RecentPosts = recent;
          this.RecentPosts.forEach(element => {
            if (!element.RedirectUrl.includes('/blogs/')) {
              element.RedirectUrl = `/blogs${element.RedirectUrl}`;
            }
          });
        }
      }, error: error => {
        this.RecentPosts = [];
      }
    });
  }

  private GetSectors(): void {
    this.masterDataService.getSector(null).subscribe(result => {
      if (IsAny(result)) {
        this.Sectors = result;
      }
    });
  }

  private AddingDynamicSEOMetaStuff(blogTitle: string, quickSummary: string) {
    if (IsNotNullOrEmpty(blogTitle) && IsNotNullOrEmpty(quickSummary)) {
      let CountStringLength = this.CountStringLength(blogTitle);

      if (CountStringLength <= 45) {
        this.SetPageTitle(`${blogTitle} | Apply4U Blogs`);
      } else {
        this.SetPageTitle(`${blogTitle} | Apply4U`);
      }

      let countDescription = this.CountStringLength(quickSummary);

      if (countDescription > 160) {
        let trimmedDescription = this.SettingUpMetaDescriptionLength(quickSummary);
        let countAgain = this.CountStringLength(trimmedDescription);

        if (countAgain > 160) {
          let trimAgain = this.SettingUpMetaDescriptionLength(trimmedDescription);

          this.UpdateMetaTag('description', `${trimAgain}`);
        } else {
          this.UpdateMetaTag('description', `${trimmedDescription}`);
        }
      } else {
        this.UpdateMetaTag('description', `${quickSummary}`);
      }
    }
    // Updating OG Image Tag Url
    if (IsNotNullOrEmpty(this.serverBaseUrl) && IsNotNullOrEmpty(this.SearchResult.ThumbnailPath)) {
      let updateogImageUrl = this.serverBaseUrl + this.SearchResult.ThumbnailPath;

      this.UpdateOgImageTag('og:image', `${updateogImageUrl}`);
    }
    //#region SEO META TAGS
    this.metaService.CreateCanonicalURL(this.metaService.GetCurrentPageURLWithoutQueryParams());
    let CreatedCanonicalUrl = this.metaService.GetCurrentPageURLWithoutQueryParams();

    if (IsNotNullOrEmpty(CreatedCanonicalUrl)) {
      let breadcrumbsScript = `{
        "@context": "https://schema.org/", 
        "@type": "BreadcrumbList", 
        "itemListElement": [{
          "@type": "ListItem", 
          "position": 1, 
          "name": "home",
          "item": "https://www.apply4u.co.uk"  
        },{
          "@type": "ListItem", 
          "position": 2, 
          "name": "blogs",
          "item": "https://www.apply4u.co.uk/blogs"  
        },{
          "@type": "ListItem", 
          "position": 3, 
          "name": "${blogTitle}",
          "item": "${CreatedCanonicalUrl}"  
        }]
      }`;

      this.metaService.CreateBreadCrumbsScript(breadcrumbsScript);
    }
  }

  private SetPageTitle(title: string) {
    this.title.setTitle(title);
    this.meta.updateTag({ property: 'og:title', content: title });
    this.meta.updateTag({ name: 'twitter:text:title', content: title });
  }

  private UpdateMetaTag(name: string, content: string): HTMLMetaElement {
    this.meta.updateTag({ name: 'twitter:card', content: 'summary' });
    this.meta.updateTag({ name: 'twitter:site', content: '@apply4ucouk' });
    this.meta.updateTag({ name: 'twitter:text:description', content: content });
    this.meta.updateTag({ property: 'og:description', content: content });
    this.meta.updateTag({ property: 'og:type', content: 'website' });

    return this.meta.updateTag({ name: name, content: content });
  }

  private UpdateOgImageTag(name: string, content: string): HTMLMetaElement {
    this.meta.updateTag({ property: 'og:image', content: content });
    return this.meta.updateTag({ name: name, content: content });
  }

  private CountStringLength(text: string): number {
    return text.length;
  }

  private CheckStringHaveCommaSign(desCriptionText: string) {
    if (IsNotNullOrEmpty(desCriptionText)) {
      let hasComma = desCriptionText.includes(',');

      return hasComma;
    }
  }

  private SettingUpMetaDescriptionLength(metaDescription: string) {
    let formattedDescription: string = "";

    if (IsNotNullOrEmpty(metaDescription)) {
      if (this.CheckStringHaveCommaSign(metaDescription) == true) {
        let splittedDesc = metaDescription.split(',');

        if (IsAny(splittedDesc)) {
          let sliceLastIndex = splittedDesc.slice(0, -1);
          let count: number = 0;
          let countSplittedText = "";

          sliceLastIndex.forEach(element => {
            if (count == 0) {
              countSplittedText = element;
              count = 1;
            } else {
              countSplittedText += ',' + element;
            }
          });

          formattedDescription = countSplittedText;
        }
      } else {
        // If Title have no Comma Sign 
        formattedDescription = metaDescription;
      }
    }

    return formattedDescription;
  }

  private PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();

    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleHome;

    if (this.contextServices.IsJobSeeker) {
      listItem.item = `${environment.HostName}${'/'}`;
    } else if (this.contextServices.IsRecruiter) {
      listItem.item = `${environment.HostName}${'/recruiter'}`;
    }

    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem1 = new ListItem();
    listItem1.position = 2;
    listItem1.name = BreadcrumbsTitleBlogs;
    listItem1.item = `${environment.HostName}${Blog_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem1);

    let listItem2 = new ListItem();
    listItem2.position = 3;
    listItem2.name = this.SearchResult.Title
    listItem2.item = `${environment.HostName}${this.router.url}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);
  }

  OnClickOurPricingPageLink(): void {
    let pricingPageUrl: string = this.utilService.GetPricingPageUrlByUserType();

    if (IsNotNullOrEmpty(pricingPageUrl)) {
      this.router.navigateByUrl(pricingPageUrl);
    }
  }

}
