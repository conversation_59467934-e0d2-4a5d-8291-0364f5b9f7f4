/* New Theme */
.NewTheme {
  padding: 40px 100px;
  background: #0e1b5c;
}
.image-2 {
  width: 10% !important;
  height: auto !important;
  position: relative;
  top: -14px;
}
.NewTheme .heading {
  margin-top: 20px;
  padding: 0;
  color: #ffffff;
  font-size: 80px;
  text-align: left;
  font-weight: bold !important;
}

.NewTheme h2 {
  margin: 0;
  padding: 0;
  color: #fff;
  font-size: 60px;
  text-align: left;
  font-weight: 400 !important;
}

.NewTheme span {
  color: #14C59C;
  font-size: 50px;
  text-align: left;
  font-weight: 700 !important;
}
.NewTheme .btn-text  {
  color:#0e1b5c!important;
}
.NewTheme .btn {
  margin: 20px 0;
  font-size: 24px !important;
  font-weight: 700!important;
}
.LatestJobs{
  margin-top: 70px !important;
}

.LatestJobs .carousel-indicators{
  position: relative;
}

.btn-primary {
  background-color: #14C59C;
  border-color: #14C59C;
}
.btn-primary:hover {
  background-color: #14C59C!important;
  border-color: #0e1b5c!important;
}
.btn-primary:active {
  background-color: #fff;
  border-color: #0e1b5c;
}
.TrustedBy {
  padding: 0;
  margin: 0;
  color: #000;
  font-size: 26px;/* New Theme */
  font-weight: 600 !important;
}
.carousel-indicators li{
  width:12px!important;
  height:12px!important;
  border-radius: 100%;
}

.NewTheme .card {
  border: 1 px solid;
  background-color: #14C59C;
  border-color: #14C59C;
}
.in{
  cursor: default !important;
}
/* NewThemeSearch */
.NewThemeSearch {
  padding: 70px 100px;
  background: #f9f9f9;
  border-bottom: 1px solid #f9f9f9;
}
.NewThemeSearch .card {
  border:  1px solid;
  border-color: #ccc;
  border-radius: 8px;
  margin-bottom: 20px;
}
.imgCircle {
  border: 4px solid #0e1b5c;
  border-radius: 100%;
}
.NewThemeSearch .card .card-header-morejobs {
  background-color: #0e1b5c!important;
  padding: 10px 0!important;
}
.NewThemeSearch .card .card-header-morejobs .title{
  color:#ffffff!important;
  float: left;
  font-size: 24px;
  font-weight: bold;
  margin-left: 20px;
}
 .indicators {
  position: relative!important;
  width: 300px;
}
.NewThemeSearch .card .card-header-morejobs .MoreJobs{
  color:#ffffff!important;
  float: right;
  font-size: 15px;
  padding-top: 4px;
  margin-right: 15px;
  text-decoration: underline;
  cursor: pointer;
}
.NewThemeSearch .card .card-header-morejobs .MoreJobs:hover {
    color: #14C59C !important;;
    text-decoration: none;
  }

.NewThemeSearch .card .carousel-item h4{
font-weight: bold!important;
margin-top: 10px;
color: #000;
}

.NewThemeSearch h1 {
  margin: 5px 0;
  padding: 0;
  color: #434343;
  font-size: 34px;
  text-align: center;
  font-weight: bold !important;
}

.NewThemeSearch h2 {
  padding: 0;
  color: #434343;
  font-size: 25px;
  margin: 28px 0px 28px 0px!important;
  text-align: left;
  text-align: center;
  font-weight: 500 !important;
}

.NewThemeSearch h2 span {
  text-align: left;
  font-weight: bold;
  font-size: 30px;
}

.NewThemeSearch h3 {
  padding: 0;
  color: #434343;
  font-size: 34px;
  text-align: center;
  margin: 0 0 15px 0;
  font-weight: bold !important;
}

.NewThemeSearch h3 span {
  color: #434343;
  text-align: left;
  font-weight: 500 !important;
}

.NewThemeSearch .MobileSPC {
  float: right;
}

.NewThemeSearch .TxtLink a {
  color: #545454;
  font-size: 16px;
  text-decoration: none;
}

.NewThemeSearch .TxtLink a:hover {
  color: #0e1b5c;
  text-decoration: none;
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show>.btn-primary.dropdown-toggle {
  color: #fff !important;
  background-color: #0e1b5c !important;
}

#accordion .panel {
  border: none;
  border-radius: 3px;
  box-shadow: none;
}
.searchmore .panel-body {
  background: #0e1b5c !important;
  height: 100%;
}
.Registration .panel-body {
  background: #0e1b5c !important;
}
.HelpAdvice .VideoCard{
  height: 100%;
}
.panel-body p{
  color:#ffffff;
}
.panel-body p span{
  color:#ffffff!important;
  font-weight: 700!important;
}
.panel-body p a{
  color:#14C59C;
}
.panel-body .btn {
  background-color:#14C59C !important;
  color:#0e1b5c!important;
}
.panel-body .btn:hover{
  background-color: #0e1b5c!important;
  border-color: #14C59C!important;
  color:#ffffff!important;
}
#accordion .panel-body {
  border: none;
  padding: 15px;
  line-height: 29px;
  /* margin-bottom: 10px; */

}

#accordion .panel-body ul {
  margin: 0;
  padding: 10px;
  list-style: none;
}

.btn-success:hover {
  color: #077e36;
  background-color: none;
}

.btn-success {
  border-color: #14C59C;
  background-color: #14C59C;
}

#accordion .panel-body ul li a {
  color: #14C59C;
  font-size: 15px;
  text-decoration: none;
  padding: 10px !important;
}

#accordion .panel-body ul li a:hover {
  color: #fff;
  text-decoration: underline;
}

/* NewThemeSearch */

/*Registration*/
.Registration {
  padding: 0px 32px;
  background: #ffffff;
}

.Registration .h1 {
  padding: 0;
  margin: 0 0 15px 0;
  color: #0e1b5c;
  font-size: 34px;
  text-align: center;
}

.Registration .h1 span {
  color: #0e1b5c;
  font-weight: bold !important;
}

.Registration h2 {
  padding: 0;
  margin: 0 0 15px 0;
  color: #0e1b5c;
  font-size: 30px;
  text-align: center;
}

.Registration h2 span {
  color: #0e1b5c;
  font-weight: bold !important;
}

.Registration p span {
  color: #0e1b5c;
  font-weight: bold;
}

.Registration p strong {
  color: #434343;
  font-weight: bold;
}

.Registration a {
  background-color: #0e1b5c !important;
  border-color: #0e1b5c;
}
.NM_accordion {
  background-color: #0e1b5c;
}
.NM_accordion h1 {
  color: #fff;
  margin-top: 25px;
  font-size: 30px;
  font-weight: 800!important;
}
.NM_accordion h2 span {
  color: #14C59C;
  font-size: 30px;
  font-weight: 800!important;
}
.NM_accordion .panel-body a {
  float: right!important;
  margin: 0px 20px;
  font-weight: 700!important;
}

/*Registration*/
.accordion .card-header .title {
  color: #fff;
  float: left;
  padding: 6px 19px;
  font-weight: 500 !important;
}
.accordion .card-header .title:hover {
  cursor: auto;
}

.accordion .card-header .accicon {
  font-size: 20px;
  width: 1.2em;
  text-align: center;
  color: #14C59C;
  padding: 0px 25px;
}

.accordion .card-header {
  cursor: pointer;
  padding: 0px !important;
}

.accordion .card {
  border-radius: 10px;
}
.accordion .card-body {
  padding: 10px;
}

.accordion .card-header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

span.accicon {
  float: right;
  position: relative;
}

.accordion .card-body {
  padding: 0px !important;
  border-top: 0px !important;
}

/******************* Accordion Demo - 8 *****************/
#accordion8 .panel {
  border: none;
  box-shadow: none;
  border-radius: 0;
  /* margin-bottom: 15px; */
}

#accordion8 .panel-body {
  border: none;
  color: #545454;
  padding: 15px;
  font-size: 15px;
  line-height: 25px;
  background: #fff;
  transition: all 0.5s ease 0s;
}

.LogoSliderBox {
  background-color: #f9f9f9;
  padding: 21px;
}

.LogoSliderBox h1 {
  color: #0e1b5c !important;
}

.LogoSliderBox h1 span {
  color: #0e1b5c;
  font-weight: 700 !important;
}

.LogoSliderBox a {
  background-color: #0e1b5c !important;
  color: #fff;
  border-color: #0e1b5c;
  font-weight: 600!important;
}
.featuredrecruiter {
  position: relative!important;
}
.NewTheme .carousel-indicators li  {
  background-color: #14C59C!important;
}
.carousel-indicators li  {
  background-color: #081039!important;
}

/*Help & Advice */
.HelpAdvice {
  padding: 0px 100px;
  background: #f9f9f9;
}

.HelpAdvice h1 {
  padding: 0;
  color: #0e1b5c;
  font-size: 36px;
  margin: 0 0 50px 0;
  text-align: center;
}

.HelpAdvice h1 span {
  color: #0e1b5c;
  font-weight: bold !important;
}

.HelpAdvice h2 {
  padding: 0;
  margin: 0 0 15px 0;
  color: #060E37;
  font-size: 36px;
  text-align: center;
  font-weight: 400 !important;
  padding: 20px;
}

.HelpAdvice h2 span {
  color: #060E37;
  font-weight: bold !important;
}

.HelpAdvice p a {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
}

.HelpAdvice p a:hover {
  color: #fff;
  text-decoration: underline;
}

#accordion2 .card-header .title {
  color: #14C59C !important;
}

#accordion2 .accordion {
  background-color: #0e1b5d;
}

#accordion2 .accordion .card {
  border: 1px solid !important;
}

#accordion2 .panel-body p {
  margin-bottom: 0;
  padding: 15px;
}
#accordion2 .panel-body .btn {
  margin: 15px;
}

#accordion2 .panel-title a.collapsed:hover {
  color: #fff;
  background: #060E37;
  text-decoration: none;
}

/*Help & Advice */
.jobs {
  padding-top: 10px;
  padding-bottom: 10px;
}
:host ::ng-deep app-browse-by-industry .IndustriesListing ul li a {
  color: #14C59C !important;
  font-weight: normal !important;
}
:host ::ng-deep app-browse-by-industry .IndustriesListing ul  {
  padding-top: 0px;
  padding-bottom: 0px;
}
:host ::ng-deep app-browse-by-industry .IndustriesListing ul li a:hover {
  color: #fff !important;
  text-decoration: underline!important;
}
:host ::ng-deep app-browse-by-location-item .JobsListing ul li a {
  color: #14C59C !important;
  font-weight: normal !important;
}
:host ::ng-deep app-browse-by-location-item .JobsListing ul li a:hover {
  color: #fffc !important;
  text-decoration: underline;
}
:host ::ng-deep app-browse-by-location-item .JobsListing ul li {
  width: 100% !important;
  line-height: 30px;
  margin-bottom: 0px;
}

:host ::ng-deep app-browse-by-location-item .JobsListing ul {
  padding: 0px 10px 0px 10px !important;
}

:host ::ng-deep app-browse-by-location-item .JobsListing ul h3 {
  font-weight: normal !important;
}

:host ::ng-deep app-browse-by-industry .IndustriesListing ul h1 {
  font-weight: normal !important;
}

:host ::ng-deep app-browse-by-industry .IndustriesListing ul li {
  margin-bottom: 0px !important;
  line-height: 30px !important;
}

.btnshowmore {
  color: #ffff !important;
  padding: 0 19px;
}
.video-card .card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 15px;
  margin: 0px;
}
.VideoCard{
  padding:10px;
  text-align: center;
  background:#0e1b5c;
  color:#ffff;
  margin-bottom: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.VideoCard .card-body{
  padding:0px;
  /* height: 400px; */
}
.VideoCard h2{
  color:#fff;
  font-size: 16px;
  font-weight: 600;
}
.VideoCard p{
  font-size: 15px;
  text-align: center;
  color:#ffff;
  min-height: 90px;
  text-decoration: none;
}
.adviceBtn{
  margin-bottom: 10px;
}
.VideoCard .card-body a{
  text-decoration: none;
  display: inline;
  font-weight: 600!important;
}
.VideoCard .card-body a:hover{
  background-color: #32d2ad !important;
  color: #202c66!important;
  border: 1px solid #27389c;
}
.VideoCard p a:hover{
  color:black;
  text-decoration: none;
}
.VideoCard img{
  width:100%;
  height:auto;
  margin-bottom: 10px;
}
.VideoCard .card-body a:hover{
  text-decoration: none;
}

.VideoCard .card-body a h2:hover{
  text-decoration: underline!important;
}
.MobileSearchForm .MobileFix a {
color: #0e1b5c;
font-weight: 500!important;
}
.MobileSearchForm .MobileFix a:hover{
color: #fff;
}
a {
  cursor: pointer;
}

::-webkit-input-placeholder {
  /*Chrome/Opera/Safari*/
  font-size: 14px;
}

::-moz-placeholder {
  /*Firefox 19+*/
  font-size: 14px;
}

:-ms-input-placeholder {
  /*IE 10+*/
  font-size: 14px;
}

:-moz-placeholder {
  /*Firefox 18-*/
  font-size: 14px;
}

.nav-link:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

.btn:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

.card-header2 .title {
  color: #fff;
  font-size: 15px;
}

.card-header2 .accicon {
  float: right;
  font-size: 20px;
  width: 1.2em;
}

.card-header2 {
  color: #fff;
  cursor: pointer;
  border-bottom: none;
  background: #3465b7;
  padding: 6px 0 6px 10px;
}

.card {
  border: 1px solid #9ac1ff;
}

.card-body2 {
  padding: 10px;
  font-size: 14px;
  border-top: 1px solid #9ac1ff;
}

.card-header2:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
}

.card-header {
  background: #0e1b5c !important;
  padding: 35px 0px !important;
  text-align: center;
}

.card-header2 {
  background: #0e1b5d !important;
  padding: 35px 0px !important;
  text-align: center;
}

.card-header img {
  width: 100% !important;
  height: auto;
}

/*JobSearch **********************************************************************************/
/* /*Don't Delete below CSS its Necessary To BrowseBy Industry & Browse By Location Child Component By Using NgDeep Pseudo/ */

.p-l-8 {
  padding-left: 8px;
}

.f-w {
  font-weight: 500;
}

.ShowSlideDown {
  visibility: visible;
  max-height: 20000px;
  opacity: 1;
  transition: all 0.5s ease-in;
}

.HideSlideDown {
  visibility: hidden;
  max-height: 0px;
  opacity: 0;
  overflow: hidden;
  max-height: 0px;
  transition: all 0.9s ease-out;
}

.FastHide {
  transition: all 0.5s ease-out;
}

:host ::ng-deep app-smart-location-autocomplete .target-icon {
  margin-top: -37px;
}

/**/
.card {
  margin: 0 auto;
  border: none;
}

.card .carousel-item {
  min-height: 190px;
}

.card2 .carousel-item {
  min-height: 120px;
}

.CardBorder {
  padding: 40px 20px;
  text-align: center;
  border-radius: 6px;
  background: #0e1b5c;
}

.card .carousel-caption {
  padding: 0;
  right: 0!important;
  left: 0!important;
  top: 0px;
  color: #ffffff;
  background-color: #0e1b5c;
  border: 1px solid #0e1b5c;
  min-height: 215px;
  padding: 15px;
  border-radius: 10px;
  position: relative;
  margin-bottom: 10px;
}

.card .carousel-caption h2 {
  color: #14c59c !important;
  font-size: 25px;
  font-weight: 600!important;
  margin-bottom: 10px;
}

.card .carousel-caption .col-sm-3 {
  display: flex;
  align-items: center;
}

.card .carousel-caption .col-sm-9 {
  text-align: left;
}

.smallest {
  color:#14C59C;
  font-weight: 600!important;
  padding-top: 20px;
}

.card .carousel-control-prev,
.card .carousel-control-next {
  color: #3d3d3d !important;
  opacity: 1 !important;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  background-image: none;
  color: #0e1b5c;
  font-size: 14px;
  background-color: #14C59C;
  height: 32px;
  line-height: 32px;
  width: 32px;
  border-radius: 50%;
}

.carousel-control-prev-icon:hover,
.carousel-control-next-icon:hover {
  opacity: 0.85;
}

.carousel-control-prev {
  left: 40%;
  top: 110%;
}

.carousel-control-next {
  right: 40%;
  top: 110%;
}

.midline {
  width: 60px;
  border-top: 2px solid #0e1b5d;
}

.midline2 {
  width: 60px;
  border-top: 2px solid #fff;
}

.a4u-banner {
  padding: 20px 125px;
  background-color: #0e1b5c;
}

.listInScroll{
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* -webkit-overflow-scrolling: touch; */
}

.listInScroll::-webkit-scrollbar {
  width: 4px;
}

.listInScroll::-webkit-scrollbar-thumb {
  background-color: #d3cece;
}

.bg-off-white {
  background-color: #f9f9f9;
}

.btn.btn-primary1 {
  background-color: #14C59C;
}
.btn-primary1:hover {
  background-color:#14C59C;
  border-color:#0e1b5c!important;
}

@media (min-width: 320px) and (max-width: 575px) {
  .carousel-control-prev {
    left: 35%;
    top: 105%;
  }

  .carousel-control-next {
    right: 35%;
    top: 105%;
  }

  .card .carousel-caption h3 {
    margin-top: 0;
    font-size: 16px;
    font-weight: 700;
  }
  .a4u-banner {
    padding: 0px 88px;
    background-color: #0e1b5c;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .carousel-control-prev {
    left: 35%;
    top: 105%;
  }

  .searchCard{
    padding: 0 31px;
  }

  .carousel-control-next {
    right: 35%;
    top: 105%;
  }
}

@media (min-width: 767px) and (max-width: 991px) {
  .card .carousel-caption h3 {
    margin-top: 0;
    font-size: 16px;
    font-weight: 700;
  }
}

@media only screen and (max-width: 350px) {
  .NewTheme .card .MobileFix a {
    font-size: 13px !important;
  }
}

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .NewTheme {
    padding: 20px 0px 20px 0px;
  }
  .searchCard{
    padding: 0 31px;
  }

  .SliderBox1{
    height:100%;
    margin-left: 15px;
  }
  .BannerBtn {
    margin-top: -30px;
  }
  .CarouselCard {
    padding: 0 20px;
  }
  section.Trustpilot{
    margin-bottom:20px;
  }
  .MobilePadTop{
    padding-top: 70px!important;
  }
  .NewTheme .HomeSlider {
    padding: 0px!important;
  }
  .NewTheme .heading {
    font-size: 37px;
    margin-top: 0;
  }
  .NewTheme h2{
    font-size: 60px;
  }
  .NewTheme h3{
    color:#14C59C;
    font-size: 20px;
    top: -19px;
    position: relative;
    font-weight: 500!important;
  }
  .NewTheme h3.NewJobs{
    color:#fff;
    font-size: 30px;
    top: -30px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme span.a4u{
    top: -22px;
    color:#fff;
    font-size: 39px;
    position: relative;
  }
  .NewTheme span.Two{
    color:#14C59C;
    font-size: 140px;
    position: absolute;
    top: -36px;
    left: 270px;
    transform: rotate(5deg);
  }
  .NewTheme .slide2 {
    left: 245px!important;
  }
  .NewTheme span.Experts{
    top: -10px;
    color:#fff;
    font-size: 26px;
    position: relative;
  }
  .NewTheme h3.Jobs10{
    color:#fff;
    font-size: 39px;
    top: -20px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme h2.CallUs a, .NewTheme h2.CallUs{
    color:#14C59C;
    font-size: 45px;
  }
  .TrustedBy {
    font-size: 15px;
    margin-top: 0px!important;
  }
  .NewTheme .btn {
    margin: 10px 0px!important;
  }
  .btntalk .btn {
    font-size: 22px !important;
  }  
  .NewTheme .carousel-indicators {
    bottom: 0px;
    margin: 0px;
  }
  .NM_accordion{
    margin-top: 0px!important;
    padding:20px!important;
  }
  .NewTheme img {
    width: 25%;
    height: auto;
    margin-bottom: 15px;
  }
  .NewThemeSearch {
    padding: 0 25px;
  }
  .NewThemeSearch input {
    margin: 10px 0;
  }
  .NewThemeSearch .btn {
    display: block;
    margin-top: 10px;
  }
  .MobileSearchForm {
    padding: 0px!important;
  }
  .MobileSearchForm .MobileFix {
   margin-bottom: 0px!important;
  }
  .MobileSearchForm .MobileFix:first-child {
    margin-bottom: 8px!important;
   }
  .NewTheme .card {
   padding: 10px 0px; 
   margin: 0 15px;
  }
  .NewTheme .card label {
    font-size: 15px;
    font-weight: 700 !important;
    color: #0e1b5d;
    margin-top: 0px;
    margin-bottom: 5px!important;
  }
  .NewTheme .card a {
    color: #0e1b5d;
    font-size: 15px !important;
    margin-left: 5px;
  }
  .NewThemeSearch h3 {
    margin-top: 50px;
  }
  .NewThemeSearch h2 {
    margin: 16px 0px 15px 0px!important;
  }
  .Additionalhelp {
    margin-top: 30px!important;
  }
  .Additionalhelp .btn-primary {
    background-color: #14C59C;
    border-color: #0e1b5d;
  }
  .blog {
    margin-top: 40px;
  }
  .Registration {
    padding: 0px 25px !important;
  }
  .R-Mobile {
    text-align: left;
  }
  .HelpAdvice {
    padding: 0px 20px;
  }
  .Testi {
    padding: 50px 50px;
  }
  .Registration .h1 {
    margin: 10px 0 10px 0;
  }
  /* .container {
    max-width: 550px !important;
    padding-bottom: 30px;
  } */
  .card-header-morejobs{
    margin-bottom: 15px!important;
  }
  .card-header-morejobs .MoreJobs{
    padding-top: 8px;
    font-size:16px!important;
  }
  :host ::ng-deep app-browse-by-industry .IndustriesListing ul li {
    margin: 0px 20px!important;
    margin-bottom: 0px !important;
  }

  :host ::ng-deep app-browse-by-industry .IndustriesListing ul li a {
    line-height: 1.0 !important;
  }

  :host ::ng-deep app-browse-by-industry .IndustriesListing ul li a.heading {
    margin: 0px !important;
  }

  :host ::ng-deep app-browse-by-location-item .JobsListing ul li {
    margin-bottom: 0px !important;
  }

  :host ::ng-deep app-browse-by-location-item .JobsListing ul li a {
    line-height: 1.0 !important;
  }

  .HelpAdvice .card-header.show.in{
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
  .NewTheme {
    padding: 20px 20px 20px 20px;
  }
  section.Trustpilot{
    margin-bottom:20px;
  }
  .NewTheme .heading {
    font-size: 60px;
  }
  /* .NewTheme h2{
    font-size: 60px;
  } */
  .NewTheme h3{
    color:#14C59C;
    font-size: 36px;
    top: -16px;
    position: relative;
    font-weight: 500!important;
  }
  .NewTheme h3.NewJobs{
    color:#fff;
    font-size: 30px;
    top: -30px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme span.a4u{
    top: -20px;
    color:#fff;
    font-size: 60px;
    position: relative;
  }
  .NewTheme span.Two{
    color:#14C59C;
    font-size: 140px;
    position: absolute;
    top: -36px;
    left: 270px;
    transform: rotate(5deg);
  }
  .NewTheme span.Experts{
    top: -10px;
    color:#fff;
    font-size: 30px;
    position: relative;
  }
  .NewTheme h3.Jobs10{
    color:#fff;
    font-size: 52px;
    top: -20px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme h2.CallUs a, .NewTheme h2.CallUs{
    color:#14C59C;
    font-size: 60px;
  }
  .TrustedBy {
    font-size: 15px;
    margin-top: 0px!important;
  }
   .NewTheme .carousel-indicators {
    bottom: -15px;
  }
  .NewTheme img {
    width: 15%;
    height: auto;
    margin-bottom: 10px;
  }
  .NewThemeSearch {
    padding: 0 20px;
  }
  .NewThemeSearch input {
    margin: 10px 0;
  }
  .NewThemeSearch .btn {
    display: block;
    margin-top: 10px;
  }
  .MobileSearchForm {
    padding: 0px!important;
  }
  .MobileSearchForm .MobileFix {
   margin-bottom: 0px!important;
  }
  .NM_accordion{
    padding:20px!important;
    margin-top:-1px!important;
  }
  .NewTheme .card {
   padding: 10px 0px; 
   margin: 0 15px;
  }
  .NewTheme .card label {
    font-size: 15px;
    font-weight: 700 !important;
    color: #0e1b5d;
    margin-top: 0px;
    margin-bottom: 5px!important;
  }
  .NewTheme .card .btn-sm {
    background-color: #0e1b5d;
    color: #fff !important;
    font-size: 18.7px !important;
  }
  .NewTheme .card a {
    color: #0e1b5d;
    font-size: 15px !important;
    margin-left: 5px;
  }
  .Registration .h1 {
    margin: 50px 0 15px 0;
  }

  .R-Mobile {
    text-align: left;
  }

  .HelpAdvice {
    padding: 0px 20px;
  }
 

  /* .container {
    max-width: 600px !important;
  } */
}

@media only screen and (max-width: 767px) {
  .NewTheme .card .btn-sm {
    background-color: #0e1b5d;
    color: #fff !important;
    font-size: 18.7px !important;
    font-weight: 500 !important;
    width: 100%;
    border-radius: 4px;
  }

  .fit-width{
    width: fit-content !important;
  }
}

/* Medium devices (landscape tablets, 768px and down) */
@media only screen and (max-width: 768px) {
  .TrustedBy {
    margin-top: 30px;
  }
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .NewTheme img {
    width: 32%;
    height: auto;
    margin-bottom: 10px;
  }
  .TrustedBy{
    margin-top: 210px!important;
    margin-left: 110px;
  }
  .btntalk {
    margin-top:150px!important;
    margin-left: 50px!important;
  }
.MobileSearchForm{
 padding: 0px 15px!important;
}
.NewTheme .card .btn-sm {
  position: relative;
  top:10px;
}
  /* .container {
    max-width: 900px !important;
  } */
}

/* Large devices (laptops/desktops, 992px and down) */
@media only screen and (max-width: 992px) {
  .Registration{
    padding: 0 20px;
  }
  .Registration .container{
    padding: 0;
  }
  .NewThemeSearch .container{
    padding: 0;
  }

  .NewThemeSearch .job-item{
      padding: 0px !important;
  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  .NewTheme img {
    width: 25%;
    height: auto;
    margin-bottom: 10px;
  }

  /* .container {
    max-width: 1000px !important;
    display: contents;
  } */
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media (min-width: 1200px) {
.NewTheme {
  padding: 0px 0px 20px 0px;
}
.NewTheme span.a4u{
  position: relative;
  top: -6px;
}
.NewTheme h3{
  top: 0px;
}
.NewTheme .btn {
  margin:0px!important
}
.BannerBtn{
  margin: -20px 0px;
}
.SliderBox1{
  height:270px;
}
.padfix {
  margin-top: 0px;
}
.SliderBox2{
  height:310px;
  /* background:#333;  */
}
.NewTheme span.Two {
margin-left: 10px;
}
.NewTheme .slide2 {
  margin-left: 200px!important;
}
.NewTheme .HomeSlider img {
  width: 48%;
  height: auto;
}
.TrustedBy {
  margin-left:50px!important;
  margin-top: 190px!important;
}
.NewTheme .card {
  margin: 0;
  border-radius: 10px;
}
.NewTheme .card label {
  font-size: 20px;
  font-weight: 700 !important;
  color: #0e1b5d;
}
.NewTheme .card .btn-sm {
  background-color: #0e1b5d;
  color: #fff !important;
  margin-top: 33px !important;
  padding: 5px 15px;
  top:2px;
  font-size: 18px !important;
}
.NewTheme .card a {
  color: #0e1b5d;
  font-size: 15px !important;
  margin-left: 5px;
}
.Registration .h1 {
  margin-top: 0;
}
.NewThemeSearch .row {
  margin: 0 80px;
}
.HelpAdvice .NewThemeSearch .row {
  margin: 0 30px;
}
.HideDesktop {
  display: none !important;
}
.HideMobile {
  display: block;
}
.searchmore{
  margin: 0px !important;
}
/* .container {
  max-width: 1280px !important;
  display: block;
} */
.NewTheme .carousel-indicators {
  bottom: 0px;
  margin: 0px;
}
.NM_accordion{
  padding:0px!important;
  margin-top:24px!important;
}
.Trustpilot {
  padding:50px 40px!important;
}
.VideoCard p{
  min-height: 86px;
}
}

/* Extra extra large devices (extra large desktops, 1400px and up)  */
@media (min-width: 1400px) {
  .NewTheme {
    padding: 20px 100px 50px 100px;
  }
  .BannerBtn{
    margin: -15px 0px;
  }
  .SliderBox1{
    height:270px;
  }
  .SliderBox2{
    height:310px;
  }
  .NewTheme .carousel-indicators {
    bottom: 0px!important;
    margin: 0px;
  }
  .NewTheme .heading {
    font-size: 60px;
  }
  .NewTheme h2{
    font-size: 60px;
  }
  .NewTheme h3{
    top: 6px;
    color:#14C59C;
    font-size: 36px;
    position: relative;
    font-weight: 500!important;
  }
  .NewTheme h3.NewJobs{
    color:#fff;
    font-size: 36px;
    top: -35px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme span.a4u{
    top: -7px;
    color:#fff;
    font-size: 60px;
    position: relative;
  }
  .NewTheme span.Two{
    color:#14C59C;
    font-size: 150px;
    position: absolute;
    top: -42px;
    left: 266px;
    transform: rotate(5deg);
  }
  .NewTheme span.Experts{
    top: 0px;
    color:#fff;
    font-size: 30px;
    position: relative;
  }
  .NewTheme h3.Jobs10{
    color:#fff;
    font-size: 52px;
    top: 0px;
    position: relative;
    font-weight: 700!important;
  }
  .NewTheme h2.CallUs a, .NewTheme h2.CallUs{
    color:#14C59C;
    font-size: 60px;
  }
  .TrustWrap{
    left: 100px;
    position: relative;
  }
  .TrustedBy {
    margin-top: 210px!important;
    float: right;
    margin-right: 320px;
  }
  .NewTheme .HomeSlider img {
    width: 35%;
    height: auto;
  }
  .NewThemeSearch {
    padding: 0px 0px;
  }
  .HelpAdvice {
    padding: 0px 100px;
  }
  .Registration {
    padding: 8px 50px 0px 50px;
  }
  .NewThemeSearch h3 {
    margin-top: 0;
  }
  .R-desktop {
    text-align: right;
  }
}