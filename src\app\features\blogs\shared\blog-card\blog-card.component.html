<div class="row pt-2 pb-2 m-0">
    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12" *ngFor="let blog of BlogsList;" id="blogid={{blog.Id}}">
        <div class="row m-0">
            <div class="col-md-12 BoxListing">
                <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{blog?.RedirectUrl}}" class="cursor">
                    <img *ngIf="blog.ThumbnailPath" alt="{{blog.Title}}" src="{{BaseUrl}}/{{blog.ThumbnailPath}}" width="100" height="100" />
                    <img *ngIf="!blog.ThumbnailPath" alt="{{blog.Title}}" src="assets/images/Groups/BlogPost.png" width="100" height="100" />
                </a>
                <div class="row m-0">
                    <div class="col-md-12 p-0">
                        <p class="Date">By {{blog.PostedBy}} | <i class="fa fa-calendar-o"></i> {{blog.CreatedOn | date}}
                        </p>
                    </div>
                    <div class="col-md-12 p-0">
                        <p class="Date cursor"><a href="JavaScript:Void(0);" (click)="ShareBlogProfile(blog)" data-toggle="modal"
                                data-target="#ShareCompanyProfileModal">
                                <span><i class="fa fa-share-alt"></i> Share</span></a>
                        </p>
                    </div>
                </div>
                <h2 class="title-elipsis cursor" title="{{blog.Title}}"><a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }" routerLink="{{blog?.RedirectUrl}}"
                        class="text-bold blog-link">{{blog.Title}}</a></h2>
                <div class="height">
                    <p class="content-elipsis" title="{{blog.QuickSummary}}">{{blog.QuickSummary}}</p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Share Blogs -->
<span *ngIf="IsBlogShare">
    <app-share-company-modal [PostUrl]="PostURL" [Title]="'Share Blog'"></app-share-company-modal>
</span>
<!-- Share Blogs -->