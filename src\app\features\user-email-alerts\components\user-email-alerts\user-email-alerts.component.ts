import { Component, OnInit, Input, EventEmitter, On<PERSON><PERSON>roy } from '@angular/core';
import { fork<PERSON>oin, Subject } from 'rxjs';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { LoadingService } from '@apply4u/services/loading.service';
import { Shared } from '@apply4u/models/shared';
import { CheckPlatformService } from '@apply4u/services/check-platform.service';
import { ProfileService } from '@apply4u/services/profile.service';
import { UserEmailConfiguration } from '@apply4u/models/useremailconfiguration';
import { MasterDataService } from '@apply4u/services/masterdata.service';
import { EmailFrequency } from '@apply4u/models/emailfrequency';
import { SaveSearchServices } from '@apply4u/services/save-searches';
import { SaveSearches } from '@apply4u/models/save-searches';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { EmailEventGroup } from '@apply4u/models/emailEventGroup';
import { EmailType } from '@apply4u/models/emailType';
import { CommonMessages } from '@apply4u/shared/constant/messages/common-messages';
import { UnSubscribeEmailMessages } from '@apply4u/shared/constant/messages/un-subscribe-email-messages';
import { ConfirmationMessages } from "@apply4u/shared/constant/messages/confirmation-messages";
import { SaveSearchesMessages } from '@apply4u/shared/constant/messages/save-searches-message';
import { LoginTypeKeys } from '@apply4u/shared/constant/Local-storage-keys/login-type-keys';
import { ContextService } from '@apply4u/services/context-service';
import { UseremailalertsService } from '@apply4u/services/useremailalerts.service';
import { Title } from '@angular/platform-browser';
import { ConfirmationService } from "@apply4u/shared/components/confirmation-modal/confirmation.service";
import { IsAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNull, IsNullOrEmpty, IsZero } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { AttbJobAlert } from '@apply4u/models/attb-job-alert';
import { AttbJobAlertService } from '@apply4u/services/attb-job-alert.service';
import { Useralerts } from '@apply4u/shared/constant/useralerts';
import { UserAlertMessages } from '@apply4u/shared/constant/messages/user-alert-messages';
import { SalaryRange } from '@apply4u/models/SalaryRage';
import { Duration } from '@apply4u/models/duration';
import { Currency } from '@apply4u/models/currency';
import { TotalJobAlert } from '@apply4u/models/total-job-alert';
import { TotalJobAlertService } from '@apply4u/services/total-job-alert.service';
import { Attbalertfrequency } from '@apply4u/models/attbalertfrequency';
import { JobGateJobAlert } from '@apply4u/models/jobGateJobAlert';
import { JobGateAlertService } from '@apply4u/services/job-gate-alert.service';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { BreadCrumbTitleDashBoard, BreadcrumbsTitleManageAlerts } from '@apply4u/shared/constant/constant';
import { environment } from '@environment/environment';
import { DashBoard_RouteUrl, EmailAlert_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';

@Component({
  selector: 'app-user-email-alerts',
  templateUrl: './user-email-alerts.component.html',
  styleUrls: ['./user-email-alerts.component.css']
})
export class UserEmailAlertComponent implements OnInit, OnDestroy {

  serverBaseUrl: string = Shared.baseUrl;
  isBrowser: boolean = false;
  userEmailconfigurations: UserEmailConfiguration[] = [];
  emailFrequencies: EmailFrequency[] = [];
  emailFrequency: EmailFrequency[] = [];
  saveSearches: SaveSearches[] = [];
  emailEventGroups: EmailEventGroup[] = [];
  isUserLoggedIn: boolean = false;
  loginEmail: string = "";
  EmailEventTypeId: number;
  ContextService: ContextService;

  attbJobAlert: AttbJobAlert;
  alreadySetAlerts: AttbJobAlert[] = [];
  AttbAlertForm: UntypedFormGroup;
  IsAttbAlertFormSubmitted: boolean = false;
  attbAlertFrequencies: Attbalertfrequency[] = [];

  TotalJobAlert: TotalJobAlert = new TotalJobAlert();
  TotalJobAlerts: TotalJobAlert[] = [];
  TotalJobAlertForm: UntypedFormGroup;
  IsTotalJobFormSubmitted: boolean = false;

  JobGateAlert: JobGateJobAlert = new JobGateJobAlert();
  JobGateAlerts: JobGateJobAlert[] = [];
  JobGateAlertForm: UntypedFormGroup;
  IsJobGateFormSubmitted: boolean = false;


  selectedFrequencyId: number = 0;
  salaryRanges: SalaryRange[];
  SelectedSalaryRanges: SalaryRange[];
  selectedSalaryId: any = 2;
  salaryDurations: Duration[] = [];
  currencies: Currency[];
  BreadCrumbSchema: BreadCrumbs;

  @Input()
  IsUnSubPage: boolean = false;
  @Input('UnsubscribeAll') UnsubscribeAll:Subject<any>;
  @Input('UnsubscribeJobAlerts') UnsubscribeJobAlerts:Subject<any>;  

  constructor(
    private loadingService: LoadingService,
    private confirmationService: ConfirmationService,
    private toasterService: ToasterMessageService,
    private masterDataService: MasterDataService,
    private contextService: ContextService,
    private userEmailAlertService: UseremailalertsService,
    private title: Title,
    private checkPlatformService: CheckPlatformService,
    private localStorageService: LocalstorageService,
    private saveSearchesService: SaveSearchServices,
    private activatedRoute: ActivatedRoute,
    private profileService: ProfileService,
    private attbJobAlertService: AttbJobAlertService,
    private formBuilder: UntypedFormBuilder,
    private totalJobAlertService: TotalJobAlertService,
    private jobGateAlertService: JobGateAlertService,
  ) {

    if (this.contextService.IsBrowser) {
      this.isBrowser = true;
      this.loginEmail = this.localStorageService.GetItem(LoginTypeKeys.LoginName);
      this.ContextService = this.contextService;
      this.InitializeAttbAlertForm();
      this.InitializeTotalJobAlertForm();
      this.InitializeJobGateAlertForm;
      this.InitAttbAlert();
      this.InitTotalJobAlert();
      this.InitJobgGateAlert();
    }

  }

  InitializeAttbAlertForm(): void {
    this.AttbAlertForm = this.formBuilder.group({
      SearchKeyword: ['', Validators.required],
      LocationText: ['', Validators.required],
      JobType: [, [Validators.required]],
      JobContract: [, Validators.required],
      MinimumSalary: [, Validators.required],
      MaximumSalary: [, Validators.required],
      Radius: [, Validators.required],
    });
  }

  InitializeTotalJobAlertForm(): void {
    this.TotalJobAlertForm = this.formBuilder.group({
      SearchKeyword: ['', Validators.required],
      LocationText: ['', Validators.required]
    });
  }

  InitializeJobGateAlertForm(): void {
    this.JobGateAlertForm = this.formBuilder.group({
      SearchKeyWord: ['', Validators.required],
      LocationText: ['', Validators.required]
    });
  }

  AddATTBAlert(): void {
    this.InitializeAttbAlertForm();
    this.InitAttbAlert();
  }

  AddTotalJobAlert(): void {
    this.InitializeTotalJobAlertForm();
    this.InitTotalJobAlert();
  }

  AddJobGAteAlert(): void {
    this.InitializeJobGateAlertForm();
    this.InitJobgGateAlert();
  }

  ngOnInit() {
    if (this.isBrowser) {
      if (IsNotNull(this.UnsubscribeAll)) {
        this.UnsubscribeAll.subscribe(data => {
          this.UnsubscibeFromAll();
        });
      }
      if (IsNotNull(this.UnsubscribeJobAlerts)) {
        this.UnsubscribeJobAlerts.subscribe(data => {
          this.UnsubscibeFromJobAlerts();
        });
      }

      this.LoadComponentData();
      this.PopulateBreadCrumbsDisplayList();
    }
  }

  LoadComponentData() {
    this.LoadMasterData();
    this.LoadEmailConfiguration();
    this.LoadATTBAlerts();
    this.LoadTotaljobAlerts();
    //this.LoadJobGateAlerts();
  }

  private LoadMasterData(): void {
    let salaryRangeRequest = this.masterDataService.GetSalaryRanges();
    let salaryDurationRequest = this.masterDataService.getDuration();
    let salaryCurrencyRequest = this.masterDataService.getCurrency();
    let attbAlertFrequencyRequest = this.masterDataService.getAlertFrequencies();
    let emailFrequencyRequest = this.masterDataService.GetEmailFrequency();

    forkJoin([salaryRangeRequest, salaryDurationRequest, salaryCurrencyRequest, attbAlertFrequencyRequest, emailFrequencyRequest])
      .subscribe(masterDataResults => {

        this.salaryRanges = masterDataResults[0];
        this.salaryDurations = masterDataResults[1];
        this.currencies = masterDataResults[2];
        this.attbAlertFrequencies = masterDataResults[3];
        this.emailFrequencies = masterDataResults[4];
        this.emailFrequency = masterDataResults[4];
        
        if (IsAny(this.emailFrequency)) {
          this.emailFrequency = this.emailFrequency.filter(d => d.Id != 1);
        }

        if (IsNotNull(this.attbAlertFrequencies) && this.attbAlertFrequencies.length > 0) {

          this.GetAttbRegistraion();
        }

      });
  }

  SearchEnterKeyPressed(event: boolean): void {
  }

  OnAttbLocationChanged(event: string) {
    this.attbJobAlert.DesiredLocation = event;

    if (IsNullOrEmpty(this.attbJobAlert.DesiredLocation)) {
      this.attbJobAlert.Radius = 0;
    }
  }

  OnTotslJobLocationChange(event: string) {
    this.TotalJobAlert.LocationText = event;
  }

  OnJobGateLocationChange(event: string) {
    this.JobGateAlert.LocationText = event;
  }


  private GetAttbRegistraion(): void {
    this.loadingService.Show();
    this.attbJobAlertService.GetAttbRegistration(this.contextService.LoggedInUserId)
      .subscribe(registration => {
        this.loadingService.Hide();

        if (IsNotNull(registration)) {
          this.selectedFrequencyId = registration.AlertFrequency;
        }
      }, e => {
        this.loadingService.Hide();
      });
  }

  LoadEmailConfiguration() {
    this.loadingService.Show();
    this.profileService.GetEmailConfigurationByUser(this.contextService.LoggedInUserId).subscribe({
      next: result => {
        this.emailEventGroups = result;

        if (IsAny(this.emailEventGroups)) {
          this.emailEventGroups.forEach(element => {
            element.IsUnSubGroup = false;
            if (IsNotNull(element) && IsAny(element.EmailEventTypes)) {
              // check is user configuration exist              
              element.EmailEventTypes.forEach(event => {
                if (IsNotNull(event) && IsNotNull(event.UserEmailConfiguration)) {
                  if (IsNotNull(event.UserEmailConfiguration.IsSubscribed) && event.UserEmailConfiguration.IsSubscribed == false) {
                    event.IsUnSubFromEvent = false;
                  } else {
                    element.IsUnSubGroup = true;
                    event.IsUnSubFromEvent = true;
                  }
                } else {
                  element.IsUnSubGroup = true;
                  event.IsUnSubFromEvent = true;
                  event.UserEmailConfiguration = this.InitEmailConfiguration(event.Id);
                }

                event.IsSaveSearchExists = false;
              });
            }

          });
          this.loadingService.Hide();
        }
      },
      error: error => {
        this.loadingService.Hide();
        this.toasterService.ServerError();
      }
    });
  }


  InitEmailConfiguration(eventTypeId: any) {
    let configuration = new UserEmailConfiguration();

    configuration.Id = 0;
    configuration.EmailEventTypeId = eventTypeId;
    configuration.FrequencyId = 2; // defaul frequency is daily
    configuration.UserId = this.contextService.LoggedInUserId;
    configuration.IsSubscribed = true;

    return configuration;
  }


  OnRemoveClickHandler(userConfiguration: UserEmailConfiguration) {
    userConfiguration.IsSubscribed = false;
    this.loadingService.Show();
    this.profileService.UpdateEmailConfiguration(userConfiguration.Id, userConfiguration).subscribe({
      next: result => {
        this.loadingService.Hide();
        this.toasterService.Success(null, UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess);
        this.userEmailconfigurations = this.userEmailconfigurations.filter(f => f.Id != userConfiguration.Id);
        //this.LoadUserConnections();        
      },
      error: error => {
        this.LoadEmailConfiguration();
        this.loadingService.Hide();
        this.toasterService.ServerError();
      }
    });

  }

  UpdateSaveSearchEmailFrequency(savedSearche: SaveSearches) {
    if (IsNotNull(savedSearche) && IsNotNull(savedSearche.EmailFrequencyId) && savedSearche.EmailFrequencyId > 0) {
      this.loadingService.Show();
      this.saveSearchesService.UpdateSaveSearchId(savedSearche.Id, savedSearche).subscribe({
        next: result => {
          this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailFrequencyChanged, null);
          this.loadingService.Hide();

          if (IsNull(savedSearche.IsEmailAlertActive) || savedSearche.IsEmailAlertActive == false) {
            savedSearche.IsEmailAlertActive = true;
          }
        },
        error: error => {
          this.LoadEmailConfiguration();
          this.loadingService.Hide();
          this.toasterService.ServerError();
        }
      });
    } else {
      this.loadingService.Show();
      savedSearche.EmailFrequencyId = null;
      this.saveSearchesService.UpdateSaveSearchId(savedSearche.Id, savedSearche).subscribe({
        next: result => {
          savedSearche.EmailFrequencyId = 0;
          savedSearche.IsEmailAlertActive = false;
          this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess, null);
          this.loadingService.Hide();
        },
        error: error => {
          this.LoadEmailConfiguration();
          this.loadingService.Hide();
          this.toasterService.ServerError();
        }
      });
    }
  }

  GetSaveSearchesData() {
    if (this.contextService.IsUserLoggedIn) {
      this.loadingService.Show();
      this.saveSearchesService.GetSaveSearches(this.contextService.LoggedInUserId, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, true)
        .subscribe({
          next: result => {
            let mysavesearch = result.filter(m => m.UserId == this.contextService.LoggedInUserId);
            // console.log(mysavesearch);
            mysavesearch.forEach(element => {
              element.SearchReferenceName = this.GenerateRefNameForSaveSearch(element);
              element.DisplayKeyword = this.GenerateKeywordofSearch(element);
              if (IsNull(element.EmailFrequencyId) || IsZero(element.EmailFrequencyId)) {
                element.EmailFrequencyId = 0;
                element.IsEmailAlertActive = false;
              } else {
                element.IsEmailAlertActive = true;
              }
            });

            this.saveSearches = mysavesearch.reverse();
            this.loadingService.Hide();
          },
          error: error => {
            this.LoadEmailConfiguration();
            this.loadingService.Hide();
            this.toasterService.ServerError();
          }
        });

    } else {
      this.toasterService.Error(SaveSearchesMessages.OnCheckResultLoginError, null);
    }
  }

  GenerateRefNameForSaveSearch(savesearch: SaveSearches): string {
    let refname = "";
    if (IsNotNull(savesearch)) {
      // if (IsNotNull(savesearch.IsBooleanSearch) && savesearch.IsBooleanSearch == true) {
      //   if (IsNotNullOrEmpty(savesearch.AllofTheseWords)) {
      //     let str = savesearch.AllofTheseWords.toLowerCase();

      //     str = this.EscapeRegExp(str);
      //     refname += this.ConcatedStringForRefName(str);
      //   }

      //   if (IsNotNullOrEmpty(savesearch.ExactPharse)) {
      //     let str = savesearch.ExactPharse.toLowerCase();
      //     refname += this.ConcatedStringForRefName(str);
      //   }

      //   if (IsNotNullOrEmpty(savesearch.AnyofTheseWords)) {
      //     let str = savesearch.AnyofTheseWords.toLowerCase();
      //     refname += this.ConcatedStringForRefName(str);
      //   }

      //   if (IsNotNullOrEmpty(savesearch.NoneofTheseWords)) {
      //     let str = savesearch.NoneofTheseWords.toLowerCase();
      //     refname += this.ConcatedStringForRefName(str);
      //   }

      // }
      
        if (IsNotNullOrEmpty(savesearch.KeyWords)) {
          let str = "";

          str = savesearch.KeyWords.toLowerCase();
          str = this.EscapeRegExp(str);
          str = str.replace("and", "");
          str = str.replace("or", "");
          str = str.replace("not", "");
          refname += str;
          // refname += this.ConcatedStringForRefName(str);
        }
     
      // if (IsNotNullOrEmpty(refname)) {
      //   refname = refname.slice(0, -1);
      // }

      // if (IsNotNull(savesearch.LocationId) && savesearch.LocationId != 0) {
      //   if (IsNotNullOrEmpty(savesearch.LocationText)) {
      //     let splittedlocation;
      //     splittedlocation = savesearch.LocationText.split(",");
      //     if (IsNotNullOrEmpty(splittedlocation)) {
      //       refname += "<a> in </a>" + splittedlocation[2];
      //     }
      //   }
      // } else if (IsNotNullOrEmpty(savesearch.LocationId)) {
      //   if (this.IsSpecialCharacterExist(savesearch.LocationText)) {
      //     let splittedlocation;

      //     splittedlocation = savesearch.LocationText.split(",");
      //     if (IsNotNull(splittedlocation) && splittedlocation.length > 2) {
      //       refname += "<a> in </a>" + splittedlocation[2];
      //     }
      //     else {
      //       refname += "<a> in </a>" + savesearch.LocationText;
      //     }
      //   }
      //   else {
      //     refname += "<a> in </a>" + savesearch.LocationText;
      //   }
      // }
      
      return refname;
    }
    return "";
  }

  GenerateKeywordofSearch(savesearch: SaveSearches): string {
    let refname = "";
    if (IsNotNull(savesearch)) {
      if (IsNotNull(savesearch.IsBooleanSearch) && savesearch.IsBooleanSearch == true) {
        if (IsNotNullOrEmpty(savesearch.AllofTheseWords)) {
          let str = savesearch.AllofTheseWords.substring(0, 1).toUpperCase() + savesearch.AllofTheseWords.substring(1).toLowerCase();

          str = this.EscapeRegExp(str);
          refname += str + " | ";
        }

        if (IsNotNullOrEmpty(savesearch.ExactPharse)) {
          let str = savesearch.ExactPharse.substring(0, 1).toUpperCase() + savesearch.ExactPharse.substring(1).toLowerCase();

          refname += str + " | ";
        }

        if (IsNotNullOrEmpty(savesearch.AnyofTheseWords)) {
          let str = savesearch.AnyofTheseWords.substring(0, 1).toUpperCase() + savesearch.AnyofTheseWords.substring(1).toLowerCase();

          refname += str + " | ";
        }

        if (IsNotNullOrEmpty(refname)) {
          return refname;
        }
      }
      else if (IsNotNull(savesearch.IsBooleanSearch) && savesearch.IsBooleanSearch == false) {
        if (savesearch.KeyWords !== undefined && savesearch.KeyWords != null && savesearch.KeyWords != "") {
          let str = "";

          str = savesearch.KeyWords;
          str = this.EscapeRegExp(str);
          str = str.split("and").join("");
          str = str.split("not").join("");
          str = str.split("or").join("");
          let splittedArrayforString = str.split(" ");
          str = "";
          splittedArrayforString.forEach(element => {
            if (IsNotNullOrEmpty(element)) {
              str += element + " | ";
            }
          });
          refname += str;
        }
        if (IsNotNullOrEmpty(refname)) {
          return refname;
        }
      }

    }
    return "";
  }

  private EscapeRegExp(string) {
    return string.replace(/[.*+?"^${}()|[\]\\]/g, '');
  }

  ConcatedStringForRefName(keywordstring: string): string {
    if (IsNotNullOrEmpty(keywordstring)) {
      let refname = "";
      let splittedstringwithspaces;
      splittedstringwithspaces = keywordstring.split(" ");

      if (IsNotNull(splittedstringwithspaces) && splittedstringwithspaces.length > 0) {
        let count = 0;

        splittedstringwithspaces.forEach(element => {
          if (IsNotNullOrEmpty(element) && count < 5) {
            if (IsNotNullOrEmpty(element) && count < 5) {
              let splitkeyword = "";

              splitkeyword = element.split("");
              refname += splitkeyword[0].toUpperCase();

              if (IsNotNullOrEmpty(splitkeyword[1])) {
                refname += splitkeyword[1].toLowerCase();
              }

              if (IsNotNullOrEmpty(splitkeyword[2])) {
                refname += splitkeyword[2].toLowerCase();
              }

              count = count + 1;
              refname += "_";
            }
          }
        });
      }

      return refname;
    }

    return "";
  }

  private IsSpecialCharacterExist(urlPrams: any) {
    let format = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;

    if (format.test(urlPrams)) {
      return true;
    } else {
      return false;
    }
  }

  showSaveSearche(emailvEvetGroup: EmailEventGroup) {
    if (IsNotNull(emailvEvetGroup) && IsNotNull(emailvEvetGroup.EmailEventTypes) && emailvEvetGroup.EmailEventTypes.length > 0) {
      let emailvEvets = emailvEvetGroup.EmailEventTypes.filter(s => s.Id == 18);

      if (IsNotNull(emailvEvets) && emailvEvets.length > 0) {
        let emailvEvet = emailvEvets[0];

        if (IsNotNull(emailvEvet) && IsNotNull(emailvEvet.UserEmailConfiguration) && emailvEvet.UserEmailConfiguration.EmailEventTypeId == 18) {
          emailvEvet.IsSaveSearchExists = !emailvEvet.IsSaveSearchExists;

          if (emailvEvet.IsSaveSearchExists == true) {
            emailvEvetGroup.isCollasapable = true;

            this.GetSaveSearchesData();
          } else {
            emailvEvetGroup.isCollasapable = false;
            this.saveSearches = [];
          }
        }
      }
    }
  }

  UnsubscibeFromAll() {
    this.confirmationService.SetMessage(ConfirmationMessages.ConfirmationOnUnSubscribeAllEmail).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        if (IsNotNull(this.emailEventGroups) && this.emailEventGroups.length > 0) {
          this.loadingService.Show();

          this.profileService.UnSubscribeAllEmailConfiguration(this.contextService.LoggedInUserId)
            .subscribe({
              next: result => {
                this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedAllEmailSuccessfully, null);
                this.loadingService.Hide();
                this.LoadEmailConfiguration();
                this.LoadATTBAlerts();
                this.LoadTotaljobAlerts();
                this.LoadJobGateAlerts();
              },
              error: error => {
                this.LoadEmailConfiguration();
                this.LoadATTBAlerts();
                this.LoadTotaljobAlerts();
                this.LoadJobGateAlerts();
                this.loadingService.Hide();
                this.toasterService.ServerError();
              }
            });
        }
      }
    });
  }

  UnsubscibeFromJobAlerts() {
    this.confirmationService.SetMessage(ConfirmationMessages.ConfirmationOnUnSubscribeAllJobAlerts).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        if (IsNotNull(this.emailEventGroups) && this.emailEventGroups.length > 0) {
          this.loadingService.Show();

          this.profileService.UnSubscribeJobAlerts(this.contextService.LoggedInUserId)
            .subscribe({
              next: result => {
                this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedAllEmailSuccessfully, null);
                this.loadingService.Hide();
                this.LoadEmailConfiguration();
                this.LoadATTBAlerts();
                this.LoadTotaljobAlerts();
                this.LoadJobGateAlerts();
              },
              error: error => {
                this.LoadEmailConfiguration();
                this.LoadATTBAlerts();
                this.LoadTotaljobAlerts();
                this.LoadJobGateAlerts();
                this.loadingService.Hide();
                this.toasterService.ServerError();
              }
            });
        }
      }
    });
  }

  UnSubOrSubFromGroupButtonHandler(emailEventGroup: EmailEventGroup, isSub: boolean) {
    if (isSub == true) {
      emailEventGroup.EmailEventTypes.forEach(element => {
        if (IsNotNull(element.UserEmailConfiguration)) {
          this.UnSubOrSubFromGruopButton(element, true);
        }

        if (element.Id == 18) {
          // active user all saved searches
          this.ActiveDeActiveFromAllSavedSearches(true);
        }
      });

      emailEventGroup.IsUnSubGroup = true;
      let groupElement = document.getElementById("mainGroup0");

      if (groupElement != null) {
        groupElement.click();
      }
    } else if (isSub == false) {
      emailEventGroup.EmailEventTypes.forEach(element => {
        this.UnSubOrSubFromGruopButton(element, false);
        if (element.Id == 18) {
          // DeActive from all user saved searches email
          this.ActiveDeActiveFromAllSavedSearches(false);
        }
      });

      emailEventGroup.IsUnSubGroup = false;
      emailEventGroup.isCollasapable = false;
    }
  }

  ActiveDeActiveFromAllSavedSearches(isActive: boolean) {
    if (this.contextService.IsUserLoggedIn) {
      this.loadingService.Show();
      this.saveSearchesService.GetSaveSearches(this.contextService.LoggedInUserId, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2, null, true)
        .subscribe(result => {

          if (IsAny(result)) {
            this.saveSearches = result;

            if (isActive == true) {
              // Active all Saved Searches email

              this.saveSearches.forEach(element => {
                if (IsNull(element.EmailFrequencyId) || element.EmailFrequencyId == 0) {
                  element.EmailFrequencyId = 2; // default frequency
                }
                this.loadingService.Show();
                this.saveSearchesService.UpdateSaveSearchId(element.Id, element)
                  .subscribe(result => {
                    element.IsEmailAlertActive = true;
                    this.toasterService.Success(UnSubscribeEmailMessages.OnSubscribedEmailSuccessfully, null);
                    this.loadingService.Hide();
                  }, error => {
                    this.loadingService.Hide();
                  });
                //this.GetSaveSearchesData();
              });
            } else if (isActive == false) {
              this.saveSearches.forEach(element => {
                if (IsNotNull(element.EmailFrequencyId)) {
                  element.EmailFrequencyId = null;
                }
                this.loadingService.Show();
                this.saveSearchesService.UpdateSaveSearchId(element.Id, element)
                  .subscribe(result => {
                    element.IsEmailAlertActive = false;
                    this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess, null);
                    this.loadingService.Hide();
                  }, error => {
                    this.loadingService.Hide();
                  });
              });
              //this.GetSaveSearchesData();
            }
          }
          this.loadingService.Hide();
        }, errorResult => {
          this.loadingService.Hide();
          this.toasterService.Error(CommonMessages.OnSomethingWentWrongError, null);
        });

    } else {
      this.toasterService.Error(SaveSearchesMessages.OnCheckResultLoginError, null);
    }
  }


  UnSubOrSubFromEvent(emailEventType: EmailType, event: any) {

    if (IsNotNull(emailEventType)) {

      if (event.target.checked == true) {

        emailEventType.IsUnSubFromEvent = true;

        if (IsNotNull(emailEventType.UserEmailConfiguration) && emailEventType.UserEmailConfiguration.Id > 0) {

          this.confirmationService.SetMessage("Are you sure you want to subscribe?").OpenConfirmationDialogue(isConfirmed => {
            if (isConfirmed) {
              emailEventType.UserEmailConfiguration.IsSubscribed = true;

              this.loadingService.Show();
              this.profileService.DeleteEmailConfiguration(emailEventType.UserEmailConfiguration.Id)
                .subscribe(result => {

                  emailEventType.UserEmailConfiguration.Id = 0;
                  emailEventType.IsUnSubFromEvent = true;
                  this.toasterService.Success(UnSubscribeEmailMessages.OnSubscribedEmailSuccessfully, null);
                  this.loadingService.Hide();
                  let groups = this.emailEventGroups.filter(g => g.Id == emailEventType.EmailEventGroupId);

                  if (IsNotNull(groups) && groups.length > 0) {

                    let group = groups[0];
                    group.IsUnSubGroup = true;
                  }

                }, error => {

                  this.loadingService.Hide();
                  this.toasterService.ServerError();
                });

            } else {
              emailEventType.IsUnSubFromEvent = false;
            }
          });
        }
      } else {
        if (IsNotNull(emailEventType.UserEmailConfiguration) && emailEventType.UserEmailConfiguration.Id > 0) {
          this.confirmationService.SetMessage("Are you sure you want to unsubscribe?").OpenConfirmationDialogue(isConfirmed => {
            if (isConfirmed) {
              emailEventType.UserEmailConfiguration.IsSubscribed = false;

              this.loadingService.Show();
              this.profileService.UpdateEmailConfiguration(emailEventType.UserEmailConfiguration.Id, emailEventType.UserEmailConfiguration)
                .subscribe(result => {

                  emailEventType.IsUnSubFromEvent = false;
                  this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess, null);
                  this.loadingService.Hide();

                }, error => {

                  this.loadingService.Hide();
                  this.toasterService.ServerError();
                });

            } else {
              emailEventType.IsUnSubFromEvent = true;
            }
          });
        }
        else if (IsNotNull(emailEventType.UserEmailConfiguration) && emailEventType.UserEmailConfiguration.Id == 0) {
          emailEventType.UserEmailConfiguration.IsSubscribed = false;
          this.profileService.AddEmailConfiguration(emailEventType.UserEmailConfiguration)
            .subscribe(result => {

              emailEventType.UserEmailConfiguration.Id = result;
              emailEventType.UserEmailConfiguration.IsSubscribed = false;
              this.loadingService.Hide();
              emailEventType.IsUnSubFromEvent = false;
              this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess, null);

            }, error => {

              this.loadingService.Hide();
              this.toasterService.ServerError();
            });
        }
      }
    }
  }

  salaryPerChange() {

    // this.ChangeRangeDropDown(Salaryperevnet.target.value);
    this.attbJobAlert.SalaryRangeId = 0;
    this.SelectedSalaryRanges = [];

    if (IsNotNull(this.salaryRanges) && this.salaryRanges.length > 0) {
      let selectSalaryRange = this.salaryRanges.filter(s => s.DurationTypeId == this.attbJobAlert.JobDurationId);

      if (IsNotNull(selectSalaryRange) && selectSalaryRange.length > 0) {
        this.SelectedSalaryRanges = selectSalaryRange;
      }
    }
  }

  UnSubOrSubFromGruopButton(emailEventType: EmailType, isSub: boolean) {
    if (IsNotNull(emailEventType)) {

      if (isSub == true) {

        emailEventType.IsUnSubFromEvent = true;

        if (IsNotNull(emailEventType.UserEmailConfiguration) && emailEventType.UserEmailConfiguration.Id > 0) {

          //emailEventType.UserEmailConfiguration.IsSubscribed = true;

          this.loadingService.Show();
          this.profileService.DeleteEmailConfiguration(emailEventType.UserEmailConfiguration.Id)
            .subscribe(result => {

              emailEventType.UserEmailConfiguration.Id = 0;
              emailEventType.IsUnSubFromEvent = true;
              //      this.toasterService.Success("UnSubscribed Successfully");
              this.loadingService.Hide();

            }, error => {

              this.loadingService.Hide();
              this.toasterService.ServerError();
            });
        }

      } else {

        if (IsNotNull(emailEventType.UserEmailConfiguration) && emailEventType.UserEmailConfiguration.Id > 0) {

          emailEventType.UserEmailConfiguration.IsSubscribed = false;

          this.loadingService.Show();
          this.profileService.UpdateEmailConfiguration(emailEventType.UserEmailConfiguration.Id, emailEventType.UserEmailConfiguration)
            .subscribe(result => {

              emailEventType.IsUnSubFromEvent = false;
              //      this.toasterService.Success("UnSubscribed Successfully");
              this.loadingService.Hide();

            }, error => {

              this.loadingService.Hide();
              this.toasterService.ServerError();
            });
        }
        else {
          emailEventType.UserEmailConfiguration.IsSubscribed = false;

          this.loadingService.Show();
          this.profileService.AddEmailConfiguration(emailEventType.UserEmailConfiguration)
            .subscribe(result => {

              emailEventType.UserEmailConfiguration.Id = result;
              emailEventType.IsUnSubFromEvent = false;
              //      this.toasterService.Success("UnSubscribed Successfully");
              this.loadingService.Hide();

            }, error => {

              this.loadingService.Hide();
              this.toasterService.ServerError();
            });
        }
      }

    }
  }

  OnFrequencyChangeHandler(emailEvent: EmailType) {


    if (IsNotNull(emailEvent) && IsNotNull(emailEvent.UserEmailConfiguration)) {

      if (IsNotNull(emailEvent.UserEmailConfiguration.Id) && emailEvent.UserEmailConfiguration.Id > 0) {

        if (emailEvent.UserEmailConfiguration.FrequencyId == 5) {

          emailEvent.UserEmailConfiguration.IsSubscribed = false;
          emailEvent.IsUnSubFromEvent = false;
        } else {

          emailEvent.UserEmailConfiguration.IsSubscribed = true;
          emailEvent.IsUnSubFromEvent = true;
        }
        this.loadingService.Show();

        this.profileService.UpdateEmailConfiguration(emailEvent.UserEmailConfiguration.Id, emailEvent.UserEmailConfiguration)
          .subscribe(RequestAcceptResults => {

            this.loadingService.Hide();
            this.toasterService.Success(null, UnSubscribeEmailMessages.OnUnSubscribedEmailFrequencyChanged);
            //this.LoadUserConnections();        
          }, error => {

            this.loadingService.Hide();
          });


      } else {


        if (IsNotNull(emailEvent) && IsNotNull(emailEvent.Id) && emailEvent.UserEmailConfiguration.FrequencyId == 5) {

          //5 is never call unsubscibed function here                          
          emailEvent.UserEmailConfiguration.IsSubscribed = false;
          emailEvent.IsUnSubFromEvent = false;
        } else {

          emailEvent.UserEmailConfiguration.IsSubscribed = true;
          emailEvent.IsUnSubFromEvent = true;
        }

        this.profileService.AddEmailConfiguration(emailEvent.UserEmailConfiguration)
          .subscribe(result => {

            emailEvent.UserEmailConfiguration.Id = result;
            this.loadingService.Hide();
            this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess, null);

          }, error => {

            this.loadingService.Hide();
            this.toasterService.ServerError();
          });

        //this.toasterService.Error("You haven't subscribed");
      }

    } else {

      // To do what you want when user configuration object not exists      
    }

  }

  UnSubOrSubBySavedSearch(savedSearch: SaveSearches, isSub: boolean) {
    if (IsNotNull(isSub) && isSub == true) {

      if (IsNull(savedSearch.EmailFrequencyId) || savedSearch.EmailFrequencyId == 0) {

        savedSearch.EmailFrequencyId = 2;// default daily
      }

      this.loadingService.Show();
      this.saveSearchesService.UpdateSaveSearchId(savedSearch.Id, savedSearch)
        .subscribe(result => {

          savedSearch.IsEmailAlertActive = true;

          this.toasterService.Success(UnSubscribeEmailMessages.OnSubscribedEmailSuccessfully, null);
          this.loadingService.Hide();

        }, error => {
          this.loadingService.Hide();
        });

    } else if (IsNotNull(isSub) && isSub == false) {

      this.loadingService.Show();
      savedSearch.EmailFrequencyId = null;
      this.saveSearchesService.UpdateSaveSearchId(savedSearch.Id, savedSearch)
        .subscribe(result => {

          savedSearch.EmailFrequencyId = 0;
          savedSearch.IsEmailAlertActive = false;

          this.toasterService.Success(UnSubscribeEmailMessages.OnUnSubscribedEmailSuccess, null);
          this.loadingService.Hide();

        }, error => {
          this.loadingService.Hide();
        });

    }
  }

  //isBrowser:boolean=false;
  IsShowMoreOption: boolean = false;
  IsShowMoreOptionClicked: boolean = false;
  SalaryTo: number;
  SalaryFrom: number;

  isChecked: boolean = false;
  checkValue(e: any) {
    console.log(e);
  }

  OnShowMoreClick(): void {
    this.IsShowMoreOption = !this.IsShowMoreOption;
    this.IsShowMoreOptionClicked = true;
  }

  OnSubmitRequest() {
    //Do something here!
  }

  LoadATTBAlerts() {
    if (this.contextService.IsUserLoggedIn) {

      this.attbJobAlertService.GetAttbJobAlertsByUserId(this.contextService.LoggedInUserId).subscribe(res => {      
        if (IsNotNull(res)) {

          this.alreadySetAlerts = res;

          if (IsNotNull(this.alreadySetAlerts) && this.alreadySetAlerts.length > 0) {

            this.attbJobAlert.FrequencyId = this.alreadySetAlerts[0].FrequencyId;
          }
        }
      });
    }
  }

  OnSubmitAttbAlertForm() {    
    this.IsAttbAlertFormSubmitted = true;
    if (this.AttbAlertForm.valid) {
      this.IsAttbAlertFormSubmitted = false;
      this.attbJobAlert.UserId = this.contextService.LoggedInUserId;

      this.confirmationService.OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          if (IsNotNull(this.attbJobAlert.Id) && this.attbJobAlert.Id != 0) {

            this.UpdateRecord();

          } else {
            this.loadingService.Show();

            this.attbJobAlertService.SearchJobAlert(null,
              this.attbJobAlert.DesiredKewords, this.attbJobAlert.DesiredLocation, this.attbJobAlert.Radius,
              this.attbJobAlert.JobType, this.attbJobAlert.JobContract, this.attbJobAlert.MinimumSalary,
              this.attbJobAlert.MaximumSalary, this.attbJobAlert.UserId, null, null, 1, null, null).subscribe(m => {

                this.loadingService.Hide();

                if (IsNotNull(m) && m.length > 0) {
                  this.attbJobAlert.Id = m[0].Id;
                  this.UpdateRecord();

                } else {

                  this.InsertNewRecord();
                }

              }, err => {
                this.loadingService.Hide();
                this.toasterService.Error(null, Useralerts.OnSubmitFailSaveMessage);
                return;
              });
          }
        }
      });
    }
  }

  OnSubmitTotalJobAlertForm() {
    this.IsTotalJobFormSubmitted = true;
    if (this.TotalJobAlertForm.valid) {
      this.IsTotalJobFormSubmitted = false;
      if (this.contextService.IsUserLoggedIn == false) {
        this.toasterService.Error(null, Useralerts.OnSubmitLoggedInMessage);
        return;
      }
      if (IsNullOrEmpty(this.TotalJobAlert.SearchKeyword)) {
        this.toasterService.Error(Useralerts.OnSubmitDesiredKeywordMessage, null);
        return;
      }
      if (IsNullOrEmpty(this.TotalJobAlert.LocationText)) {
        this.toasterService.Error(Useralerts.OnSubmitDesiredLocationMessage, null);
        return;
      }

      this.TotalJobAlert.UserId = this.contextService.LoggedInUserId;
      this.TotalJobAlert.EmailConsent = true;

      this.confirmationService.OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          this.loadingService.Show();
          this.totalJobAlertService.SaveTotalJobAlert(this.TotalJobAlert).subscribe(result => {
            this.loadingService.Hide();
            this.toasterService.Success(null, Useralerts.UpdateRecordUpdatedMessage);
            let element = document.getElementById('TotalJobAlertsModal');
            if (IsNotNull(element)) {
              element.click();
            }
            this.InitTotalJobAlert();
            this.LoadTotaljobAlerts();
          }, err => {
            this.loadingService.Hide();
            this.toasterService.Error(null, Useralerts.OnSubmitFailSaveMessage);
            return;
          });
        }
      });
    }
  }

  OnSubmitJobGateAlertForm() {
    this.IsJobGateFormSubmitted = true;
    if (this.JobGateAlertForm.valid) {
      this.IsJobGateFormSubmitted = false;
      if (this.contextService.IsUserLoggedIn == false) {
        this.toasterService.Error(null, Useralerts.OnSubmitLoggedInMessage);
        return;
      }
      if (IsNullOrEmpty(this.JobGateAlert.SearchKeyWord)) {
        this.toasterService.Error(Useralerts.OnSubmitDesiredKeywordMessage, null);
        return;
      }
      if (IsNullOrEmpty(this.JobGateAlert.LocationText)) {
        this.toasterService.Error(Useralerts.OnSubmitDesiredLocationMessage, null);
        return;
      }

      this.JobGateAlert.UserId = this.contextService.LoggedInUserId;
      this.JobGateAlert.EmailConsent = true;

      this.confirmationService.OpenConfirmationDialogue(isConfirmed => {
        if (isConfirmed) {
          this.loadingService.Show();
          this.jobGateAlertService.SaveJobgateAlert(this.JobGateAlert).subscribe(result => {
            this.loadingService.Hide();
            this.toasterService.Success(null, Useralerts.UpdateRecordUpdatedMessage);
            let element = document.getElementById('JobG8AlertsModal');
            if (IsNotNull(element)) {
              element.click();
            }
            this.InitJobgGateAlert();
            this.LoadJobGateAlerts();
          }, err => {
            this.loadingService.Hide();
            this.toasterService.Error(null, Useralerts.OnSubmitFailSaveMessage);
            return;
          });
        }
      });
    }
  }

  LoadTotaljobAlerts() {
    if (this.contextService.IsUserLoggedIn) {
      this.totalJobAlertService.GetTotalJobAlertsByUserId(this.contextService.LoggedInUserId).subscribe(res => {
        if (IsNotNull(res)) {
          this.TotalJobAlerts = res;
        }
      });
    }
  }

  LoadJobGateAlerts() {
    if (this.contextService.IsUserLoggedIn) {
      this.jobGateAlertService.GetJobGateAlertsByUserId(this.contextService.LoggedInUserId).subscribe(res => {
        if (IsNotNull(res)) {
          this.JobGateAlerts = res;
        }
      });
    }
  }

  SalaryValidation() {

    let isValid = true;
    if ((IsNotNull(this.attbJobAlert.MinimumSalary) && (this.attbJobAlert.MinimumSalary != 0)) && (IsNotNull(this.attbJobAlert.MaximumSalary) && (this.attbJobAlert.MaximumSalary != 0))) {

      let salaryFrom = parseInt(this.attbJobAlert.MinimumSalary.toString());
      let salaryTo = parseInt(this.attbJobAlert.MaximumSalary.toString());

      if (salaryFrom > salaryTo) {

        this.toasterService.Error(UserAlertMessages.OnMinAndMaxSalaryValidationError, null);
        isValid = false;

      } else {

        if (this.attbJobAlert.JobDurationId == 5) {

          if ((salaryFrom < 1000) || (salaryTo < 1000)) {

            this.toasterService.Error(UserAlertMessages.OnAlrestValidationSalaryError, null);
            isValid = false;
          }
        }

      }

    } else {

      isValid = false;
      this.toasterService.Error(CommonMessages.OnSavingSalaryFromAndSalaryToRequired, null);
    }

    return isValid;
  }

  UpdateRecord() {

    this.loadingService.Show();

    // console.log("Just Before Update:", this.attbJobAlert);
    this.attbJobAlertService.UpdateAttbJobAlerts(this.attbJobAlert.Id, this.attbJobAlert)
      .subscribe(res => {

        this.loadingService.Hide();
        this.toasterService.Success(null, Useralerts.UpdateRecordUpdatedMessage);
        let element = document.getElementById('ATTBAlertsModal');
        if (IsNotNull(element)) {
          element.click();
        }
        //this.resetForm();
        this.InitAttbAlert();
        this.selectedFrequencyId = 1;
        this.LoadATTBAlerts();

      }, err => {

        this.loadingService.Hide();

      });
  }

  InitAttbAlert() {
    this.attbJobAlert = new AttbJobAlert();
    this.attbJobAlert.Id = 0;
    this.attbJobAlert.DesiredKewords = "";
    this.attbJobAlert.DesiredLocation = "";
    this.IsAttbAlertFormSubmitted = false;
  }

  InitTotalJobAlert() {
    this.TotalJobAlert = new TotalJobAlert();
    this.TotalJobAlert.Id = 0;
    this.TotalJobAlert.SearchKeyword = "";
    this.TotalJobAlert.LocationText = "";
    this.TotalJobAlert.EmailConsent = true;
    this.IsTotalJobFormSubmitted = false;
  }

  InitJobgGateAlert() {
    this.JobGateAlert = new JobGateJobAlert();
    this.JobGateAlert.Id = 0;
    this.JobGateAlert.SearchKeyWord = "";
    this.JobGateAlert.LocationText = "";
    this.JobGateAlert.EmailConsent = true;
    this.IsJobGateFormSubmitted = false;
  }


  InsertNewRecord() {
    this.loadingService.Show();
    this.attbJobAlert.FrequencyId = this.selectedFrequencyId;
    this.attbJobAlertService.SaveAttbJobAlerts(this.attbJobAlert)
      .subscribe(res => {
        this.loadingService.Hide();
        this.toasterService.Success(null, Useralerts.InsertNewRecordInsertMessage);
        let element = document.getElementById('ATTBAlertsModal');
        if (IsNotNull(element)) {
          element.click();
        }
        this.InitAttbAlert();
        this.selectedFrequencyId = 1;
        this.LoadATTBAlerts();
      }, err => {
        this.loadingService.Hide();
      });
  }

  isNumber(evt) {
    evt = (evt) ? evt : window.event;
    var charCode = (evt.which) ? evt.which : evt.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  OnDeleteTotalJobAlert(totalJobAlertId: number) {
    this.confirmationService.SetMessage(Useralerts.OnDeleteConfirmationMessage).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        this.totalJobAlertService.DeleteTotalJobAlert(totalJobAlertId).subscribe({
          next: result => {
            this.loadingService.Hide();
            this.toasterService.Success(null, Useralerts.OnDeleteDeletedSuccessfullyMessage);
            this.InitTotalJobAlert();
            this.LoadTotaljobAlerts();
            return;
          },
          error: errorResult => {
            this.loadingService.Hide();
            this.toasterService.ServerError();
          }
        });
      }
    });
  }

  OnDeleteJobGateAlert(jobGateAlertId: number) {
    this.confirmationService.SetMessage(Useralerts.OnDeleteConfirmationMessage).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        this.jobGateAlertService.DeleteJobGateAlert(jobGateAlertId).subscribe({
          next: result => {
            this.loadingService.Hide();
            this.toasterService.Success(null, Useralerts.OnDeleteDeletedSuccessfullyMessage);
            this.LoadJobGateAlerts();
            return;
          },
          error: errorResult => {
            this.loadingService.Hide();
            this.toasterService.ServerError();
          }
        });
      }
    });
  }

  OnEditAttbAlert(jobalert: AttbJobAlert) {
    this.attbJobAlert = JSON.parse(JSON.stringify(jobalert));

    if (IsNotNull(this.attbJobAlert)) {
      if (IsNull(this.attbJobAlert.JobType)) {
        this.attbJobAlert.JobType = 0;
      }

      if (IsNull(this.attbJobAlert.JobContract)) {
        this.attbJobAlert.JobContract = 0;
      }

      if (IsNull(this.attbJobAlert.SalaryCurrencyId)) {
        this.attbJobAlert.SalaryCurrencyId = 0;
      }

      if (IsNull(this.attbJobAlert.JobDurationId)) {
        this.attbJobAlert.JobDurationId = 0;
      }

      if (IsNull(this.attbJobAlert.SalaryRangeId)) {
        this.attbJobAlert.SalaryRangeId = 0;
      }

      if (IsNull(this.attbJobAlert.MinimumSalary)) {
        this.attbJobAlert.MinimumSalary = 0;
      }

      if (IsNull(this.attbJobAlert.MaximumSalary)) {
        this.attbJobAlert.MaximumSalary = 0;
      }
    }
  }

  OnDeleteAttbAlert(alertId: number) {
    this.confirmationService.SetMessage(Useralerts.OnDeleteConfirmationMessage).OpenConfirmationDialogue(isConfirmed => {
      if (isConfirmed) {
        this.loadingService.Show();
        this.attbJobAlertService.DeleteAttbJobAlerts(alertId).subscribe(result => {
          this.loadingService.Hide();
          this.toasterService.Success(null, Useralerts.OnDeleteDeletedSuccessfullyMessage);
          this.resetForm();
          return;
        }, e => {
          this.loadingService.Hide();
        });
      }
    });
  }

  resetForm() {
    this.attbJobAlert = new AttbJobAlert();
    this.attbJobAlert.FrequencyId = 1;
    this.LoadATTBAlerts();
  }

  RefreshUserEmailConfigurations(isRefreshRequired: any) {
    this.LoadEmailConfiguration();
  }

  ngOnDestroy() {
    if(IsNotNull(this.UnsubscribeAll)){
      this.UnsubscribeAll.unsubscribe();
    }
    if(IsNotNull(this.UnsubscribeJobAlerts)){
      this.UnsubscribeJobAlerts.unsubscribe();
    }
  }

  PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleDashBoard;
    listItem.item = `${environment.HostName}${DashBoard_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = BreadcrumbsTitleManageAlerts;
    listItem2.item = `${environment.HostName}${EmailAlert_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);
  }

}
