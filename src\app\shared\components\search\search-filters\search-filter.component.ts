import { Component, DestroyRef, Input, OnInit, Simple<PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CandidateType } from '@apply4u/models/cvsearchresult';
import { Duration } from '@apply4u/models/duration';
import { Jobtype } from '@apply4u/models/jobtype';
import { Positiontype } from '@apply4u/models/positiontype';
import { Sector } from '@apply4u/models/sector';
import { ContextService } from '@apply4u/services/context-service';
import { MasterDataService } from '@apply4u/services/masterdata.service';
import { SearchService } from '@apply4u/services/search/search.service';
import { SelectPlease, SelectSubPlease } from '@apply4u/shared/constant/constant';
import { SmartLocationAutocompleteConstant } from '@apply4u/shared/constant/smart-location-autocomplete.constant';
import { IsAny, IsNotNull, IsNotNullOrEmpty } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { SearchDuration } from '@apply4u/models/search/search-duration';
import { SearchDurations } from '@apply4u/shared/constant/search-within/search-durations';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { JobSearchDurations } from '@apply4u/shared/constant/search-within/job-search-duration';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'search-filter',
  templateUrl: './search-filter.component.html',
  styleUrls: ['./search-filter.component.css']
})
export class SearchFilterComponent implements OnInit {
  destroyRef = inject(DestroyRef);
  @Input() SectorName: string = null;   
  @Input() SelectedJobTypes: string = null;
  JobTypes: Jobtype[] = [];
  SalaryDurations: Duration[] = [];
  Sectors: Sector[] = [];
  SubSectors: Sector[];
  AllSectors: Sector[] = [];
  AllSubSectors: Sector[];

  CandidateTypes: CandidateType[] = [];
  PositionTypes: Positiontype[] = [];
  ContextService: ContextService;
  SearchService: SearchService;
  IsSearchFilterInitialized: boolean = false;
  IsSubSectorsLoaded: boolean = false;
  IsSectorsLoaded: boolean = false;
  SearchWithInFilterCollection: SearchDuration[] = SearchDurations;
  JobSearchWithInFilterCollection: SearchDuration[] = JobSearchDurations;
  SalaryValidationForm: UntypedFormGroup;
  globalSearchUncheckedvalues: number[] = [];

  constructor(private masterDataService: MasterDataService,
    private _searchService: SearchService,
    private formBuilder: UntypedFormBuilder,
    private toasterService: ToasterMessageService,
    private _contextService: ContextService) {
    this.ContextService = this._contextService;
    this.SearchService = this._searchService;
    this.InitializeValidationForms();
    if (typeof window !== 'undefined' && window.localStorage) {
      this.globalSearchUncheckedvalues = JSON.parse(localStorage.getItem('user unselect item'));     
    }
  }

  ngOnInit(): void {
    this.LoadSectors();
    if (!this.ContextService.IsMobileView) {
      this.InitializeFilters();
    }

    this.SearchService.CheckRadiusDisableStatus();    
  }

  ngOnChanges(changes: SimpleChanges) {
    if (IsNotNull(changes['SelectedJobTypes'])) {
      if (this.ContextService.IsBrowser) {
        let userSelectedJobTypes = changes['SelectedJobTypes'].currentValue;

        if (IsNotNull(userSelectedJobTypes)) {
          if (IsNotNullOrEmpty(this.SearchService.SearchParams.JobTypes)) {
            let selectedJobTypes = this.SearchService.SearchParams.JobTypes.split(',').map(d => parseInt(d));
            this.JobTypes.filter(d => selectedJobTypes.indexOf(d.Id) > -1).forEach(jobType => {              
              if (this.globalSearchUncheckedvalues?.length && this.globalSearchUncheckedvalues.includes(jobType.Id)) {
              
                jobType.isChecked = false;
              }else{
                jobType.isChecked = true;
              }
            })
          }
        }
      }
    }
  }

  isAnySectorChecked(): boolean {
    return this.Sectors.some(sector => sector.IsChecked);
  }

  InitializeValidationForms(): void {
    this.SalaryValidationForm = this.formBuilder.group({
     MaxGreaterThanMin: [Validators.required],
    });
  }

  InitializeFilters(): void {
    if (this.ContextService.IsJobSeeker) {
      this.masterDataService.getJobType().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(jobTypes => {
        let jtypes = jobTypes.filter(f => f.Id != 1);

        let jobTypesSort = IsAny(jtypes) ? JSON.parse(JSON.stringify(jtypes)) : [];
        this.JobTypes = jobTypesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? -1 : 1);

        if (IsNotNullOrEmpty(this.SearchService.SearchParams.JobTypes)) {
          let selectedJobTypes = this.SearchService.SearchParams.JobTypes.split(',').map(d => parseInt(d));
          this.JobTypes.filter(d => selectedJobTypes.indexOf(d.Id) > -1).forEach(jobType => {
            if (this.globalSearchUncheckedvalues?.length && this.globalSearchUncheckedvalues.includes(jobType.Id)) { 
              jobType.isChecked = false;
            }else{
              jobType.isChecked = true;
            }
          });
        }
      });   
    }

    this.masterDataService.getDuration().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(salaryDurations => {
      this.SalaryDurations = IsAny(salaryDurations) ? JSON.parse(JSON.stringify(salaryDurations)) : [];
    });

    if (this.ContextService.IsRecruiter) {
      this.masterDataService.getCandidateTypes().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(candidateTypes => {
        let candidateTypesSort = IsAny(candidateTypes) ? JSON.parse(JSON.stringify(candidateTypes)) : [];
        this.CandidateTypes = candidateTypesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? 1 : -1);

        if (IsNotNullOrEmpty(this.SearchService.SearchParams.ProfileTypes)) {
          let selectedProfileTypes = this.SearchService.SearchParams.ProfileTypes.split(',').map(d => parseInt(d));
          this.CandidateTypes.filter(d => selectedProfileTypes.indexOf(d.Id) > -1).forEach(profileType => {
            profileType.IsChecked = true;
          });
        }
      });

      this.masterDataService.getPositionType().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(positionTypes => {
        let postypes = positionTypes.filter(pos => pos.Id != 8);
        let positionTypesSort = IsAny(postypes) ? JSON.parse(JSON.stringify(postypes)) : [];
        this.PositionTypes = positionTypesSort.sort((a, b) => a.Description.toLowerCase() < b.Description.toLowerCase() ? -1 : 1);

        if (IsNotNullOrEmpty(this.SearchService.SearchParams.PositionTypes)) {
          let selectedPositionTypes = this.SearchService.SearchParams.PositionTypes.split(',').map(d => parseInt(d));
          this.PositionTypes.filter(d => selectedPositionTypes.indexOf(d.Id) > -1).forEach(positionType => {
            positionType.IsChecked = true;
          });
        }
      });
    }

    this.IsSearchFilterInitialized = true;
  }

  OnJobTypeChange(typeId: number): void {
    if (IsAny(this.JobTypes)) {
      if (typeId == 1) {
        this.JobTypes.filter(d => d.Id != 1).forEach(element => {
          element.isChecked = false;
        });
      } else if (typeId != 1) {
        this.JobTypes.filter(d => d.Id == 1).forEach(element => {
          element.isChecked = false;
        });
      }

      if (IsAny(this.globalSearchUncheckedvalues)) {
        let addedItem = this.JobTypes.find(d => d.isChecked && this.globalSearchUncheckedvalues.includes(d.Id))

        if (addedItem) {
          this.globalSearchUncheckedvalues = this.globalSearchUncheckedvalues.filter(item => item !== addedItem.Id);
        }
      }        

      let selectedJobArray = this.SearchService.SearchParams.JobTypes.split(',').map(d => parseInt(d));

      if (!this.globalSearchUncheckedvalues?.length) {
        this.globalSearchUncheckedvalues = this.JobTypes.filter(d => d.isChecked === false && selectedJobArray.includes(d.Id)).map(d => d.Id);
      }else{
        const newFilteredValues = this.JobTypes.filter(d => d.isChecked === false && !this.globalSearchUncheckedvalues.includes(d.Id) && selectedJobArray.includes(d.Id)).map(d => d.Id);
        this.globalSearchUncheckedvalues = [...this.globalSearchUncheckedvalues, ...newFilteredValues];
      }

      localStorage.setItem('user unselect item', JSON.stringify(this.globalSearchUncheckedvalues));

      let selectedJobTypes = this.JobTypes.filter(d => d.isChecked);

      if (selectedJobTypes.length > 0) {
        this.SearchService.SearchParams.JobTypes = selectedJobTypes.map(d => d.Id).join(',');
      } else {
        this.SearchService.SearchParams.JobTypes = "";
      }
    }

    this.ApplyFilterHandler();
  }  

  LoadSectors() {
    if (!this.IsSectorsLoaded) {
      this.IsSectorsLoaded = true;

      this.masterDataService.getSector(null).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(sectors => {
        this.Sectors = IsAny(sectors) ? sectors : [];

        if (IsAny(this.Sectors)) {
          if (IsNotNullOrEmpty(this.SearchService.SearchParams.SectorIds)) {
            let selectedSectorIds = this.SearchService.SearchParams.SectorIds.split(',').map(d => parseInt(d));
            this.Sectors.filter(d => selectedSectorIds.indexOf(d.Id) > -1).forEach(sector => {
              sector.IsChecked = true;
            });
          }else {
            this.Sectors.forEach(sector => {
              sector.IsChecked = false;
            });
          }
        }
      });
    }
  }

  OnSectorSelectionChange(): void {
    if (IsAny(this.Sectors)) {
      let selectedSector = this.Sectors.filter(d => d.IsChecked);

      if (selectedSector.length > 0) {
        this.SearchService.SearchParams.SectorIds = selectedSector.map(d => d.Id).join(',');
      } else {
        this.SearchService.SearchParams.SectorIds = "";
      }
    }

    this.OnSectorChange();
  }

  OnSectorChange(): void {    
    this.SearchService.SearchParams.IsSimpleSearch = true;
    this.SearchService.SearchParams.IsBooleanSearch = false;   

    this.ApplyFilterHandler();
    return;
  }

  OnSearchWithinChange() {
    this.ApplyFilterHandler();
  }

  OnSalaryPerChange() {
    this.ApplyFilterHandler();
  }

  OnProfileTypeChange(profiletypeId: number): void {
    let selectedProfileTypes = this.CandidateTypes.filter(d => d.IsChecked);

    if (selectedProfileTypes.length > 0) {
      this.SearchService.SearchParams.ProfileTypes = selectedProfileTypes.map(d => d.Id).join(',');
    } else {
      this.SearchService.SearchParams.ProfileTypes = "";
    }

    this.ApplyFilterHandler();
  }

  OnIncludeUnspecifiedSalariesChange(): void {
    this.ApplyFilterHandler();
  }

  OnPositionTypeChange(positiontypeId: number): void {
    if (IsAny(this.PositionTypes)) {
      if (positiontypeId == 8) {
        this.PositionTypes.filter(d => d.Id != 8).forEach(element => {
          element.IsChecked = false;
        });
      } else if (positiontypeId != 8) {
        this.PositionTypes.filter(d => d.Id == 8).forEach(element => {
          element.IsChecked = false;
        });
      }
    }

    let selectedPositionTypes = this.PositionTypes.filter(d => d.IsChecked);

    if (selectedPositionTypes.length > 0) {
      this.SearchService.SearchParams.PositionTypes = selectedPositionTypes.map(d => d.Id).join(',');
    } else {
      this.SearchService.SearchParams.PositionTypes = "";
    }

    this.ApplyFilterHandler();
  }

  ApplyFilterHandler(): void {
    if (this.SalaryValidationForm.invalid) {
      this.toasterService.Error("Please correct the salary range to apply the filters.");
    } else {
      this.SearchService.SearchParams.IsFilterSearch = true;
      this.SearchService.SearchRedirection();
    }
  }

  OnResetSideBarFilters(): void {
    this.SearchService.SearchParams.SalaryDuration = 0;

    this.PositionTypes.forEach(element => {
      element.IsChecked = false;
    });
    this.JobTypes.forEach(element =>{
      element.isChecked =false;
    });

    this.SearchService.SearchParams.SearchWithIn = -1;

    this.CandidateTypes.forEach(element => {
      element.IsChecked = false;
    });

    this.SearchService.SearchParams.SectorName = SelectPlease;
    this.SearchService.SearchParams.SubSectorName = SelectSubPlease;
    this.SearchService.SearchParams.SalaryFrom = null;
    this.SearchService.SearchParams.SalaryTo = null;
    this.SearchService.SearchParams.IsFilterSearch = false;
    this.SearchService.SearchRedirection();
    this.globalSearchUncheckedvalues = [];
    localStorage.removeItem('user unselect item');
  }

  OnRadiusChange(): void {
    this.ApplyFilterHandler();
  }  

  OnSearchFilterToggle(): void {
    this.SearchService.IsFilterExpandedMobile = !this.SearchService.IsFilterExpandedMobile;

    if (!this.IsSearchFilterInitialized) {
      this.InitializeFilters();
      this.LoadSectors();
    }
  }
  
  OnFocusRadiusDropdownBox(): void {
    if (this.SearchService.IsRadiusDisabled) {
      if (this.ContextService.IsBrowser) {
        let element = document.getElementById(SmartLocationAutocompleteConstant.LocationSelectionId);
        if (IsNotNull(element)) {
          element.focus();
        }
      }
    }
  }

  restrictInput(event: KeyboardEvent): void {
    const allowedKeys = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', '.'];
    const isNumberKey = event.key >= '0' && event.key <= '9';
    if (!allowedKeys.includes(event.key) && !isNumberKey) {
      event.preventDefault();
    }
    const inputElement = event.target as HTMLInputElement;
    if (event.key === '.' && inputElement.value.includes('.')) {
      event.preventDefault();
    }
    const valueWithoutDecimal = inputElement.value.replace('.', '');
    if (!inputElement.value.includes('.') && valueWithoutDecimal.length >= 7 && isNumberKey) {
      event.preventDefault();
    }
  }

  validateDecimal(event: Event, field: 'SalaryFrom' | 'SalaryTo'): void {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;
    if (!value || value.startsWith('.')) {
      inputElement.value = '';
      this.SearchService.SearchParams[field] = null;
      return;
    }
    const validDecimalPattern = /^\d{0,7}(\.\d{0,2})?$/;
    if (!validDecimalPattern.test(value)) {
      const parts = value.split('.');
      const integerPart = parts[0].substring(0, 7); 
      const decimalPart = parts[1] ? parts[1].substring(0, 2) : ''; 
      value = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
      inputElement.value = value;
    }
    this.SearchService.SearchParams[field] = parseFloat(value);
    this.OnSalaryChange();
  }
  
  OnSalaryChange(): void {
    const salaryFrom = parseFloat(String(this.SearchService?.SearchParams.SalaryFrom ));
    const salaryTo = parseFloat(String(this.SearchService?.SearchParams.SalaryTo ));
  
    if (salaryFrom != null && salaryTo != null && salaryTo < salaryFrom ) {
      this.SalaryValidationForm.controls['MaxGreaterThanMin'].setErrors({ required: true });
    } else {
      this.SalaryValidationForm.controls['MaxGreaterThanMin'].setErrors(null);
    }
  }

}
