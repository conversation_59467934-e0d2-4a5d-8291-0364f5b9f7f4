
<div class="container mt-2" style="min-height: 800px;"> 
  <breadcrumb-seo [BreadCrumbSchema]="BreadCrumbSchema"></breadcrumb-seo>
        <app-smart-tabs activeTabName="PAYMENT_HISTORY"></app-smart-tabs>
        <h1 class="mb-4 mt-3 text-uppercase"><i class="fas fa-money-bill mr-1"></i>
            My Payment History</h1>
        <div *ngIf="userPackage?.length == 0" class="alert alert-info">There are no payments against your account.</div>
        <ul *ngIf="userPackage?.length > 0" class="list-unstyled timeline-sm">
            <li *ngFor="let package of userPackage;index as indexId" class="timeline-sm-item">
              <span class="timeline-sm-date">{{package.DateFrom | date}}</span>
                <div class="row">
                    <div class="col-md-12">
                      <div style="margin-left: 31px;" [style.width]="contextService.IsMobileView?'80%':'50%'">
                        <span *ngIf="package.IsSubscriptionActive==null && package.IsUserHaveFreePackage" class="badge badge-info">One-time payment</span>
                        <div *ngIf="package.IsSubscriptionActive==null && package.IsSystemGenerated && !package.IsUserHaveFreePackage && package.Id !=0"> 
                        <span class="badge badge-danger">Subscription error</span><a href="javascript:void(0)"><span class="underline ml-2" data-toggle="modal" data-target="#subscriptionErrorModal">(View Detail)</span></a></div>
                        <span *ngIf="package.IsSubscriptionActive" class="badge badge-success">Active subscription</span>
                        <span *ngIf="package.SubscriptionStatus=='Inactive'" class="badge badge-danger">Cancelled subscription</span>
                        <a href="javascript:void(0);" *ngIf="package?.IsSubscriptionActive" class="text-danger no-underline" style="float:right" (click)="cancelSubscription(package)"><i class="fas fa-ban"></i> <span class="underline">Cancel</span></a>
                      </div>
                        <div class="PersonalDetails">
                          <a class="collapsed package-accordian no-underline" data-toggle="collapse" [href]="'#Package-'+indexId">
                            <h2><i class="fa fa-shopping-bag mr-2"></i>  
                              <span style="cursor: pointer;" class="underline" data-toggle="modal">{{package.PackageName}} <span *ngIf="package.IsSystemGenerated">Package</span> </span>
                              <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                            </h2>
                          </a>
                            <div id="Package-{{indexId}}" [ngClass]="indexId==0?'collapse show':'collapse'">
                            <div class="row">
                              <div class="col-md-12 mb-2">
                                <span style="color: #336699;font-size: 14px;" class="ml-2">{{package.Description.replace('|Cancel anytime','')}}
                                  <a data-toggle="modal" data-target="#tailoredPackageDetailModal"
                                         *ngIf="!package.IsSystemGenerated" 
                                         href="javascript:void(0);" class="" style="color: #3366bb;"> <span class="underline">(View Detail)</span></a>
                                         <a (click)="getPackageDetail(package)"
                                         *ngIf="package.IsSystemGenerated && package.Id > 0" 
                                         href="javascript:void(0);" class="" style="color: #3366bb;"> <span class="underline">(View Detail)</span></a>
                                </span>    
                              </div>
                                <div *ngIf="package.DateFrom!=null" class="col-md-4 col-sm-12">
                                  <h4 class="ml-md-2 ml-2"><i class="fas fa-calendar-alt"></i> Start Date : <span class="">{{package.DateFrom | date}}</span></h4>
                                </div>
                                <div *ngIf="package.DateTo!=null" class="col-md-3 col-sm-12">
                                  <h4 class="ml-md-3 ml-2"><i class="fas fa-calendar-alt"></i> End Date : <span class="">{{package.DateTo | date}}</span></h4>
                                </div>
                            </div>
                            <div *ngIf="package.UserYearlyPayments?.length > 1">
                              <ul *ngFor="let yearlyPayments of package.UserYearlyPayments;index as paymentIndex1" class="list-unstyled sub-timeline-sm">
                                <li class="timeline-sm-item yearlyPayments">
                                  <a class="collapsed yearlyPayments-accordian no-underline" data-toggle="collapse" [href]="'#PackageDet1-'+paymentIndex1+'-'+indexId">
                                    <span class="title font-weight-bold underline"> {{yearlyPayments.PaymentYear}}</span>
                                    <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                                  </a>
                                  <div id="PackageDet1-{{paymentIndex1}}-{{indexId}}" class="collapse">
                                  
                              <ul *ngFor="let monthlyPayments of yearlyPayments.UserMonthlyPayments;index as paymentIndex" class="list-unstyled sub-timeline-sm">
                                <li class="timeline-sm-item monthlyPayments">
                                  <a class="collapsed monthlyPayments-accordian no-underline" data-toggle="collapse" [href]="'#PackageDet-'+paymentIndex+'-'+indexId">
                                    <span class="title underline"> {{monthlyPayments.MonthName}}</span>
                                    <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                                  </a>
                                  <div id="PackageDet-{{paymentIndex}}-{{indexId}}" [ngClass]="indexId == 0 && paymentIndex == 0?'collapse show':'collapse'">
                                    <div *ngFor="let payment of monthlyPayments?.UserPayments; let last=last">
                                      <div class="row m-0 P-Detail mt-2">
                                        <div class="col-md-3 col-sm-12 ">
                                          <label>Payment Date</label>
                                        </div>
                                        <div class="col-md-8 col-sm-12 ml-md-5">
                                          <span>{{payment.CreatedOn | date}}</span>
                                        </div>
                                      </div>
                                      <div class="row m-0 P-Detail">
                                        <div class="col-md-3 col-sm-12">
                                          <label>Amount</label>
                                        </div>
                                        <div class="col-md-8 col-sm-12 ml-md-5">
                                          <span>{{payment.Amount | currency:'GBP'}}</span>
                                        </div>
                                      </div>
                                      <div class="row m-0 P-Detail">
                                        <div class="col-md-3 col-sm-12">
                                          <label>Status</label>
                                        </div>
                                        <div class="col-md-8 col-sm-12 ml-md-5">
                                          <span [ngClass]="getStatusAndColor(payment.GoCardlessStatus).class">{{getStatusAndColor(payment.GoCardlessStatus).status}} </span>
                                          <a data-toggle="modal" class="no-underline" data-target="#paymentReceiptModal" (click)="getUserPaymentReceipt(payment.PaymentId)"
                                           *ngIf="getStatusAndColor(payment.GoCardlessStatus).status == 'Paid'" 
                                           href="javascript:void(0);" style="color: #3366bb;"> <i class="fa fa-print"></i> <span class="underline">View Receipt</span> </a>
                                           <a data-toggle="modal" class="no-underline" data-target="#failedPaymentModal" (click)="getUserFailedPaymentEvent(payment.PaymentId)"
                                           *ngIf="getStatusAndColor(payment.GoCardlessStatus).status == 'Failed'" 
                                           href="javascript:void(0);" style="color: #3366bb;"> <i class="fa fa-eye"></i> <span class="underline">View Detail</span></a>
                                        </div>
                                      </div>
                                      <hr style="margin-left: 15px;" [style.width]="contextService.IsMobileView?'85%':'50%'"/>
                                    </div>
                                </div>
                                </li>
                              </ul>
                            </div>
                              </li>
                              </ul>
                            </div>
                            <div *ngIf="package.UserYearlyPayments?.length == 1">
                              <ul *ngFor="let monthlyPayments of package.UserYearlyPayments[0].UserMonthlyPayments;index as paymentIndex" class="list-unstyled sub-timeline-sm">
                                <li class="timeline-sm-item monthlyPaymentsDetails">
                                  <a class="collapsed monthlyPayments-Details no-underline" data-toggle="collapse" [href]="'#PackageDet-'+paymentIndex+'-'+indexId">
                                    <span class="title underline">{{monthlyPayments.MonthName}}</span>
                                    <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
                                  </a>
                                  <div id="PackageDet-{{paymentIndex}}-{{indexId}}" [ngClass]="indexId == 0 && paymentIndex == 0?'collapse show':'collapse'">
                                  <div *ngFor="let payment of monthlyPayments?.UserPayments; let last=last">
                                    <div class="row m-0 P-Detail mt-2">
                                      <div class="col-md-3 col-sm-12 ">
                                        <label>Payment Date</label>
                                      </div>
                                      <div class="col-md-8 col-sm-12 ml-md-5">
                                        <span>{{payment.CreatedOn | date}}</span>
                                      </div>
                                    </div>
                                    <div class="row m-0 P-Detail">
                                      <div class="col-md-3 col-sm-12">
                                        <label>Amount</label>
                                      </div>
                                      <div class="col-md-8 col-sm-12 ml-md-5">
                                        <span>{{payment.Amount | currency:'GBP'}}</span>
                                      </div>
                                    </div>
                                    <div class="row m-0 P-Detail">
                                      <div class="col-md-3 col-sm-12">
                                        <label>Status</label>
                                      </div>
                                      <div class="col-md-8 col-sm-12 ml-md-5">
                                        <span [ngClass]="getStatusAndColor(payment.GoCardlessStatus).class">{{getStatusAndColor(payment.GoCardlessStatus).status}} </span>
                                        <a data-toggle="modal" data-target="#paymentReceiptModal" (click)="generateUserPaymentReceiptPdf(payment.PaymentId)"
                                         *ngIf="getStatusAndColor(payment.GoCardlessStatus).status == 'Paid'" 
                                         href="javascript:void(0);" class="no-underline" style="color: #3366bb;"> <i class="fa fa-print"></i> <span class="underline ml-1">View Receipt</span></a>
                                         <a data-toggle="modal" data-target="#failedPaymentModal" (click)="getUserFailedPaymentEvent(payment.PaymentId)"
                                         *ngIf="getStatusAndColor(payment.GoCardlessStatus).status == 'Failed'" 
                                         href="javascript:void(0);" class="" style="color: #3366bb;"> <i class="fa fa-eye"></i> <span class="underline">View Detail</span></a>
                                      </div>
                                    </div>
                                    <hr style="margin-left: 15px;"  [style.width]="contextService.IsMobileView?'85%':'50%'"/>
                                  </div>
                                    
                                </div>
                                </li>
                              </ul>
                            </div>
                                </div>
                          </div>
                    </div>
                </div>
                <hr style="margin-left: 15px;" [style.width]="contextService.IsMobileView?'85%':'53%'" />
            </li>
        </ul>
  </div>

  <button #modal id="showModel" class="d-none" data-toggle="modal" data-target="#packageDetailModal"></button>
  <smart-modal [Id]="'packageDetailModal'" [Title]="'Package Detail'" [IsStatic]="false" [SizeClass]="'modal-lg'" [IsShowLogo]="false">
    <div modal-body>
     <app-user-package-detail [userPackageId]="userPackageId"></app-user-package-detail>
    </div>
  </smart-modal>
  <smart-modal [Id]="'tailoredPackageDetailModal'" [Title]="'Package Detail'" [IsStatic]="false" [SizeClass]="'modal-lg'" [IsShowLogo]="false">
    <div modal-body>
      <div class="row m-0">
        <p>This is a Tailored. This was directly assigned to you by A4U support team. </p>
        <p>No direct payment was made on the website, rather our support team fulfilled this package assignment and settled  this payment manually.</p>
      </div>
    </div>
  </smart-modal>
  <smart-modal [Id]="'failedPaymentModal'" [Title]="'Payment Detail'" [IsStatic]="false" [SizeClass]="'modal-lg'" [IsShowLogo]="false">
    <div modal-body>
      <div class="row m-0">
        <p>{{paymentFailedEvent?.Description}}</p>
      </div>
    </div>
  </smart-modal>
  <smart-modal [Id]="'paymentReceiptModal'" [Title]="'Payment Receipt'" [IsStatic]="false" [SizeClass]="'modal-lg'" [IsShowLogo]="false">
    <div modal-body>
      <div class="row" id="userPaymentReceipt">
        <embed [src]="pdfSrc" type="application/pdf" width="100%" height="600px" title="My PDF Document">
    </div>
    </div>
    <!-- <div modal-footer class="row m-0 justify-content-center mt-10">
      <a (click)="generateUserPaymentReceiptPdf(paymentReceipt?.PaymentId)" class="btn btn-primary waves-effect waves-light"><i class="fa fa-cloud-download mr-1"></i> Download</a>
    </div> -->
  </smart-modal>
  <smart-modal [Id]="'subscriptionErrorModal'" [Title]="'Subscription Error Detail'" [IsStatic]="false" [SizeClass]="'modal-lg'" [IsShowLogo]="false">
    <div modal-body>
      <div class="row m-0">
        <p>There was an error while creating your subscription.  </p>
        <p>please contact at info&#64;apply4u.co.uk</p>
      </div>
    </div>
  </smart-modal>





 
