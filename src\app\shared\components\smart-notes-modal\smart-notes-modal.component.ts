import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Shared } from '@apply4u/models/shared';
import { UserNote } from '@apply4u/models/user-notes/user-note';
import { ContextService } from '@apply4u/services/context-service';
import { LoadingService } from '@apply4u/services/loading.service';
import { LocalstorageService } from '@apply4u/services/localstorage.service';
import { NotesService } from '@apply4u/services/notes.service';
import { ProfileService } from '@apply4u/services/profile.service';
import { ToasterMessageService } from '@apply4u/services/toaster-message.service';
import { ConfirmationService } from '@apply4u/shared/components/confirmation-modal/confirmation.service';
import { LoginTypeKeys } from '@apply4u/shared/constant/Local-storage-keys/login-type-keys';
import { CommonMessages } from '@apply4u/shared/constant/messages/common-messages';
import { UserNotesModalMessages } from '@apply4u/shared/constant/messages/user-notes-modal-messages';
import { NoteForType } from '@apply4u/shared/enums/note-for-type.enum';
import { NoteType as NoteTypeEnum } from '@apply4u/shared/enums/note-type.enum';
import { IsAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber, IsTrue } from '@apply4u/shared/helpers/common/type-safe-helpers';

@Component({
  selector: 'smart-notes-modal',
  templateUrl: './smart-notes-modal.component.html',
  styleUrls: ['./smart-notes-modal.component.css']
})
export class SmartNotesComponent implements OnInit, OnChanges {
  @Input() CandidateId: number;
  @Input() NoteTypeId: number = NoteTypeEnum.RecruiterNote;
  @Input() NoteForTypeId: number;
  @Output() OnNoteChange: EventEmitter<UserNote> = new EventEmitter<UserNote>();
  UserNote: UserNote;
  UserNotes: UserNote[];
  Heading: string;
  DomainUrl: string = Shared.baseUrl;
  UserNoteForTypeId : number = 2;
  UserNotesText : string = '';
  isPublicNotesAllowed: boolean = false;
  PublicNotesReadOnly: boolean = false;

  constructor(private toasterService: ToasterMessageService,
    private loadingService: LoadingService,
    private confirmationService: ConfirmationService,
    private notesServices: NotesService,
    private localStorageService: LocalstorageService,
    private profileService: ProfileService,
    private contextService: ContextService) {
      this.NewUserNote();
    }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (IsNotZero(changes['NoteTypeId']?.currentValue) || IsNotZero(changes['CandidateId']?.currentValue) || IsNotZero(changes['NoteForTypeId']?.currentValue)) {
      
      if(IsNotZero(changes['NoteForTypeId']?.currentValue) && changes['NoteForTypeId']?.currentValue == 3){
        this.PublicNotesReadOnly = true;
      }

      this.Heading = `${this.NoteTypeId == NoteTypeEnum.RecruiterNote ? 'Notes' : 'Feedback'} For Candidate: ${this.CandidateId}`;
      this.NewUserNote();
      this.OnChangeNoteTypeLoadData();
      this.isPublicNotesAllowed = JSON.parse(localStorage.getItem('IspublicStaticNotesAllowed'))
    
    }
  }

  NewUserNote(): void {
    if(IsNotZeroNumber(this.NoteForTypeId)){
      this.UserNoteForTypeId = this.NoteForTypeId == 4?3:this.NoteForTypeId;
      //this.OnChangeNoteTypeLoadData();
    }else {
      this.UserNoteForTypeId = 2;
    }
    
    this.UserNote = new UserNote();
    this.UserNote.Id = 0;
    this.UserNote.UserId = this.contextService.LoggedInUserId;
    this.UserNote.UserName = this.localStorageService.GetItem(LoginTypeKeys.LoggedInUserTitle);
    this.UserNote.CandidateId = this.CandidateId;
    this.UserNote.NoteForTypeId = this.NoteTypeId == NoteTypeEnum.RecruiterNote ? NoteForType.Company : NoteForType.Personal;
    this.UserNote.NoteTypeId = this.NoteTypeId;
    this.UserNote.JobId = null;
    this.UserNote.Notes = "";
    this.UserNote.LastUpdated = new Date();

    this.UserNotesText = "";
  }

  OnChangeNoteTypeLoadData(): void {
    if (this.contextService.IsUserLoggedIn) {
      this.loadingService.Show();
      this.notesServices.getCandidateNotes(this.contextService.LoggedInUserId, this.UserNote.CandidateId, this.NoteTypeId, this.UserNoteForTypeId == 3 ? 4 : this.UserNoteForTypeId)
        .subscribe({
          next: result => {
            if (IsAny(result)) {
              this.UserNotes = result;
              if (IsAny(this.UserNotes)) {
                this.UserNotes = this.UserNotes.reverse();
                if (this.UserNotes[0].NoteTypeId == 3) {
                  this.UserNotesText = this.UserNotes[0].Notes;
                }
              }
            } else {
              this.UserNotes = [];
            }

            this.loadingService.Hide();
          }, error: error => {
            this.loadingService.Hide();
            this.toasterService.Error(UserNotesModalMessages.OnLoadNotesDataError, null);
          }
        });
    }
  }

  SaveCandidateNotes(): void {
    if (IsNotNull(this.UserNote) && this.UserNote.NoteTypeId == 3) {
      if (IsAny(this.UserNotes)) {
        this.UserNote.Id = this.UserNotes[0].Id;
      }
    }
    this.UserNote.Notes = this.UserNotesText;
    this.UserNote.NoteForTypeId = this.UserNoteForTypeId;
    if (IsNotZero(this.UserNote.Id)) {
      this.loadingService.Show();
      this.notesServices.updateCandidateNotes(this.UserNote.Id, this.UserNote)
        .subscribe({
          next: res => {
            this.loadingService.Hide();
            this.toasterService.UpdatedSuccessfully();
            document.getElementById("SmartNotesModal").click();            
            this.UserNotesText = "";
            this.OnNoteChange.emit(this.UserNote);
          },error: error => {
            this.loadingService.Hide();
            this.toasterService.Error(UserNotesModalMessages.OnSavingNotesError, null);
          }
        });
    } else {
      if(IsNotNullOrEmpty(this.UserNote.Notes)){
      this.loadingService.Show();      
      this.UserNote.NoteTypeId = parseInt(this.UserNote.NoteTypeId.toString());
      this.notesServices.addCandidateNotes(this.UserNote)
        .subscribe({
          next: result => {
            this.UserNote.Id = result;
            this.UserNotesText = "";
            this.loadingService.Hide();           
            this.toasterService.SavedSuccessfully();
            document.getElementById("SmartNotesModal").click();
            this.UserNotes.push(this.UserNote);
          

            if (IsNotNullOrEmpty(this.UserNote.Notes)) {
              this.OnNoteChange.emit(this.UserNote);
            }

            this.NewUserNote();
          
          },error: error => {
            this.loadingService.Hide();
            this.toasterService.Error(CommonMessages.OnSomethingWentWrongError, null);
          }
        });
      }else {
        this.toasterService.Error("Please enter notes first.", null);
      }
    }
  }

  OnNoteEdit(id: number, notes : string ): void {
    this.UserNotesText = notes;
    let userNote = this.UserNotes.filter(x=> x.Id == id)[0]; 

    if (IsNotNull(userNote)) {
      this.UserNote = userNote;
    }
  }

  OnNoteDelete(noteId: number): void {
    console.log(noteId);

    this.confirmationService.SetMessage("Are you sure to delete this Note?").OpenConfirmationDialogue((isOk: any) => {
      if (isOk) {
        this.notesServices.DeleteCandidateNotes(noteId).subscribe(result => {
          this.toasterService.Success(CommonMessages.OnDeleteSuccessfully);
          this.OnNoteChange.emit(this.UserNote);

          if (IsNotZeroNumber) {
            this.UserNotes = this.UserNotes.filter(d => d.Id != noteId);
          }
        });
      }
    });
  }

  CloseSmartNotesModal(isClosed : boolean) : void {
    if(IsTrue(isClosed)) {
      this.NewUserNote();
    }
  }
}
