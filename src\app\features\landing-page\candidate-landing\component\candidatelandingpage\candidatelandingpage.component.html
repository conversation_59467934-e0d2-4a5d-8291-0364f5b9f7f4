<!--new banner -->
<section class="NewTheme">
  <div class="row all-section-maxwidth px-3 px-xl-0 mx-auto">
    <div class="col-12 col-lg-6 px-0 mb-3 mb-md-5 mb-lg-0">
      <div class="row mx-0 HomeSlider pr-0 pr-lg-4 pr-xl-0">
        <div class="col-12 px-0 px-xl-3 MobileFix top-section-heading">
          <h1>Get <b style="color: #14C59C;"> Hired Faster</b></h1>
          <h1>with Apply4U<span style="color: #14C59C;">.</span></h1>
          <h3 class="mt-4">Your Helpful Job Site</h3>
        </div>
      </div>
    </div>
    <div class="col-12 col-lg-6 top-userform px-3 pt-2"
      style="border: 1px solid white;border-radius: 4px; background-color: #f6f7f8;">
      <div class="row px-2">
        <h1 class="text-left ml-2 mb-2 form-heading" style="color:#14C59C"><span class="ml-1"
            style="font-weight: 400 !important;">Let's <b>get started!</b> </span></h1>
        <div class="col-12">
          <form [formGroup]="PersonalDetailsValidationForm" novalidate>
            <div class="row ">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                <input type="text" placeholder="First Name" id="_firstName" class="form-control" name="firstName"
                  autocomplete="off" formControlName="FirstName" [(ngModel)]="RegistrationForm.UserProfile.FirstName"
                  [ngClass]="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['FirstName'].invalid && PersonalDetailsValidationForm .controls['FirstName'].errors['required'] ? 'border border-danger' : ''">
                <div
                  *ngIf="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['FirstName'].invalid && PersonalDetailsValidationForm .controls['FirstName'].errors['required']"
                  class="alert alert-danger mt-2">
                  <i class="fas fa-info-circle"></i> Please enter first name.
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                <input type="text" placeholder="Last Name" id="_lastName" class="form-control" name="lastName"
                  autocomplete="off" formControlName="LastName" [(ngModel)]="RegistrationForm.UserProfile.LastName"
                  [ngClass]="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['LastName'].invalid && PersonalDetailsValidationForm .controls['LastName'].errors['required'] ? 'border border-danger' : ''">
                <div
                  *ngIf="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['LastName'].invalid && PersonalDetailsValidationForm .controls['LastName'].errors['required']"
                  class="alert alert-danger mt-2">
                  <i class="fas fa-info-circle"></i> Please enter last name.
                </div>
              </div>
            </div>
            <div class="row ">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                <input type="password" placeholder="Enter Password" id="_userPassword" formControlName="Password"
                  class="form-control" name="Password" autocomplete="off" [(ngModel)]="RegistrationForm.Password"
                  [(ngModel)]="RegistrationForm.Password" [type]="IsPassword ? 'password' : 'text'"
                  [ngClass]="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['Password'].invalid && PersonalDetailsValidationForm .controls['Password'].errors['required'] ? 'border border-danger' : ''">
                <span (click)="ShowHidePassword()" class="fa fa-fw field-icon"
                  [ngClass]="IsPassword ? ' fa-eye':' fa-eye-slash'"></span>
                <div
                  *ngIf="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['Password'].invalid && PersonalDetailsValidationForm .controls['Password'].errors['required']"
                  class="alert alert-danger mt-2">
                  <i class="fas fa-info-circle"></i> Please enter password.
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                <input type="password" placeholder="Confirm Password" id="_confirmuserPassword"
                  formControlName="ConfirmPassword" class="form-control" name="ConfirmPassword" autocomplete="off"
                  [(ngModel)]="RegistrationForm.ConfirmPassword" [(ngModel)]="RegistrationForm.ConfirmPassword"
                  [type]="IsConfirmPassword ? 'password' : 'text'"
                  [ngClass]="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['ConfirmPassword'].invalid && PersonalDetailsValidationForm .controls['ConfirmPassword'].errors['required'] ? 'border border-danger' : ''">
                <span (click)="ShowHideConfrimPassword()" class="fa fa-fw field-icon"
                  [ngClass]="IsConfirmPassword ? ' fa-eye':' fa-eye-slash'"></span>
                <div
                  *ngIf="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['ConfirmPassword'].invalid && PersonalDetailsValidationForm .controls['ConfirmPassword'].errors['required']"
                  class="alert alert-danger mt-2">
                  <i class="fas fa-info-circle"></i> Please Confirm password.
                </div>
              </div>
            </div>
            <div class="row ">
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2">
                <input type="email" placeholder="Enter Email" id="_userEmail" class="form-control" name="UserName"
                  autocomplete="off" formControlName="Email" [(ngModel)]="RegistrationForm.LoginName"
                  [ngClass]="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['Email'].invalid && PersonalDetailsValidationForm .controls['Email'].errors['required'] ? 'border border-danger' : ''">
                <div
                  *ngIf="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['Email'].invalid && PersonalDetailsValidationForm .controls['Email'].errors['required']"
                  class="alert alert-danger mt-2">
                  <i class="fas fa-info-circle"></i> Please enter valid email address.
                </div>
                <div
                  *ngIf="IsPerDetailsFormSubmitted && PersonalDetailsValidationForm.controls['Email'].invalid && PersonalDetailsValidationForm.controls['Email'].errors['pattern']"
                  class="alert alert-danger">
                  <i class="fas fa-info-circle"></i> Please enter a valid email address
                </div>
              </div>
              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 mb-2 text-center">
                <p class="small text-black mb-0 mr-1" style="margin-top: -6px;">Choose CV file: .docx is preferred
                  format</p>
                <span>
                  <label id="_bbUploadCV" class="uploadfiler mt-1 header-cv-btn cllapse smaller required inline-b"
                    title="{{CvHint}}">{{ buttonText }}
                    <input type="file" id="uploadFiles" class="hidden" style="display: none;"
                      (change)="OnSelectedCVFileChange($event)">
                  </label>
                  <div class=" text-ellipsis" *ngIf="IsSelectedFileValid">
                    <p class="small text-center mb-0 text-dark" title="{{CVName}}">{{CVName}}</p>
                  </div>
                </span>

              </div>
            </div>
          </form>
        </div>
      </div>
      <div class="px-2">
        <div class="row">
          <div class="col-12 col-md-8">
            <p class="text-dark mb-2">Please email me my FREE Professional CV review </p>
          </div>
          <div class="col-12 col-md-4">
            <span>
              <select class="mt-1 mb-2 mb-lg-0 w-100" name="TopCv" [(ngModel)]="RegistrationForm.IsTopCV"
                [ngModelOptions]="{ standalone: true }">
                <option [value]="true">Yes, sure!</option>
                <option [value]="false">No, maybe later!</option>
              </select>
            </span>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <span class="form-group form-check mb-2">
              <input type="checkbox" name="fmcv"  class="form-check-input"
                [ngModelOptions]="{ standalone: true }" [(ngModel)]="RegistrationForm.IsFreeMyCV">
              <label class="form-check-label ">
                <p class="text-dark mb-0">Distribute my CV to UK's top
                  employers.</p>
              </label>
              <span><a data-toggle="collapse" href="#moretermcondition">
                  <strong class="text-underline pl-1" style="color:#000">Read More</strong>
                </a></span>
            </span>
            <p id="moretermcondition" class="collapse" style="line-height: 36px; color: #000;"> By ticking this box you
              agree to <a class="text-underline" style="color: #14C59C;"
                href="https://www.freemycv.com/TermsAndConditions.aspx" target="_blank">Terms & Conditions</a>
              and give us your consent to distribute your details to other
              job sites. This will significantly increase your chances of getting a job.
            </p>

          </div>
        </div>
        <div class="row">
          <div class="col">
            <span class="form-group form-check">
              <input type="checkbox" name="termsAndConditions" [ngModelOptions]="{ standalone: true }"
                class="form-check-input" [(ngModel)]="RegistrationForm.AcceptOurTermsAndCondition">
              <label style="line-height: 30px;" class="text-dark form-check-label">By registering you
                accept our <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                  routerLink="/privacy-and-terms" [queryParams]="{tab: 'terms'}" target="_blank" class="text-underline"
                  style="color:#14C59C">Terms
                  & Conditions</a> and <a routerLink="/privacy-and-terms" target="_blank" class="text-underline"
                  class="text-underline" style="color:#14C59C">Privacy
                  Policy</a> & are giving us
                consent
                to hold your data for the purposes of job searching.</label>
            </span>

          </div>
        </div>
        <div class="row mb-4">
          <div class="col">
            <button type="button" (click)="OnSubmit()" class="btn btn-start btn-indent br-3 text-uppercase">Get
              Started</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!--New Banner -->
<!--trustpilot-->
<section>
  <div class="ratings-container">
    <div class="row my-0 mx-auto all-section-maxwidth">
      <div class="col-md-12 py-3 px-3">
        <!-- TrustBox widget - Carousel -->
        <div id="trustbox-jobdetail-carousel" class="trustpilot-widget" data-locale="en-GB"
          data-template-id="53aa8912dec7e10d38f59f36" data-businessunit-id="5ad869fc089028000118a067"
          data-style-height="140px" data-style-width="100%" data-theme="dark" data-tags="Useful" data-stars="3,4,5"
          data-review-languages="en">
          <a href="https://uk.trustpilot.com/review/www.apply4u.co.uk" target="_blank" rel="noopener" style="color: #fff;">Trustpilot</a>
        </div>
        <!-- End TrustBox widget -->

      </div>
    </div>
  </div>
</section>
<!--trustpilot-->

<!--slider-->
<div class="container-fluid PageBg apply4u-choose-section">
  <div class="row CardsWrapper my-0 all-section-maxwidth mx-auto">
    <div class="col-12 px-0">
      <h1>Why <b>Apply4U</b>?</h1>
    </div>
    <br>
    <div class="w-100">
      <div class="row mx-0 -mx-md-3">
        <div class="col-12 px-0 text-center">

          <div id="carouselPartners" name="carouselPartners" class="carousel slide" data-ride="carousel">
            <div class="carousel-inner">
              <a data-slide="prev" href="#carouselPartners" class="carousel-control-prev">
                <i class="fas fa-chevron-left"></i>
              </a>
              <a data-slide="next" href="#carouselPartners" class="carousel-control-next">
                <i class="fas fa-chevron-right"></i>
              </a>
              <div class="carousel-item item active">
                <img src="assets/images/jobseeker/Free-CV-Review.webp" class="avatar rounded mx-auto d-block"
                  alt="Aspire">
                <h3 class="mt-3  slider-h3">Apply4Me</h3>
                <p>We not only apply to top companies after thorough research but can also follow up with employers <br>
                  to secure feedback.
                </p>
              </div>
              <div class="carousel-item item">
                <img src="assets/images/jobseeker/CV-Distribution.webp" class="avatar rounded mx-auto d-block"
                  alt="Total-Jobs">
                <h3 class="mt-3 slider-h3">CV & Profile Review</h3>
                <p> Our CV experts dive into both your CV and profile to help you make a fantastic impression on
                  potential employers. Get personalised tips to boost your job search and make your CV stand out!
                </p>
              </div>
              <div class="carousel-item item">
                <img src="assets/images/jobseeker/Job-Search-Career-Advice.webp" class="avatar rounded mx-auto d-block"
                  alt="pwc">
                <h3 class="mt-3 slider-h3">Career Advice Call</h3>
                <p>Feeling lost in your job search? Our Career Advice Call is here to guide you. Even if you're unsure
                  about your ideal job, we'll uncover it based on your passions and skills.
                </p>
              </div>
              <div class="carousel-item item">
                <img src="assets/images/jobseeker/matching-jobs_1.webp" class="avatar rounded mx-auto d-block"
                  alt="bupa">
                <h3 class="mt-3 slider-h3">Verified & Featured</h3>
                <p>Verify your profile to increase it's credibility, and as a featured candidate, you'll enjoy increased
                  visibility at the top of employer searches, opening doors to premium job opportunities.</p>
              </div>
              <div class="carousel-item item">
                <img src="assets/images/jobseeker/career-advice-call.webp" class="avatar rounded mx-auto d-block"
                  alt="nhs">
                <h3 class="mt-3 slider-h3">Interview Support</h3>
                <p>Our expert team will review your video introduction and provide personalised advice to enhance your
                  interview skills. We'll also conduct a realistic mock interview, helping you refine your performance
                  and boost your confidence.</p>
              </div>
              <div class="carousel-item item">
                <img src="assets/images/jobseeker/Verification-Screening-Call.webp"
                  class="avatar rounded mx-auto d-block" alt="nhs">
                <h3 class="mt-3 slider-h3">Tailored Job Alerts</h3>
                <p>Our advanced system, driven by both AI technology and human expertise, searches the job market to
                  find opportunities that precisely match your criteria.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!--slider-->

<!--Search jobs-->
<section class="liveJobs">
  <div class="row all-section-maxwidth mx-auto px-3" [ngClass]="ContextService.IsMobileView ? 'carousel slide': ''"
    id="carouselHelp" data-ride="carousel">
    <div class="carousel-inner">
      <div class="col-12 text-center">
        <h1><b>Search</b> jobs</h1>
      </div>
      <div class="row">
        <div [ngClass]="ContextService.IsMobileView ? 'carousel-item item active': 'col-lg-4 col-12'">
          <div class="accordion" id="PerInfo">
            <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard' : ''">
              <div class="card-header show in" data-toggle="collapse">
                <h2>Jobs by location</h2>
              </div>
              <div class="panel-collapse collapse show" aria-labelledby="headingTwo">
                <div class="panel-body">
                  <div class="row m-0">
                    <div class=" col-md-12 VideoCard">
                      <div class="card-body">
                        <img src="assets/images/LandingPage/webp/A4U_Location.jpg" alt="JOBS BY LOCATIONS"
                          title="JOBS BY LOCATIONS" class="img-responsive" width="100" height="100">
                        <ul>
                          <li *ngFor="let loc of MenuLocations">
                            <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                              [routerLink]="loc.JobSearchUrl" class="text-dark"
                              title="{{loc.RegionName}} Jobs | Show all Jobs in {{loc.RegionName}}">
                              <h3 class=" font-size">Jobs In {{loc.RegionName}}</h3>
                            </a>
                          </li>
                        </ul>
                        <div class=" col-md-12 text-center">
                          <button class="btn btn-start" type="button" (click)="ShowMoreLocations()">
                            {{ShowLessMoreLocation}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div [ngClass]="ContextService.IsMobileView ? 'carousel-item item': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
          <div class="accordion" id="PerInfo">
            <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard' : ''">
              <div class="card-header show in" data-toggle="collapse">
                <h2>Jobs by Industry</h2>
              </div>
              <div class="panel-collapse collapse show" aria-labelledby="headingOne">
                <div class="panel-body">
                  <div class="row m-0">
                    <div class=" col-md-12 VideoCard">
                      <div class="card-body">
                        <img src="assets/images/LandingPage/webp/A4U_Industry.jpg" alt="Find Jobs by Sectors"
                          title="JOBS BY SECTORS" class="img-responsive" width="100" height="100">
                        <ul>
                          <li *ngFor="let sect of Sectors">
                            <a routerLinkActive="active" [routerLinkActiveOptions]="{ exact: true }"
                              routerLink="{{sect.JobSearchUrl}}"
                              title="{{sect.SectorName}} Jobs | Show all Jobs in {{sect.SectorName}}">
                              <h3 class=" font-size">{{sect.SectorName}} Jobs</h3>
                            </a>
                          </li>
                        </ul>
                        <div class=" col-md-12 text-center">
                          <button class="btn btn-start" type="button" (click)="ShowMoreIndustries()">
                            {{ShowLessMoreIndustry}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div [ngClass]="ContextService.IsMobileView ? 'carousel-item item': 'col-xl-4 col-lg-4 col-md-12 col-sm-12'">
          <div class="accordion" id="PerInfo">
            <div class="card" [ngClass]="ContextService.IsMobileView ? 'CarouselCard' : ''">
              <div class="card-header show in" data-toggle="collapse">
                <h2>Popular Job searches</h2>
              </div>
              <div class="panel-collapse collapse show" aria-labelledby="headingTwo">
                <div class="panel-body">
                  <div class="row m-0">
                    <div class=" col-md-12 VideoCard">
                      <div class="card-body">
                        <img src="assets/images/LandingPage/webp/A4U-Popular.jpg" alt="Find Popular Job Searches"
                          title="POPULAR JOBS SEARCHES" class="img-responsive" width="100" height="100">
                        <ul>
                          <li *ngFor="let search of LandingPageSearches;">
                            <a title="{{search.Title}} Jobs | Show all {{search.Title}} Jobs" routerLinkActive="active"
                              [routerLinkActiveOptions]="{ exact: true }" routerLink="{{search.JobSearchUrl}}">
                              <h3 class=" font-size">{{search.Title}} Jobs </h3>
                            </a>
                          </li>
                        </ul>
                        <div class=" col-md-12 text-center">
                          <button class="btn  btn-start" type="button" (click)="ShowMorePopularSearches()">
                            {{ShoeMorePopulsrSearche}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ol *ngIf="ContextService.IsMobileView" class="carousel-indicators indicators">
      <li data-target="#carouselHelp" data-slide-to="0" class="active"></li>
      <li data-target="#carouselHelp" data-slide-to="1"></li>
      <li data-target="#carouselHelp" data-slide-to="2"></li>
    </ol>
  </div>
</section>
<!--Search jobs-->

<!--Testimonials-->
<section class="Registration">
  <div class="all-section-maxwidth px-3 mx-auto">
    <h1 class="text-center ">What <b>Our User</b>s Say</h1>
    <hr class="midline" />
    <div class="row size">
      <div class="card col-12 mt-2 px-0 px-md-3">
        <div id="carouselExampleControls" name="carouselExampleControls" class="carousel slide mb-5"
          data-ride="carousel" data-interval="5000">
          <div class="w-100 carousel-inner mb-3">
            <div class="carousel-item active">
              <div class="carousel-caption">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h2 class="text-white">Dan Robert</h2>
                    <small><q>When I was searching through for jobs, I just couldn’t
                        find the right ones to apply to. They either were below
                        my experience level or just not right. I was about to
                        give up but then Apply4U made me realise you could work
                        backwards, weird I know! I became a verified
                        professional and the jobs I wanted and better came
                        flooding towards me. What makes it even better is these
                        opportunities weren’t even yet advertised on any job
                        site.</q></small><br>
                    <small class="smallest mute">Got the job come to him</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item ">
              <div class="carousel-caption">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h2 class="text-white">Adam Ali</h2>
                    <small><q>I was getting interviews but they never led anywhere, I
                        didn’t know what I was doing wrong. I randomly started
                        talking to people in some communities then an Apply4U
                        expert pointed out a few things which I thought were
                        obvious, but apparently they weren’t! would you know it,
                        I mentioned the obvious in my next interview and got the
                        job!</q></small><br>
                    <small class="smallest mute">Got an offer after a little interview advice</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item ">
              <div class="carousel-caption">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h2 class="text-white">Sarah Richmond</h2>
                    <small><q>Honestly I wasn’t actively looking for a new job, so I
                        registered on Apply4U just to network and build up my
                        connections. I found it quite useful and it helped me
                        find a few new businesses to network with. One of these
                        businesses ended up messaging me with an interesting
                        opportunity which I just could refuse.</q></small><br>
                    <small class="smallest mute">Networked her way up</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item ">
              <div class="carousel-caption">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h2 class="text-white">Aaron Richardson</h2>
                    <small><q>I didn’t have much time to spend on looking for a new
                        job, so I made my profile on Apply4U after hearing that
                        it does the search for you. Within a few days I started
                        receiving calls and messages from employers and
                        recruiters. I had a few interviews and managed to secure
                        my new position.</q></small><br>
                    <small class="smallest mute">Saved time by allowing people to contact him</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item ">
              <div class="carousel-caption">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h2 class="text-white">Shane Gill</h2>
                    <small><q>I was invited to join Apply4U and went ahead in doing
                        so. Very quickly I realised it was a useful tool and
                        since then I’ve been using it most days. Already had
                        several job offers and am waiting on one more before
                        deciding which to go for.</q></small><br>
                    <small class="smallest mute">Owes it to a friend</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="carousel-item ">
              <div class="carousel-caption">
                <div class="row">
                  <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h2 class="text-white">Jaswinder Singh</h2>
                    <small><q>After job searching without any success for months. I
                        decided I needed to try something new. I came across the
                        free CV review offering and decided to take it up. I
                        then decided to get my CV written by a professional and
                        would you know it, within a few weeks I started to get
                        interviews. I really didn’t give it much thought before
                        this, but it really has opened my eyes.</q></small><br>
                    <small class="smallest mute">Got a job through a new CV</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="indicatorWraper">
            <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
              <span class="carousel-control-prev-icon" aria-hidden="true"><i class="fas fa-chevron-left"></i></span>
              <span class="sr-only">Previous</span>
            </a>
            <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
              <span class="carousel-control-next-icon" aria-hidden="true"><i class="fas fa-chevron-right"></i></span>
              <span class="sr-only">Next</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

</section>
<!--Testimonials-->

<!--Frequently Asked Questions-->
<div class="container-fluid PageBg">
  <div class="row FAQs my-0 mx-auto all-section-maxwidth">

    <div class="col-12">
      <h1>Frequently Asked Questions</h1>
    </div>

    <div class="col-lg-4 col-12">
      <img alt="Apply4u Frequently Asked Questions" title="FAQs About Job Search And Recruitment" class="img-responsive"
        src="assets/images/LandingPage/webp/A4U_Questions.jpg" width="100" height="100">
    </div>

    <div class="col-lg-8 col-12">
      <p>Apply4U is an award winning jobsite for top performers & rising superstars! We help you with your job search &
        speak to top employers help you gain interviews & feedback, so you can sit back & relax while we help you get
        the job you REALLY want.</p>
      <div class="accordion" id="accordionExample">
        <div class="card">
          <div class="card-header" data-toggle="collapse" data-target="#collapseOne">
            <span class="title">Is Apply4U Free?</span>
            <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
          </div>
          <div id="collapseOne" class="collapse show" data-parent="#accordionExample">
            <div class="card-body">
              Yes, 100% free to use for all jobseekers. You can upload your CV, search and apply to jobs, receive job
              alerts, get a free CV review, highlight your profile as pre-screened and get headhunted all for free using
              Apply4U.
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header collapsed" data-toggle="collapse" data-target="#collapseTwo">
            <span class="title">How does Apply4U make money?</span>
            <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
          </div>
          <div id="collapseTwo" class="collapse" data-parent="#accordionExample">
            <div class="card-body">
              We are not a recruitment agency so employers don’t pay us any fees to hire you, so they can offer you a
              higher salary :-). We make money from companies paying us to advertise their job vacancies and help them
              search CVs & recommend them the best candidates available.
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header collapsed" data-toggle="collapse" data-target="#collapseThree">
            <span class="title">Why Should I Upload My CV?</span>
            <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
          </div>
          <div id="collapseThree" class="collapse" data-parent="#accordionExample">
            <div class="card-body">
              Our clever technology allows employers to view your full CV profile but hides your name & contact details,
              so only those subscribers that are really interested in your profile will download your CV to contact you
              directly.
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header collapsed" data-toggle="collapse" data-target="#collapseFour">
            <span class="title">What Are The Next Steps?</span>
            <span class="accicon"><i class="fas fa-angle-down rotate-icon"></i></span>
          </div>
          <div id="collapseFour" class="collapse" data-parent="#accordionExample">
            <div class="card-body">
              If you follow these steps we are confident with our expert help & enticing employers to view your profile
              you will get 2x more interviews. Simply upload your CV (takes less than 60 seconds), get your free CV
              review, edit your profile, fill the screening form (to be 65% more likely to be viewed), set up job
              alerts, search & apply to many jobs, follow up, prepare well for your interviews & get the job you REALLY
              want.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 mt-1">
      <h1 class="mx-0 mb-0 mt-4">Want to Know More? <a routerLinkActive="active"
          [routerLinkActiveOptions]="{ exact: true }" routerLink="/contact-us"
          class="contact-links"><b>Contact Us</b></a>
        or Explore our
        <a href="JavaScript:Void(0);" title="Explore our Pricing Plans" class="cursor"
          (click)="OnClickOurPricingPageLink()"  class="contact-links"><b> Pricing
            Plans</b></a>
      </h1>
    </div>
  </div>
</div>
<!--Frequently Asked Questions-->

<!--trustpilot-->
<section class="Trustpilot" *ngIf="ContextService.IsDesktopView">
  <div class="trustpilot-widget all-section-maxwidth mx-auto px-3" id="trustbox-rating" data-locale="en-GB"
    data-template-id="53aa8912dec7e10d38f59f36" data-businessunit-id="5ad869fc089028000118a067"
    data-style-height="140px" data-style-width="100%" data-theme="light" data-tags="useful-jobseeker"
    data-stars="1,2,3,4,5" data-review-languages="en">
    <a href="https://uk.trustpilot.com/review/www.apply4u.co.uk" target="_blank" rel="noopener">Trustpilot</a>
  </div>
</section>
<!--trustpilot-->

<button id="btnCVUploadAIModal" class="d-none" data-toggle="modal" data-target="#modalCVUploadAIInfo"></button>
<smart-modal [Id]="'modalCVUploadAIInfo'" [SizeClass]="'modal-sm'" [ShowFooter]="false"
  [IsShowLogo]="false" [ShowCloseButton]="false"  [HideHeader]="true">
  <div modal-body>
    <div class="panel-body">
      <div class="row m-0">
        <div class=" col-md-12 p-0">
          <div class="form-group text-center">
            <h5>Bear with us, it may take a minute.</h5>
            <div class="spinner-border text-success" role="status">
              <span class="sr-only">loading...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</smart-modal>