h1, h2, h3, h4, h5, h6{font-size:24px;color:#333;font-weight: bold!important;}
p {font-size:15px;font-weight: 600!important;}
.card {border:1px solid #a3a3a3!important;}
.BgBlue {background:#0e1b5d;}
.SliderWrapper span.h4{
  color: #14C59C;
  font-size: 36px;
}
.recruiter .SliderWrapper span.h4{
  color: #000;
  font-size: 36px;
}

.nav-pills a, .nav-tabs a {
  color: #fff;
  font-size: 15px;
  font-weight: 500!important;
  text-decoration: none;
  background-color: transparent;
}
.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
  color: #070e32;
  background-color: #14C59C;
  padding: 6px 9px;
  font-size:15px;
  font-weight: 700!important;
}

.Story img {width: 100%;height: auto;}
.Story .accordion {width: 100%;}
.Story .card-header .title {font-size: 16px;color: #000;}
.Story .card-header .accicon {float: right;font-size: 20px;width: 1.2em;}
.Story .card-header {cursor: pointer;border-bottom: none;}
.Story .card {margin-bottom: 10px;border: 1px solid #ddd;}
.Story .card-body {border-top: 1px solid #ddd;}
.Story .card-header:not(.collapsed) .rotate-icon {transform: rotate(180deg);}

.OurPartner {padding: 80px 0px;background: #0e1b5c;}
.OurPartner h2{color:#fff!important;}
.OurPartner p{color:#fff!important;}

.slider {padding: 40px 20px;}
.RecruitmentAwards h1{color:#fff;}

.BlogSection {padding: 80px 0px;}
.BlogSection .card-body{padding: 0px;}
.BlogSection img{width: 100%;height: auto;}
.BlogSection .card-header{background:#0e1b5c;}
.BlogSection .card-header h2{color:#fff!important;}

#Charity .card-body p, #News .card-body p, #HelpfulFoundation .card-body p{color:#333!important;}
#News .card-body img{width: 100%;height:auto;}
#Charity .card .card-body img {width: 100%;height: auto;margin-bottom: 15px;border-radius: 10px;border: 1px solid #ccc;}
#Award .card .card-body ul{margin: 0;padding:0 0px;list-style: none;}

/* jobseeker */
.jobseeker {padding: 40px 0px;background: #0e1b5c;}
.image-2 {width: 10% !important;height: auto !important;position: relative;top: -15px;}
.jobseeker h1 {margin: 0;padding: 0;color: #ffffff;font-size: 58px;text-align: left;font-weight: bold !important;}
.jobseeker h2 {margin: 0;padding: 0;color: #fff;font-size: 60px;text-align: left;margin-top: 10px;font-weight: 700!important;}
.jobseeker h3 {color: #fff;font-size: 60px;text-align: left;font-weight: 700 !important;}
.jobseeker h3 span{color: #14C59C;}
.jobseeker h4 {color: #fff;font-size: 24px;font-weight: 700 !important;}
.jobseeker .btn-success {background-color: #14C59C;border-color: #14C59C;color: #0e1b5d;}
/* jobseeker  */

/* recruiter */
.recruiter {padding: 40px 100px;background: #05F1BE;}
.image-2 {width: 10% !important;height: auto !important;position: relative;top: -15px;}
.recruiter h1 {margin: 0;padding: 0;color: black;font-size: 60px;text-align: left;font-weight: bold !important;}
.recruiter h2 {margin: 0;padding: 0;color: #000;font-size: 60px;text-align: left;margin-top: 10px;font-weight: 700 !important;}
.recruiter h3 {color: black;font-size: 40px;text-align: left;font-weight: 400 !important;}
.recruiter h4 {color: black;font-size: 24px;font-weight: 700 !important;}
.recruiter .btn-success {background-color: #0e1b5d;border-color: #0e1b5d;color: #fff;}
/* recruiter */

.Trustpilot {padding: 50px 45px !important;}
.PageBg {background: #fff;}
.BannerBg {padding: 15px 0;display: inline-block;background: rgba(14, 27, 93);}

.WhyUseUs {padding: 40px 0;}
.WhyUseUs img {width: 100%;height: auto;margin-bottom: 15px;border-radius: 10px;border: 1px solid #ccc;}
.WhyUseUs p {color: #333333;text-align: left;}
.WhyUseUs ul {margin: 0;padding: 0;list-style: none;}
.WhyUseUs ul li {font-size: 15px;}
.WhyUseUs ul li i {font-size: 15px;}

.Testimonials {padding: 80px 0;background: #0e1b5c;border-bottom: 1px solid #fff;}
.Testimonials h1{color:#fff;}
.Testimonials p{color:#fff;}
.Testimonials .carousel-caption p{color:#333;}
.Testimonials .card {padding: 30px;}
.Testimonials .card .carousel-item {height: 110px;}
.Testimonials .card .carousel-caption {padding: 0;right: 0;left: 0;top: 0px;color: #3d3d3d;}
.Testimonials .card .carousel-caption h3 {color: #3d3d3d;}
.Testimonials .card .carousel-caption p {line-height: 20px;}
.Testimonials .card .carousel-caption .col-sm-3 {display: flex;align-items: center;}
.Testimonials .card .carousel-caption .col-sm-9 {text-align: left;}
.Testimonials .navi a {text-decoration: none;}
.Testimonials a>.ico {color: #fff;border-radius: 5px;background-color: #333;}
.Testimonials a:hover>.ico {color: #ccc;background-color: #222;}
.Testimonials span.ico {padding: 0 8px;}

.btn-primary {color: #fff;background: #0e1b5d;border-color: #0e1b5d;}
.btn-primary:hover {color: #fff;background: #081347;border-color: #081347;}

.TrustUs {padding: 80px 0px;}
.slide img {width: 150px;height: 100px; animation: scroll 60s linear infinite;}
.slide-track {width: 100%;display: flex;gap: 20px;}
.VideoCard {padding: 10px;text-align: center;border-radius: 8px;}
.contact  {font-size: 30px;font-weight:700!important;}

#main, #main2 {margin-top: 30px;}
#main #faq .card, #main2 #faq2 .card {margin-bottom: 20px;border: 0;}
#main #faq .card .card-header, #main2 #faq2 .card .card-header {
  border: 0;
  -webkit-box-shadow: 0 0 20px 0 rgba(213, 213, 213, 0.5);
          box-shadow: 0 0 20px 0 rgba(213, 213, 213, 0.5);
  border-radius: 2px;
  padding: 0;
}
#main #faq .card .card-header .btn-header-link, #main2 #faq2 .card .card-header .btn-header-link {
  color: #fff;display: block;text-align: left;background: #f2f2f2;color: #222;
  padding: 20px;font-weight: 700!important;text-decoration: none;font-size: 24px;
}
#main #faq .card .card-header .btn-header-link:after, #main2 #faq2 .card .card-header .btn-header-link:after {
  content: "\f107";font-family: 'Font Awesome 5 Free';
  font-weight: 900;float: right;
}
#main #faq .card .card-header .btn-header-link.collapsed, #main2 #faq2 .card .card-header .btn-header-link.collapsed {
  background: #0e1b5d;color: #14C59C;font-size: 24px;
  font-weight: 700!important;text-decoration: none;
}
#main #faq .card .card-header .btn-header-link.collapsed:after, #main2 #faq2 .card .card-header .btn-header-link.collapsed:after {
  content: "\f106";
}
#main #faq .card .collapsing, #main2 #faq2 .card .collapsing {background: #f2f2f2;line-height: 30px;}
#main #faq .card .collapse, #main2 #faq2 .card .collapse {border: 0;}
#main #faq .card .collapse.show, #main2 #faq2 .card .collapse.show {background: #f2f2f2;line-height: 30px;color: #222;}

.custom-arrow {
  background-image: none;
  text-align: center;
  color: #0e1b5c;
  font-size: 14px;
  background-color: #14C59C;
  height: 32px;
  line-height: 32px;
  width: 32px;
  border-radius: 50%;
}
.custom-arrow:hover{
  opacity: 0.85;
}


/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .PageBg {padding: 0px 15px;}
  .mobileView{padding: 8px 8px;}
  .BgBlue{margin:10px;}
  .M-10{margin:10px;}
  .jobseeker h2 {font-size:40px;}
  .WhyUseUs {padding: 20px 15px;}
  .Testimonials .card .carousel-item {height: 265px;}
  .recruiter {padding: 20px 0px 20px 0px;}
  .recruiter h1 {font-size: 60px;}
  .recruiter h4 {color: black;font-size: 24px; font-weight: 700 !important;}
  .recruiter h3 {color: black;text-align: left;font-weight: 700 !important;}
  .recruiter p {font-size: 15px;color: black;text-align: center;}
  .recruiter h1 {font-size: 51px;}
  .jobseeker h4 {color: #fff;font-size: 24px;font-weight: 700 !important;}
  .jobseeker h3 {color: #fff;font-size: 40px;text-align: left;font-weight: 700 !important;position: relative;}
  .jobseeker p {font-size: 15px;color: #fff;width: 340px !important;text-align: center;}
  .Testimonials {padding: 20px 0;}
  .Testimonials .card{margin: 15px;}
  .company-logo-section-padding{
    padding: 0px;
  }
}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {
.PageBg {padding: 0px 15px;}
.WhyUseUs {padding: 40px 15px;}
.CardsWrapper {padding: 40px 15px;}
.Testimonials .card .carousel-item {height: 150px;}
.jobseeker {padding: 20px 20px 20px 20px;}
.jobseeker h1 {font-size: 60px;}
.jobseeker h4 {color: #fff;font-size: 24px;font-weight: 700 !important;}
.recruiter {padding: 20px 20px 20px 20px;}
.recruiter h1 {font-size: 60px;}
.recruiter h4 {color: black;font-size: 24px;font-weight: 700 !important;}
.recruiter h3 {color: black;font-size: 25px;font-weight: 700 !important;}
.jobseeker p {font-size: 15px;color: #fff;text-align: center;}
.jobseeker h3 {color: #fff;font-size: 25px;font-weight: 700 !important;}
.recruiter p {font-size: 15px;color: black;text-align: center;}
}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {
  .CardsWrapper .card .card-body {min-height: 120px;}
  .RecruitmentAwards .card .card-body {min-height: 376px !important;}
  .Testimonials .card .carousel-item {height: 220px;}
  .jobseeker h1 {font-size: 55px;} 
  .recruiter h1 {font-size: 55px;}
  .jobseeker p {font-size: 15px;color: #fff;text-align: center;}
  .jobseeker h4 {color: #fff;font-size: 27px;font-weight: 700 !important;}
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .PageBg {padding: 0px 85px;background: #fff;}
  .OurStory {padding: 40px 85px;}
  .container {max-width: 1280px;}
  .WhyUseUs {padding: 40px 0px;}
  .CardsWrapper {padding: 40px 0px;}
  .Testimonials .card .carousel-item {height: 110px;}
  .AwardImg img {width: 40%;height: auto;}
  .jobseeker {padding: 30px 0px 20px 0px;}
  .jobseeker h1 {font-size: 60px;}
  .jobseeker h3 {color: #fff;font-size: 60px;text-align: left;font-weight: 700 !important;}
  .jobseeker h4 {color: #fff;font-size: 27px;font-weight: 700 !important;}
  .jobseeker p {font-size: 15px;color: #fff;text-align: center;}
  .recruiter p {font-size: 15px;color: black;text-align: center;}
  .recruiter h4 {color: black;font-size: 27px;font-weight: 700 !important;}
  .recruiter h3 {color: black;font-size: 60px;text-align: left;font-weight: 700 !important;}
}