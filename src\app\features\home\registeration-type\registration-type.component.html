<div class="container mt-4">
    <main>
      <p class="instruction">Join as a Jobseeker or Recruiter <b style="color: #14c59c;">?</b></p>
      <div class="options">
        <div class="option opt2" [class.selected]="selectedOption === 'jobseeker'" (click)="selectJobseeker()">
          <div class="option-content">
            <i class="fas fa-user"></i>
            <input type="radio" id="jobseekerRadio" name="userType" [checked]="selectedOption === 'jobseeker'">
          </div>
          <div class="mt-5 p-0 labelText">
            <label for="jobseekerRadio">I’m a jobseeker, looking for work</label>
          </div>
          <div class="col-md-12 shade">

          </div>
        </div>
        <div class="option opt1" [class.selected]="selectedOption === 'recruiter'" (click)="selectRecruiter()">
          <div class="option-content">
            <i class="fas fa-user-tie"></i>
            <input type="radio" id="recruiterRadio" name="userType" [checked]="selectedOption === 'recruiter'">
          </div>
          <div class="mt-5 p-0 labelText">
            <label for="recruiterRadio">I’m a recruiter, looking to hire</label>
          </div>
        </div>
      </div>
      <button class="create-account-button" [ngClass]="{'opt1-selected': selectedOption === 'recruiter','opt2-selected': selectedOption === 'jobseeker'}" [disabled]="!selectedOption">
        <a *ngIf="selectedOption" [href]="IsJobseeker ? Navigations.JobSeekerJoinNow : Navigations.RecruiterJoinNow" (click)="ContextService.OnRegisterClick()">{{ buttonText }}</a>
        <a href="JavaScript:Void(0);" *ngIf="!selectedOption">{{ buttonText }}</a>
      </button>
      <p class="login-text">Already have an account? <a href="JavaScript:Void(0);" on-click="SignInHandler()">Sign In</a></p>
    </main>
  </div>
  