<page-heading-recruiter [PageTitle]="'Search4Me'"
 [PageParagraph]="'Say goodbye to the recruitment grind. Our platform searches both our extensive database and external job boards to serve you a handpicked selection of the best candidates.'"></page-heading-recruiter>
 <div class="container-fluid PageBg">
  <div class="container">
    <breadcrumb-seo [BreadCrumbSchema]="BreadCrumbSchema"></breadcrumb-seo>
    <app-smart-tabs *ngIf="isRecruiter" [activeTabName]="'CV_SEARCH4ME'"></app-smart-tabs>
  </div>
  <section class="section bg-gray">
    <div class="container mb-3">     
      <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0">
          <div class="row m-0 mt-3 horizontal-cards-container">
            <div class="  col-lg-4 col-xl-4 col-md-4 p-0 mb-2" >
              <div class="card">
                <div class="card-body">
                  <p class="mb-2">Get matched with top talent. Find qualified candidates.</p>
                  <a id="btnRequestScreenedCVsModal" data-toggle="modal" data-target="#RequestScreenedCVsModal" class="mr-1 mb-0 d-xs" title="Request Screened CVs" (click)="RequestScreenedCVsModals()">
                    <!-- <i class="fa fa-user-edit"></i> -->
                    Request Search4Me
                  </a>
                </div>
              </div> 
            </div>
            <div class="  col-lg-4 col-xl-4 col-md-4 p-0 mb-2" >
              <div class="card">
                <div class="card-body">
                  
                  <p class="mb-2">Stop searching, start hiring. Review and manage requests here.</p>
                  <a class="mr-1 mb-0 d-xs" data-toggle="modal" data-target="#ViewRequestModal" title="View Requests">
                    <!-- <i class="fa fa-eye"></i> -->
                    View Requests
                  </a>
                </div>
              </div>  
            </div>
            <div class="  col-lg-4 col-xl-4 col-md-4 p-0 mb-2" >
              <div class="card">
                <div class="card-body">
                  
                  <p class="mb-2">Take control and effortless analysis. Export your results to Excel.</p>
                  <a class="mr-1 mb-0 d-xs" data-toggle="modal" data-target="#ExportToExcelModal" title="Export to Excel" (click)="InitExportModalControls()">
                    <!-- <i class="fa fa-file-export"></i> -->
                    Export
                  </a>
                </div>
              </div> 
            </div>
            <div class="  col-lg-4 col-xl-4 col-md-4 p-0 mb-2" >
              <div class="card">
                <div class="card-body">
                  
                  <p class="mb-2">Unlock all talent. See all candidates. Download Now!</p>
                  <a class="mr-1 mb-0 d-xs" data-toggle="modal" data-target="#DownloadModal" title="Download All" (click)="InitDownloadModalControls()">
                    <!-- <i class="fa fa-file-download"></i> -->
                    Download All CVs
                  </a>
                </div>
              </div> 
            </div>
            <div class="  col-lg-4 col-xl-4 col-md-4 p-0 mb-2" >
              <div class="card">
                <div class="card-body">
                  
                  <p class="mb-2">Your access to top talent. Invest in finding the perfect fit.</p>
                  <a class="mr-1 mb-0 d-xs" data-toggle="modal" data-target="#ViewCredits" title="Available credits" (click)="ViewCredits()">
                    <!-- <i class="fa fa-credit-card"></i> -->
                    View credits
                  </a>
                </div>
              </div>  
            </div>
          </div>
        
        
        <!-- List view for mobile -->
        <!-- <div *ngIf="ContextService.IsMobileView" class="container mt-4">
          <div class="card dropDownAlign">
            <div class="card-body">
              <span style="color: #0e1b5d; font-weight: 700;">
                Search4Me Options
                <i *ngIf="ViewState" class="fa fa-angle-down float-right" data-toggle="collapse" data-target="#search4MeCard" aria-expanded="false" aria-controls="StartJobCard" (click)="Toggle()"></i>
                <i *ngIf="!ViewState" class="fa fa-angle-up float-right" data-toggle="collapse" data-target="#search4MeCard" aria-expanded="false" aria-controls="StartJobCard" (click)="Toggle()"></i>
              </span>
            </div>
          </div>
          <div class="collapse" id="search4MeCard">
            <div class="card">
              <div class="card-body">
                  <a data-toggle="modal" data-target="#RequestScreenedCVsModal" class="mr-1 mb-2 d-xs" title="Request Screened CVs" (click)="RequestScreenedCVsModal = true;SelectedEditRequestId=0;">
                    <i class="fa fa-user-edit"></i>
                    Request Search4Me
                  </a>
                  <p>Get matched with top talent. Find qualified candidates.</p>
              </div>
            </div>
          </div>
        
          <div class="collapse" id="search4MeCard">
            <div class="card">
              <div class="card-body"> 
              <a class="mr-1 mb-2 d-xs" data-toggle="modal" data-target="#ViewRequestModal" title="View Requests">
                <i class="fa fa-eye"></i>
                View Requests
              </a>
              <p>Stop searching, start hiring. Review and manage requests here.</p>
              </div>
            </div>
          </div>
        
          <div class="collapse" id="search4MeCard">
            <div class="card">
              <div class="card-body">  
                <a class="mr-1 mb-2 d-xs" data-toggle="modal" data-target="#ExportToExcelModal" title="Export to Excel" (click)="InitExportModalControls()">
                  <i class="fa fa-file-export"></i>
                  Export
                </a>
                <p>Take control and effortless analysis. Export your results to Excel</p>
              </div>
            </div>
          </div>
        
          <div class="collapse" id="search4MeCard">
            <div class="card">
              <div class="card-body">
                
                <a class="mr-1 mb-2 d-xs" data-toggle="modal" data-target="#DownloadModal" title="Download All" (click)="InitDownloadModalControls()">
                  <i class="fa fa-file-download"></i>
                  Download All CVs
                </a>
                <p>Unlock all talent. See all candidates. Download Now</p>
              </div>
            </div>
          </div>
        
          <div class="collapse" id="search4MeCard">
            <div class="card">
              <div class="card-body">
                <a class="mr-1 mb-2 d-xs" data-toggle="modal" data-target="#ViewCredits" title="Available credits">
                  <i class="fa fa-credit-card"></i>
                  View credits
                </a>
                <p>Your access to top talent. Invest in finding the perfect fit</p>
              </div>
            </div>
          </div>
        </div> -->

      </div>
      <div class="row mt-3" #targetElement>
        <div class="col-md-12 dropdown_container">
          <label>Requested Searches: </label>
          <select id="jobTitles" name="jobTitles" (change)="OnChangeRequestedCVs($event)" [(ngModel)]="RequestId"
            class="form-control">
            <option [ngValue]="0">All</option>
            <option *ngFor="let request of CvSearchRequests;" [ngValue]="request.Id"
              title="{{request.JobTitles}} in {{request.LocationText}}">
              [{{request.Id}}] {{request.JobTitles.length > 100 ? (request.JobTitles.slice(0,100) + '...') : request.JobTitles}}
              in {{request.LocationText}} {{ request?.Frequency?.EnumKey === "Passive" ? '- passive' :'' }}
            </option>
          </select>
        </div>
        <div class="col-md-6">
          <label>Show: </label>
          <select id="sCvSuitability" name="sCvSuitability" ng-class="styleOptions[selectedValue]"
            (change)="OnCvSuitabilityChange($event)" [(ngModel)]="SuitabilityTypeId"
            class="form-control font-awesome fa"
            [ngClass]="{ 'greenflag': SuitabilityTypeId == 1, 'yellowflag': SuitabilityTypeId == 2, 'redflag': SuitabilityTypeId == 3 , 'blackColor':SuitabilityTypeId==4}">
            <option value="0" class="blackColor">All</option>
            <option *ngFor="let item of SuitabilityTypes" value="{{item.Id}}" class="fa"
              [ngClass]="{'greenflag': item.Id == 1, 'yellowflag': item.Id == 2, 'redflag': item.Id == 3 , 'blackColor':item.Id == 4}">
              {{item.Description}} <span>&#xf024;</span>
            </option>
          </select>
        </div>
        <div class="col-md-6">
          <label for="dtRespondedOn">Date: </label>
          <div>
            <input type="date" id="dtRespondedDate" name="dtRespondedDate" [(ngModel)]="SelectedDate"
              class="form-control">
          </div>
        </div>
      </div>
      <div class="row mt-2">
        <div [ngClass]="{'col-md-3': RequestId > 0, 'col-md-4' : RequestId <= 0 }">
          <label>Search By Name: </label>
          <input type="text" id="txtCandidateName" name="txtCandidateName" [(ngModel)]="CandidateName"
            class="form-control" placeholder="e.g Robert">
        </div>

        <div [ngClass]="{'col-md-3': RequestId > 0, 'col-md-4' : RequestId <= 0 }">
          <label>Search By Email: </label>
          <input type="text" id="txtCandidateEmail" name="txtCandidateEmail" [(ngModel)]="EnterEmailAddress"
            class="form-control" placeholder="e.g <EMAIL>">
        </div>
        <div [ngClass]="{'col-md-6': RequestId > 0, 'col-md-4' : RequestId <= 0 }">
          <div class="row m-0 align-items-end justify-content-between">
            <div class="col p-0">
              <label>Search By Phone: </label>
              <input type="text" id="txtCandidateMobile" name="txtCandidateMobile" [(ngModel)]="EnterMobileNumber"
                class="form-control mb-1" placeholder="e.g +44 1234567">
            </div>
            <div class="col p-0 pl-md-3 pl-2">
              <button type="button" id="btnSearch" class="btn btn-primary btn-download float-right w-100"
                (click)="LoadScreenCVsResult()"><i class="fa fa-search text-success"></i> <span *ngIf="!ContextService.IsMobileView">Search</span></button>
            </div>
          </div>
        </div>
      </div>
      <div class="row my-3">
        <div class=" col-md-12 ">
          <b *ngIf="ScreenedCVsService.TotalRecords > 0">{{ScreenedCVsService.TotalRecords}} CVs available</b>
          <b class="text-danger" *ngIf="IsLoaded && ScreenedCVsService.TotalRecords > 0 == false">Your Search4Me results are updating. Please check back shortly.</b>
        </div>
      </div>
      <smart-cv-card [Candidates]="ScreenedCVsService.Candidates" (CandidateDetailsDisplay)="CandidateDetailsDisplay($event)"
        [IsScreenedCVsResult]="true" [IsHideIconRequired]="true" (OnHideCV)="OnHideCV($event)" [CompanyVerificationId]="CompanyVerificationStatusId"></smart-cv-card>
      <smart-pagination *ngIf="ScreenedCVsService.TotalRecords > 0" [TotalRecords]="ScreenedCVsService.TotalRecords" [PageNumber]="PageNumber"
        (PageNumberChanged)="OnPageNumberChanged($event)" (PageSizeChanged)="OnPageSizeChanged($event)"
        [PageSize]="PageSize">
      </smart-pagination>
    </div>
  </section>
</div>

<smart-modal [Id]="'RequestScreenedCVsModal'" *ngIf="RequestScreenedCVsModal"[Title]="'Request a CV Search'" [SizeClass]="'modal-lg'" [ShowFooter]="false" [ZIndex]="1051">
  <div modal-body style="max-height: 610px;">
    <screened-cvs-request  *ngIf="showScreenedCvsRequest" [RequestId]="SelectedEditRequestId" (OnRequestSave)="OnRequestSave($event)"></screened-cvs-request>
  </div>
</smart-modal>

<smart-modal [Id]="'ExportToExcelModal'" [Title]="'Export to Excel'" [SizeClass]="'modal-lg'">
  <div modal-body>
    <div class="row form-group">
      <div class="col-md-6">
        <label>Job Title:</label>
        <select id="sJobTitlesExportAll" name="sJobTitlesExportAll" [(ngModel)]="ExportAllRequestId"
          class="form-control">
          <option value="0">All</option>
          <option *ngFor="let request of CvSearchRequests;" value="{{request.Id}}">[{{request.Id}}] {{request.JobTitles |
            slice:0:20}} in {{request.LocationText}}</option>
        </select>
      </div>
      <div class="col-md-6">
        <label>Rank:</label>
        <select id="sCvSuitabilityOnExport" name="sCvSuitabilityOnExport"
          [(ngModel)]="ExportAllSuitabilityTypeId" class="form-control">
          <option value="0">All</option>
          <option *ngFor="let item of SuitabilityTypes" value="{{item.Id}}">{{item.Description}}</option>
        </select>
      </div>
    </div>
    <div class="row form-group">
      <div class="col-md-6">
        <label>No. Of Records:</label>
        <select id="NoOFrecords" name="NoOFrecords" [(ngModel)]="ExportAllNoOfRecords" class="form-control">
          <option value="10">10</option>
          <option value="30">30</option>
          <option value="50">50</option>
          <option value="100">100</option>
          <!-- <option value="500">500</option> -->
        </select>
      </div>
      <div class="col-md-6">
        <div class="row form-group">
          <div class="col-md-6">
            <label>From Date:</label>
            <input type="date" id="dtFromOnExportAll" name="dtFromOnExportAll" [(ngModel)]="ExportAllDateFrom"
              class="form-control" [max]="MaxDateExport" (change)="OnChangeExportDateFrom($event.target.value)">
          </div>
    
          <div class="col-md-6">
            <label>To Date:</label>
            <input type="date" id="dtToOnExportAll" name="dtToOnExportAll" [(ngModel)]="ExportAllDateTo"
              class="form-control" [min]="MinDateExport" [max]="MaxDateExport">
          </div>
        </div>
      </div>
    </div>
  </div>
  <button modal-footer type="button" class="btn btn-primary" (click)="ExportAll()">Export Results</button>
</smart-modal>
<smart-modal [Id]="'ViewCredits'" [Title]="'Your Credits'" [SizeClass]="'modal-m'" >
  <div modal-body style="border: 1px solid #d6d0d0; border-radius: 10px; padding: 15px;">
    <div class="container-fluid" *ngIf="UserFeatures && UserFeatures.length > 0">
      <div class="row form-group credRow" *ngFor="let feature of UserFeatures">
        <div class="col-md-12 rounded">
          <span class="float-left" style="font-size: 16px;">{{ feature.Title }}</span>
          <span class="float-right">
            <strong title="Consumed / Limit">
              {{ feature.Consumed || 0 }}/{{ feature.Limit || 'N/A' }}
            </strong>
          </span>
        </div>
      </div>
    </div>
    <div *ngIf="UserFeatures && UserFeatures.length == 0" class="container-fluid" style="text-align: center;">
      <span style="color:#0e1b5d; font-weight: 800;">No Credits Available.</span> <br>
      <span>Subscribe to a package by clicking the <b style="color: #14c59d;">Add Credits</b> button or click
        <b style="color:  #14c59d;" _ngcontent-apply4u-web-c144="">Contact Us</b> for a customized solution.
      </span>
    </div>
  </div>
  <a modal-footer type="button" class="btn btn-success creditBtn" href="/our-pricing/recruiter">Add Credits</a>
  <a modal-footer type="button" class="btn btn-primary contactBtn" href="/contact-us">Contact Us</a>
  
</smart-modal>

<smart-modal [Id]="'DownloadModal'" [Title]="'Download All'" [SizeClass]="'modal-lg'">
  <div modal-body>
    <div class="row form-group">
      <div class="col-md-6">
        <label>Job Title:</label>
        <select id="CvTitlesOnDownload" name="CvTitlesOnDownload" [(ngModel)]="DownloadAllRequestId"
          class="form-control">
          <option value="0">All</option>
          <option *ngFor="let request of CvSearchRequests;" value="{{request.Id}}">[{{request.Id}}] {{request.JobTitles |
            slice:0:20}} in {{request.LocationText}}</option>
        </select>
      </div>
      <div class="col-md-6">
        <label>Rank:</label>
        <select id="sSuitabilityTypesOnDownload" name="sSuitabilityTypesOnDownload"
          [(ngModel)]="DownloadAllSuitabilityTypeId" class="form-control">
          <option value="0">All</option>
          <option *ngFor="let item of SuitabilityTypes" value="{{item.Id}}">{{item.Description}}</option>
        </select>
      </div>
    </div>
    <div class="row form-group">
      <div class="col-md-6">
        <label>No. Of Records:</label>
        <select id="NoOFrecords" name="NoOFrecords" [(ngModel)]="DownloadAllNoOfRecords" class="form-control">
          <option value="10">10</option>
          <option value="30">30</option>
          <option value="50">50</option>
          <option value="100">100</option>
          <!-- <option value="500">500</option> -->
        </select>
      </div>
      <div class="col-md-6">
        <div class="row form-group">
          <div class="col-md-6">
            <label>From Date:</label>
            <input type="date" id="dtFromOnDownloadAll" name="dtFromOnDownloadAll"
              [(ngModel)]="DownloadAllDateFrom" class="form-control" [max]="MaxDateDownload" (change)="OnChangeDownloadDateFrom($event.target.value)">
          </div>
          <div class="col-md-6">
            <label>To Date:</label>
            <input type="date" id="dtToOnDownloadAll" name="dtToOnDownloadAll" [(ngModel)]="DownloadAllDateTo"
              class="form-control" [min]="MinDateDownload" [max]="MaxDateDownload">
          </div>
        </div>
      </div>
    </div>
  </div>
  <button modal-footer type="button" class="btn btn-primary" (click)="DownloadAll()">Download All</button>
</smart-modal>

<smart-modal [Id]="'ViewRequestModal'" [Title]="'View Requests'" [SizeClass]="'modal-xl'">
  <div modal-body>
    <div class="row form-group align-items-center">
      <div class="col-md-2">
        <label><b>Job Title:</b></label>
      </div>
      <div class="col-md-8">
        <select id="JobTitles" name="JobTitles" (change)="OnJobTitleChange()"
          [(ngModel)]="SelectedViewRequestId" class="form-control">
          <option [ngValue]="0">--Please Select--</option>
          <option *ngFor="let request of CvSearchRequests;" [ngValue]="request.Id">[{{request.Id}}] {{request.JobTitles |
            slice:0:20}} in {{request.LocationText}}  {{ request?.Frequency?.EnumKey === "Passive" ? '- passive' :'' }}</option>
        </select>
      </div>
    </div>
    <hr>
    <div class="row m-0">
      <table class="table m-0">
        <tbody>
          <tr>
            <td class="label-text col-2"><strong>Job Title/s:</strong></td>
            <td id="tdJobTitle" class="td-data">
              <span
                *ngIf="SelectedRequestViewModal">[{{SelectedRequestViewModal.Id}}] {{SelectedRequestViewModal.JobTitles}}</span>
              <span *ngIf="!SelectedRequestViewModal">N/A</span>
            </td>
            <td class="label-text col-2"><strong>Location:</strong></td>
            <td id="tdTargetLocation" class="td-data">
              <span *ngIf="SelectedRequestViewModal">
                <span>{{SelectedRequestViewModal.LocationText}} <strong *ngIf="SelectedRequestViewModal.Radius">Within {{SelectedRequestViewModal.Radius}} miles</strong></span>
              </span>
              <span *ngIf="!SelectedRequestViewModal">N/A</span>
            </td>
          </tr>
          <tr>
            <td class="label-text col-2"><strong>Industry:</strong></td>
            <td id="tdTargetSectors" class="td-data">
              <span
                *ngIf="SelectedRequestViewModal">{{SelectedRequestViewModal.SectorText}}</span>
              <span *ngIf="!SelectedRequestViewModal">N/A</span>

            </td>
            <td class="label-text col-2"><strong>Salary Range:</strong></td>
            <td id="tdSalaryRange" class="td-data" colspan="3">
              <span>{{SalaryRange}}</span>
            </td>
          </tr>
          <tr>
            <td class="label-text col-2"><strong>CV Age:</strong></td>
            <td id="tdSearchLength" class="td-data">

              <span
                *ngIf="SelectedRequestViewModal && SelectedRequestViewModal.CVAge">{{SelectedRequestViewModal.CVAge.Description}}</span>
              <span
                *ngIf="!SelectedRequestViewModal || !SelectedRequestViewModal.CVAge">N/A</span>

            </td>
            <td class="label-text col-2"><strong>Frequency:</strong></td>
            <td id="tdFrequency" class="td-data">
              <span
                *ngIf="SelectedRequestViewModal && SelectedRequestViewModal.Frequency">{{SelectedRequestViewModal.Frequency.Description}}</span>
              <span
                *ngIf="!SelectedRequestViewModal || !SelectedRequestViewModal.Frequency">N/A</span>
            </td>
          </tr>
          <tr>
            <td class="label-text col-2"><strong>Job Details:</strong></td>
            <td id="tdSalaryRange" class="td-data">
              <span
                *ngIf="SelectedRequestViewModal && SelectedRequestViewModal.JobDetails && SelectedRequestViewModal.JobDetails != ''">{{SelectedRequestViewModal.JobDetails}}</span>
              <span
                *ngIf="!SelectedRequestViewModal || !SelectedRequestViewModal.JobDetails || SelectedRequestViewModal.JobDetails == ''">N/A</span>
            </td>

            <!-- <td class="label-text col-2"><strong>Search Option:</strong></td>
            <td id="tdSearchOption" class="td-data">
              <span>Free CV Search4U</span>
              <span>Premium CV Search4U</span>             
            </td> -->
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <button modal-footer *ngIf="ContextService.IsCompanyAdmin || ContextService.LoggedInUserId == SelectedRequestViewModal?.RequestedByUserId" [disabled]="(SelectedViewRequestId == 0)" type="button" class="btn btn-danger"
    (click)="DeleteSelectedRequest(SelectedRequestViewModal.Id)">Delete Request</button>
    <button modal-footer *ngIf="!ContextService.IsCompanyAdmin && ContextService.LoggedInUserId != SelectedRequestViewModal?.RequestedByUserId" [disabled]="(SelectedViewRequestId == 0)" type="button" class="btn btn-danger"
    (click)="RemoveSelectedRequest(SelectedRequestViewModal.Id)">Remove Request</button>
    <button modal-footer *ngIf="ContextService.IsCompanyAdmin || ContextService.LoggedInUserId == SelectedRequestViewModal?.RequestedByUserId" [disabled]="(SelectedViewRequestId == 0)" type="button" class="btn btn-primary"
    data-toggle="modal" data-target="#RequestScreenedCVsModal" (click)="RequestScreenedCVsModal = true;SelectedEditRequestId=SelectedViewRequestId;">Edit Request</button>
</smart-modal>

<candidate-contact-detail-modal *ngIf="CandidateIdForContactDetails" [CandidateId]="CandidateIdForContactDetails"></candidate-contact-detail-modal>