import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GetRegions, BrowseService } from '@apply4u/services/browse-content/browse.service';
import { Sector } from '@apply4u/models/BrowseA4UContents/sector';
import { ContextService } from '@apply4u/services/context-service';
import { HttpClientWrapper } from "@apply4u/services";
import { Location} from '@apply4u/models/BrowseA4UContents/location';
import { ScrollToTop } from '@apply4u/shared/helpers/common/common-helpers';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { BrowseCVs_RouteUrl, BrowseCVsAllLocation_RoutedUrl } from '@apply4u/shared/constant/navigation/navigation';
import { environment } from '@environment/environment';
import { Bread<PERSON>rumbTitleHome, BreadCrumbTitleBrowseCVs} from '@apply4u/shared/constant/constant';
import { IsAny } from '@apply4u/shared/helpers/common/type-safe-helpers';

@Component({
  selector: 'app-browse-cv',
  templateUrl: './browse-cv.component.html',
  styleUrls: ['./browse-cv.component.css']
})
export class BrowseCvComponent implements OnInit, OnDestroy {
  Sectors: Sector[][] = [];
  LocationRegion: Location[];
  BreadCrumbSchema: BreadCrumbs;
  private _unsubscribeAll: ReplaySubject<boolean> = new ReplaySubject(1);
  RequiredColumns = 2;
  BrowseCVsAllLocation_RoutedUrl = BrowseCVsAllLocation_RoutedUrl;

  constructor(private contextService: ContextService,
    private httpClient: HttpClientWrapper,
    private browseService: BrowseService,
    private metaService: MetaWrapperService) { 
    }

  ngOnInit() {
    this.GetSeoSectors();
    this.GetSeoRegions();
    this.PopulateBreadCrumbsDisplayList();

    ScrollToTop(this.contextService.IsBrowser);
  }

  GetSeoSectors(): void {
    this.browseService.GetSeoSectors(this.RequiredColumns)
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(sector => {
        if (IsAny(sector)) {
          this.Sectors = sector;
        }
      });
  }
  
  GetSeoRegions(): void {
    GetRegions(this.httpClient).pipe(takeUntil(this._unsubscribeAll))
      .subscribe(result => {
        if (IsAny(result)) {
          this.LocationRegion = result;
        }
      });
  }
  
  ngOnDestroy(): void {
    if (this.contextService.IsBrowser) {
      this._unsubscribeAll.next(true);
      this._unsubscribeAll.complete();
    }
  }

  PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();
    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleHome;
    listItem.item = `${environment.HostName}`;
    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem2 = new ListItem();
    listItem2.position = 2;
    listItem2.name = BreadCrumbTitleBrowseCVs;
    listItem2.item = `${environment.HostName}${BrowseCVs_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem2);
    let breadcrumbSchemaString = JSON.stringify(this.BreadCrumbSchema);
    this.metaService.CreateBreadCrumbsScript(breadcrumbSchemaString); 
  }

}