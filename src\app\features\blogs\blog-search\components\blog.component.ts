import { Component, OnInit } from '@angular/core';
import { MetaWrapperService } from '@apply4u/services/meta-wrapper.service';
import { LoadingService } from '@apply4u/services/loading.service';
import { BlogsService } from '@apply4u/services/blogs.service';
import { MetaDataService } from '@apply4u/services/meta-data.service';
import { BlogReadOnly } from '@apply4u/models/blogs/blog-read-only';
import { BlogSearchPost } from '@apply4u/models/blogs/blog-search-post';
import { IsAny, IsNotNull, IsNotNullOrEmpty, IsNotZero, IsNotZeroNumber } from '@apply4u/shared/helpers/common/type-safe-helpers';
import { BreadcrumbsTitleBlogs, DefaultPageNumber, DefaultPageSize } from '@apply4u/shared/constant/constant';
import { ActivatedRoute } from "@angular/router";
import { Sector } from '@apply4u/models/sector';
import { MasterDataService } from '@apply4u/services/masterdata.service';
import { PopularTagSearches } from '@apply4u/shared/constant/tag-search/popular-tags';
import { Shared } from '@apply4u/models/shared';
import { BlogSearchService } from '@apply4u/services/data-services/blog-search.service';
import { ContextService } from '@apply4u/services/context-service';
import { BreadCrumbs, ListItem } from '@apply4u/models/seo/bread-crumbs';
import { BreadCrumbTitleHome} from '@apply4u/shared/constant/constant';
import { environment } from '@environment/environment';
import { Blog_RouteUrl } from '@apply4u/shared/constant/navigation/navigation';

@Component({
  selector: 'app-blog',
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.css']
})
export class BlogComponent implements OnInit {
  PageNumber: number = DefaultPageNumber;
  PageSize: number = DefaultPageSize;
  SearchTerm: string = "";
  BlogsList: BlogReadOnly[];
  PopularPosts: BlogSearchPost[] = [];
  RecentPosts: BlogSearchPost[] = [];
  Sectors: Sector[] = [];
  BaseUrl: string = "";
  TotalRecords: number = 0;
  SearchTag: string = "";
  PopularTags = PopularTagSearches;
  SectorName: string = '';
  IsLoaded: boolean = false;
  ContextServices: ContextService;
  BreadCrumbSchema: BreadCrumbs;

  constructor(
    private metaService: MetaWrapperService,
    private loadingService: LoadingService,
    private contextService: ContextService,
    private blogService: BlogsService,
    private metadataService: MetaDataService,
    private activatedRoute: ActivatedRoute,
    private masterDataService: MasterDataService,
    private blogSearchService: BlogSearchService) {
      this.ContextServices = this.contextService;
    this.BaseUrl = Shared.baseUrl;
  }

  ngOnInit() {
    //#region SEO META TAGS
    this.metaService.SetPageTitle('Blogs | Jobsite | Recruitment | Jobcentre | Hire | Apply4U');
    this.metaService.UpdateMetaTag('description', 'Apply4U blogs will guide you in finding jobs and to recruit best employee. Our jobsite will highlight best practices for job search and hiring.');
    this.metaService.CreateCanonicalURL(this.metaService.GetCurrentPageURLWithoutQueryParams());
    this.metaService.CreateBreadCrumbsScript(this.metadataService.BlogsMainComponentBreadCrumbs);
    //#endregion
    this.InitializeSearchParams();
    if(this.contextService.IsBrowser){
      this.PopulateBreadCrumbsDisplayList();
    }
  }

  OnPageNumberChanged(pageNumber: number): void {
    if (IsNotZeroNumber(pageNumber)) {
      this.PageNumber = pageNumber;
      this.blogSearchService.SetBlogQueryParams(this.SearchTerm, null, this.PageNumber, this.PageSize);
    }
  }

  OnPageSizeChanged(pageSize: number): void {
    if (IsNotZero(pageSize)) {
      this.PageNumber = DefaultPageNumber;
      this.PageSize = pageSize;
      this.blogSearchService.SetBlogQueryParams(this.SearchTerm, null, this.PageNumber, this.PageSize);
    }
  }

  OnSearchTermChanged(searchTag: string): void {
    if(IsNotNullOrEmpty(searchTag)){
      this.SearchTag = searchTag;
      this.blogSearchService.SetBlogQueryParams(this.SearchTag, null, this.PageNumber, this.PageSize);
    }    
  }

  private InitializeSearchParams(): void {
    this.activatedRoute.queryParamMap.subscribe((queryParamMap) => {
      if (queryParamMap.has("page")) {
        this.PageNumber = parseInt(queryParamMap.get("page"));
      }

      if (queryParamMap.has("pagesize")) {
        this.PageSize = parseInt(queryParamMap.get("pagesize"));
      }

      if (queryParamMap.has("searchTerm")) {
        this.SearchTerm = queryParamMap.get("searchTerm");
      }

      if (queryParamMap.has("sectorName")) {
        this.SectorName = queryParamMap.get("sectorName");
      }

      this.BlogsDataOnLoad();
    });
  }

  private BlogsDataOnLoad(): void {
    this.GetPopularPosts();
    this.GetRecentPosts();
    this.SearchBlogs();
    this.GetSectors();
  }

  private GetPopularPosts(): void {
    this.blogService.BlogsPopularPost().subscribe({
      next: popular => {
        if (IsAny(popular)) {
          this.PopularPosts = [];
          this.PopularPosts = popular;
          this.PopularPosts.forEach(element => {
            if (!element.RedirectUrl.includes('/blogs/')) {
              element.RedirectUrl = `/blogs${element.RedirectUrl}`;
            }
          });
        }
      }, error: error => {
        this.PopularPosts = [];
      }
    });
  }

  private GetRecentPosts(): void {
    this.blogService.BlogsRecentPost().subscribe({
      next: recent => {
        if (IsAny(recent)) {
          this.RecentPosts = [];
          this.RecentPosts = recent;
          this.RecentPosts.forEach(element => {
            if (!element.RedirectUrl.includes('/blogs/')) {
              element.RedirectUrl = `/blogs${element.RedirectUrl}`;
            }
          });
        }
      }, error: error => {
        this.RecentPosts = [];
      }
    });
  }

  private SearchBlogs(): void {
    if (this.contextService.IsClientSideLoading) {
      this.loadingService.Show();
    }

    this.blogService.BlogsSearch(this.SearchTerm,this.SectorName, this.PageNumber, this.PageSize, this.contextService.IsJobSeeker).subscribe({
      next: search => {
        if (IsNotNull(search) && IsAny(search.Response)) {
          this.BlogsList = [];
          this.BlogsList = search.Response;
          this.TotalRecords = search.TotalRecords;
          this.BlogsList.forEach(element => {
            if (!element.RedirectUrl.includes('/blogs/')) {
              element.RedirectUrl = `/blogs${element.RedirectUrl}`;
            }
          });
        } else {
          this.BlogsList = [];
          this.TotalRecords = 0;
        }

        this.IsLoaded = true;
        this.loadingService.Hide();
      }, error: error => {
        this.BlogsList = [];
        this.loadingService.Hide();
      }
    });
  }

  private GetSectors(): void {
    this.masterDataService.getSector(null).subscribe(result => {
      if (IsAny(result)) {
        this.Sectors = result;
      }
    });
  }

  private PopulateBreadCrumbsDisplayList(): void {
    this.BreadCrumbSchema = new BreadCrumbs();

    let listItem = new ListItem();
    listItem.position = 1;
    listItem.name = BreadCrumbTitleHome;

    if (this.contextService.IsJobSeeker) {
      listItem.item = `${environment.HostName}${'/'}`;
    } else if (this.contextService.IsRecruiter) {
      listItem.item = `${environment.HostName}${'/recruiter'}`;
    }

    this.BreadCrumbSchema.itemListElement.push(listItem);

    let listItem1 = new ListItem();
    listItem1.position = 2;
    listItem1.name = BreadcrumbsTitleBlogs;
    listItem1.item = `${environment.HostName}${Blog_RouteUrl}`;
    this.BreadCrumbSchema.itemListElement.push(listItem1);
  }


}