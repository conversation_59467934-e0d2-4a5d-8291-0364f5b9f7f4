<section class="PageBg">
<div class="container">
  <breadcrumb-seo [BreadCrumbSchema]="BreadCrumbSchema"></breadcrumb-seo>

  <div *ngIf="!IsUnSubPage" class="row pt-3 CareerAdvice m-0">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0">
      <h1>Manage Alerts</h1>
    </div>
  </div>
    
  <!-- Manage Email Alerts -->
  <div class="row m-0">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 MainCanvas">
      <h2><i class="fa fa-bell"></i> Manage Email Alerts
      </h2>
      <div class="accordion" id="MainEMailAlertsAcc">
        <div *ngIf="emailEventGroups && emailEventGroups.length>0">
          <div *ngFor="let group of emailEventGroups; let index=index;">
  
            <div class="card mt-2">
              <div class="card-header">
                <div class="row">
                  <div class="col-md-11 col-sm-11 col-xs-11 collapsed rorateIcon" data-toggle="collapse"
                    [attr.data-target]="'#collapse-' + index" aria-expanded="false" (click)="showSaveSearche(group)">
                    <span class="title">{{group.Title}}</span>
                    <i class="fas fa-angle-down rotate-icon ml-1"></i>
                    <div class="row">
                      <div class="col-md-10 col-sm-10 col-xs-10">
                        <span class="mb-2">{{group.Description}}</span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-1 col-sm-1 col-xs-1">
                    <div class="custom-control custom-switch" style="float: right;">
                      <input type="checkbox" class="custom-control-input" id="UnsubFromAllswitch{{index}}"
                        *ngIf="group.IsUnSubGroup" [(ngModel)]="group.IsUnSubGroup"
                        on-change="UnSubOrSubFromGroupButtonHandler(group,false)">
                      <input type="checkbox" class="custom-control-input" id="UnsubFromAllswitch{{index}}"
                        *ngIf="!group.IsUnSubGroup" [(ngModel)]="group.IsUnSubGroup"
                        on-change="UnSubOrSubFromGroupButtonHandler(group,true)">
                      <label class="custom-control-label" for="UnsubFromAllswitch{{index}}"></label>
                    </div>
                  </div>
                </div>
              </div>
              <div [attr.id]="'collapse-' + index" class="collapse">
                <div class="card-body">
                  <div class="row m-0 mt-1" *ngFor="let evenType of group.EmailEventTypes;let eindex=index;last as isLast"
                    [ngClass]="isLast ? '' : 'border-bottom'">
                    <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12 p-0" *ngIf="evenType.Id!=18">
                      <span>
                        <p class="text-primary font-bold mb-1 mt-1">{{evenType?.Description}}</p>
                        <p class="smaller mb-1" *ngIf="evenType.EmailEventGroupId==5">
                          <span>Frequency</span>
                          <select name="emailFrequency" id="FrequencyId" on-change="OnFrequencyChangeHandler(evenType)"
                            [(ngModel)]="evenType.UserEmailConfiguration.FrequencyId" class="freq ml-2">
                            <option *ngFor="let frequency of emailFrequencies" [value]="frequency.Id">
                              {{frequency.Description}}</option>
                          </select>
                        </p>
                      </span>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 text-right p-0" *ngIf="evenType.Id!=18">
                      <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="customSwitch-{{evenType.Id}}"
                          on-change="UnSubOrSubFromEvent(evenType,$event)" [(ngModel)]="evenType.IsUnSubFromEvent">
                        <label class="custom-control-label" for="customSwitch-{{evenType.Id}}"></label>
                      </div>
                    </div>
  
                  </div>
  
                  <div *ngFor="let evenType of group.EmailEventTypes;let eindex=index;">
                    <div *ngIf="evenType.IsSaveSearchExists && saveSearches && saveSearches.length>0">
                      <div class="box-body" [ngClass]="{ 'scroll' :saveSearches.length > 3}">    
                        <div *ngFor="let search of saveSearches; let count=index;" class="box box-widget">
                          <div class="box-body">
                            <div class="row">
                              <div class="col-md-11 col-sm-11 col-xs-11">
                                <p class="text-bold no-margin">Ref [{{ search?.Id }}] (<span *ngIf="search?.SearchReferenceName" innerHtml="{{search?.SearchReferenceName }}"></span>)
                                </p>
                                <p class="text-warning smaller no-margin" *ngIf="search?.DisplayKeyword"><i
                                    class="fas fa-key"></i> Keywords:
                                  <span class="text-dark" innerHtml="{{search?.DisplayKeyword}}"></span>
                                </p>
                                <p class="text-info smaller no-margin">
                                  <span *ngIf="search?.LocationText" class="ref"><i class="fab fa-periscope"></i>
                                    <span *ngIf="search?.LocationText" class="ml-2">{{search?.LocationText }}</span>
                                  </span>
                                </p>
                                <p class="no-margin smaller">
                                  <span *ngIf="search?.CreatedOn"><i class="far fa-calendar-alt"></i>
                                    <span class="ml-2">{{ search?.CreatedOn | date:'dd-MM-yyyy' }}</span>
                                    </span>
                                </p>
                                <p class="no-margin smaller">
                                  <span *ngIf="search?.IsFilterApplyed && (search?.SalaryFrom || search?.SalaryTo || search?.SalaryDuration?.Description)">
                                    <i class="fas fa-coins"></i>
                                    <span *ngIf="search?.SalaryFrom" class="ml-2">{{ search?.SalaryFrom }}</span>
                                    <span *ngIf="!search?.SalaryFrom && search?.SalaryTo">N/A</span>
                                    <span *ngIf="search?.SalaryTo">-</span>
                                    <span *ngIf="search?.SalaryTo">{{ search?.SalaryTo }}</span>
                                    <span *ngIf="search?.SalaryDuration != null && search?.SalaryDuration?.Description">/{{search?.SalaryDuration?.Description }}</span>
                                  </span>
                                </p>
                                <p class="no-margin smaller" *ngIf="search?.JobTypes?.length > 0 || search?.PositionTypes?.length > 0">
                                  <i class="far fa-clock"></i>
                                  <span *ngIf="search?.JobTypes != null && search?.JobTypes.length> 0 && search?.IsJobSearch" class="ml-2">
                                    <span *ngFor="let jobtype of search.JobTypes; let count= index">{{ jobtype?.Description }}
                                      <span *ngIf="count < search?.JobTypes?.length-2"> | </span>
                                    </span>
                                  </span>
                                  <span *ngIf="search?.PositionTypes != null && search?.PositionTypes.length> 0 && search?.IsCvSearch" class="ml-2">
                                    <span *ngFor="let positiontype of search?.PositionTypes; let count= index">{{ positiontype?.Description }}
                                      <span *ngIf="count <search?.PositionTypes?.length-2"> | </span>
                                    </span>
                                  </span>
                                </p>
                                <p class="no-margin smaller" *ngIf="search?.IsFilterApplyed && search?.CandidateTypes?.length> 0 || search?.JobCategories?.length > 0">
                                  <i class="far fa-building"></i>
                                  <span *ngIf="search?.CandidateTypes != null && search?.CandidateTypes.length> 0 && search?.IsCvSearch" class="ml-2">
                                    <span *ngFor="let candidatetypes of search?.CandidateTypes; let count= index">{{ candidatetypes?.Description}}
                                      <span *ngIf="count < search?.CandidateTypes?.length-2"> | </span>
                                    </span>
                                  </span>
                                  <span *ngIf="search?.JobCategories != null && search?.JobCategories.length> 0 && search?.IsJobSearch" class="ml-2">
                                    <span *ngFor="let jobcategory of search?.JobCategories; let count= index">{{ jobcategory?.Description }}
                                      <span *ngIf="count < search?.JobCategories?.length-2"> | </span>
                                    </span>
                                  </span>
                                </p>
                                <p class="no-margin smaller">
                                  <span *ngIf="search?.SearchWithInLast != null || search?.IsBroadMatch">
                                    <i class="fas fa-search"></i>
                                    <span *ngIf="search?.SearchWithInLast == 0 || search?.SearchWithInLast == -1" class="ml-2">Search Within Last All</span>
                                    <span *ngIf="search?.SearchWithInLast == 1" class="ml-2">Search Within Last 24 Hours</span>
                                    <span *ngIf="search?.SearchWithInLast == 2" class="ml-2">Search Within Last 48 Hours</span>
                                    <span *ngIf="search?.SearchWithInLast == 3" class="ml-2">Search Within Last 72 Hours</span>
                                    <span *ngIf="search?.SearchWithInLast == 7" class="ml-2">Search Within Last 1 Week</span>
                                    <span *ngIf="search?.SearchWithInLast == 14" class="ml-2">Search Within Last 2 Weeks</span>
                                    <span *ngIf="search?.SearchWithInLast == 30" class="ml-2">Search Within Last 1 Month</span>
                                    <span *ngIf="search?.SearchWithInLast == 90" class="ml-2">Search Within Last 3 Months</span>
                                    <span *ngIf="search?.SearchWithInLast == 180" class="ml-2">Search Within Last 6 Months</span>
                                    <span *ngIf="search?.SearchWithInLast == 365" class="ml-2">Search Within Last 1 Year</span>
                                    <span *ngIf="search?.IsBroadMatch" class="ml-2">(<i class="fas fa-check text-success fa-sm"></i> Broad Match) </span>
                                  </span>
                                </p>
                                <p class="verticalcut overflow-tip no-margin smaller" *ngIf="search?.Sector?.Id != null">
                                  <i class="fas fa-award"></i>
                                  <span class="text-bold" class="ml-2">Industries: </span>
                                  <span class="badge label-primary br-3 sec" *ngIf="search?.Sector?.SectorSeoName != null && search?.Sector?.SectorSeoName != ''">{{ search?.Sector?.SectorSeoName }}</span>
                                </p>
  
                                <p class="verticalcut overflow-tip no-margin smaller">
                                  <label class="text-warning no-margin">Email Alert</label>
                                  <span *ngIf="emailFrequency?.length> 0" class="ml-2">
                                    <select (change)="UpdateSaveSearchEmailFrequency(search)" class="sea"
                                      name="EmailFrequencyId{{count}}" [(ngModel)]="search.EmailFrequencyId">
                                      <option value="0">Select Please</option>
                                      <option *ngFor="let emailfrequency of emailFrequency" [value]="emailfrequency?.Id">
                                        {{ emailfrequency?.Description }}</option>
                                    </select>
                                  </span>
                                </p>
                              </div>
                              <div class="col-md-1 col-sm-1 col-xs-1">
                                <div class="custom-control custom-switch" style="float: right;">
                                  <input type="checkbox" class="custom-control-input" *ngIf="!search.IsEmailAlertActive"
                                    id="SavedEmailFrequencyId{{count}}" on-change="UnSubOrSubBySavedSearch(search,true)"
                                    [(ngModel)]="search.IsEmailAlertActive">
                                  <input type="checkbox" class="custom-control-input" *ngIf="search.IsEmailAlertActive"
                                    id="SavedEmailFrequencyId{{count}}" on-change="UnSubOrSubBySavedSearch(search,false)"
                                    [(ngModel)]="search.IsEmailAlertActive">
                                  <label class="custom-control-label" for="SavedEmailFrequencyId{{count}}"></label>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
  
                  </div>
  
                  <!-- </div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Affiliate Alerts -->    
  <div class="row m-0" *ngIf="ContextService.IsJobSeeker">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 MainCanvas">
      <h2><i class="fa fa-bell"></i> Affiliate Alerts</h2>

      <!-- Attb Alerts -->
      <div class="accordion" id="AttbAlertAcc">
        <div class="card mt-2">
          <div class="card-header">
          <div class="row">
            <div class="col-md-11 col-sm-11 col-xs-11 collapsed rorateIcon" data-toggle="collapse" [attr.data-target]="'#attbAlertsAcc'" aria-expanded="false">
              <span class="title">ATTB Email Alerts</span>
              <i class="fas fa-angle-down rotate-icon ml-1"></i>
            </div>
            <div class="col-md-1 col-sm-1 col-xs-1 addIcon">
              <div style="float: right;">
                <button type="button" data-toggle="modal" data-target="#ATTBAlertsModal"
                (click)="AddATTBAlert()"><i class="fa fa-plus"></i></button>
              </div>                
            </div>
          </div>
          </div> 
          <!-- (click)="LoadUserIndustries()" -->
          <div [attr.id]="'attbAlertsAcc'" class="collapse">
            <div class="card-body">
              <div class="row m-0">
                <div class="col-md-12 p-0" style="overflow-x: auto;">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th scope="col">Keywords</th>
                        <th scope="col">Location</th>
                        <th scope="col">Radius(miles)</th>
                        <th scope="col">Job Contract</th>
                        <th scope="col">Job Type</th>
                        <th scope="col">Min Salary (K)</th>
                        <th scope="col">Max Salary (K)</th>
                        <th scope="col">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let alert of alreadySetAlerts">
                        <td >{{alert.DesiredKewords}}</td>
                        <td >{{alert.DesiredLocation}}</td>
                        <td  *ngIf="alert.Radius == 0">&nbsp;</td>
                        <td  *ngIf="alert.Radius != 0">{{alert.Radius}}</td>
                        <td  *ngIf="alert.JobContract == 0">&nbsp;</td>
                        <td  *ngIf="alert.JobContract == 1">Permanent</td>
                        <td  *ngIf="alert.JobContract == 2">Temporary</td>
                        <td  *ngIf="alert.JobContract == 3">Contract</td>
                        <td  *ngIf="alert.JobType == 0">&nbsp;</td>
                        <td  *ngIf="alert.JobType == 1">FullTime</td>
                        <td  *ngIf="alert.JobType == 2">PartTime</td>
                        <td  *ngIf="alert.JobType == 3">Work From Home</td>
                        <td  *ngIf="alert.MinimumSalary == 0">&nbsp;</td>
                        <td  *ngIf="alert.MinimumSalary != 0">{{alert.MinimumSalary}}</td>
                        <td  *ngIf="alert.MaximumSalary == 0">&nbsp;</td>
                        <td  *ngIf="alert.MaximumSalary != 0">{{alert.MaximumSalary}}</td>
                        <td >
                          <!-- <button (click)="EditAddress(address)" data-toggle="modal" data-target="#AddressModal"><i
                            class="fa fa-pencil-alt"></i></button> -->
                            <i title="Edit" class="pointer" class="fas fa-edit text-primary" on-click="OnEditAttbAlert(alert)" data-toggle="modal" data-target="#ATTBAlertsModal"></i>&nbsp;
                            <i title="Delete" class="pointer" class="fa fa-trash text-primary" on-click="OnDeleteAttbAlert(alert.Id)"></i>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>          
    </div>

    <!-- TotalJob Alerts -->
    <div class="accordion" id="TotalJobAlertAcc">
      <div class="card mt-2">
        <div class="card-header">
          <div class="row">
            <div class="col-md-11 col-sm-11 col-xs-11 collapsed rorateIcon" data-toggle="collapse" href="#totalJobAlertsAcc">
              <span class="title">TotalJob Email Alerts</span>
              <i class="fas fa-angle-down rotate-icon ml-1"></i>
            </div>
            <div class="col-md-1 col-sm-1 col-xs-1 addIcon">
              <div style="float: right;">
                <button type="button" data-toggle="modal" data-target="#TotalJobAlertsModal"
                (click)="AddTotalJobAlert()"><i class="fa fa-plus addIcon"></i></button>
              </div>                
            </div>
          </div>
        </div> 
        <!-- (click)="LoadUserIndustries()" -->
        <div id="totalJobAlertsAcc" class="collapse">
          <div class="card-body">
            <div class="row m-0">
              <div class="col-md-12 p-0" style="overflow-x: auto;">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th scope="col">Keywords</th>
                      <th scope="col">Location</th>
                      <th scope="col">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let totalJobAlert of TotalJobAlerts">
                      <td>{{totalJobAlert.SearchKeyword}}</td>
                      <td>{{totalJobAlert.LocationText}}</td>
                      <td>
                        <i title="Delete" class="pointer" class="fa fa-trash text-primary" on-click="OnDeleteTotalJobAlert(totalJobAlert.Id)"></i>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
          </div>
        </div>
      </div>

    </div>
    </div>

    <!-- JobG8 Alerts Alerts -->
    <!-- <div class="accordion" id="JobGateAlertAcc">
      <div class="card mt-2">
        <div class="card-header">
          <div class="row">
            <div class="col-md-11 col-sm-11 col-xs-11 collapsed rorateIcon" data-toggle="collapse" href="#jobGateAlertsAcc">
              <span class="title"> Jobg8 Email Alerts</span>
              <i class="fas fa-angle-down rotate-icon ml-1"></i>
            </div>
            <div class="col-md-1 col-sm-1 col-xs-1 addIcon">
              <div style="float: right;">
                <button type="button" data-toggle="modal" data-target="#JobG8AlertsModal"
                (click)="AddJobGAteAlert()"><i class="fa fa-plus addIcon"></i></button>
              </div>                
            </div>
          </div>
        </div> 
        <div id="jobGateAlertsAcc" class="collapse">
          <div class="card-body">
            <div class="row m-0">
              <div class="col-md-12 p-0" style="overflow-x: auto;">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th scope="col">Keywords</th>
                      <th scope="col">Location</th>
                      <th scope="col">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let jobGateAlert of JobGateAlerts">
                      <td>{{jobGateAlert.SearchKeyWord}}</td>
                      <td>{{jobGateAlert.LocationText}}</td>
                      <td>
                        <i title="Delete" class="pointer" class="fa fa-trash text-primary" on-click="OnDeleteJobGateAlert(jobGateAlert.Id)"></i>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
          </div>
        </div>
      </div>

    </div>
    </div> -->

    </div>
  </div>

</div>
</section>

<!--ATTB Add/Edit Modal-->
<smart-modal [Id]="'ATTBAlertsModal'" [Title]="'ATTB Alert'" [SizeClass]="'modal-lg'">
  <div modal-body>
    <form [formGroup]="AttbAlertForm" novalidate>
    <div class="row EditProInfo">
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="ATTBSearchKeyword">Desired Job Title:</label>
        <input type="text" class="d-none" formControlName="SearchKeyword" [(ngModel)]="attbJobAlert.DesiredKewords" />
        <app-keyword-autocomplete class="formMain" [SearchKeywordTitle]="attbJobAlert.DesiredKewords"
          (SearchKeywordTitleChanged)="attbJobAlert.DesiredKewords=$event"
          (IsEnterKeyPressed)="SearchEnterKeyPressed($event)" [HideRecentSearches]="true" [Placeholder]="'Job title'">
        </app-keyword-autocomplete>
        <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['SearchKeyword'].invalid && AttbAlertForm.controls['SearchKeyword'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter desired job title
        </div>                
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="ATTBJobLocationText">Desired Location:</label>
        <input type="text" class="d-none" formControlName="LocationText" [(ngModel)]="attbJobAlert.DesiredLocation"/>
        <app-smart-location-autocomplete [SearchKeywordLocation]="attbJobAlert.DesiredLocation"
          (SearchKeywordLocationChanged)="OnAttbLocationChanged($event)">
        </app-smart-location-autocomplete>
                <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['LocationText'].invalid && AttbAlertForm.controls['LocationText'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter desired Location
        </div>
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="JobType">Job Type:</label>
          <select formControlName="JobType" id="sJobType" name="sJobType" [(ngModel)]="attbJobAlert.JobType" class="form-control">
            <option value="">--Select Option--</option>
            <option value="1">FullTime</option>
            <option value="2">PartTime</option>
            <option value="3">Work From Home</option>
          </select>
        <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['JobType'].invalid && AttbAlertForm.controls['JobType'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please select a valid job type
        </div>                
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="JobContract">Job Contract:</label>
          <select formControlName="JobContract" id="sJobContract" name="sJobContract" [(ngModel)]="attbJobAlert.JobContract" class="form-control">
            <option value="">--Select Option--</option>
            <option value="1">Permanent</option>
            <option value="2">Temporary</option>
            <option value="3">Contract</option>
          </select>
          <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['JobContract'].invalid && AttbAlertForm.controls['JobContract'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please select a valid job contract
        </div>
      </div>
    </div>
    <!-- <div class="row EditProInfo">
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label class="text-strong">Currency</label>
          <select formControlName="SalaryCurrencyId" id="SalaryCurrency" name="SalaryCurrency" class="form-control form-group" [(ngModel)]="attbJobAlert.SalaryCurrencyId">
              <option [value]="0">--Select Option--</option>
            <option *ngFor="let currency of currencies;let idx=index" [value]="currency.Id">{{currency.Description}} ({{currency.Code}})</option>
          </select>
          <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['SalaryCurrencyId'].invalid && AttbAlertForm.controls['SalaryCurrencyId'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please select a valid job type
        </div> 
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label class="text-strong">Salary Per</label>
        <select formControlName="JobDurationId" class="form-control form-group" name="JobDurationId" [(ngModel)]="attbJobAlert.JobDurationId" on-change="salaryPerChange()">
            <option value="0">--Select Option--</option>
          <option *ngFor="let d of salaryDurations" [value]="d.Id">{{d.Description}}</option>
        </select>
        <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['JobDurationId'].invalid && AttbAlertForm.controls['JobDurationId'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please select a valid job type
        </div>
      </div>
    </div> -->
    <div class="row EditProInfo">
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="attbMinimumSalary">Min Salary</label>
          <input numbersOnly min="0" maxlength="7" formControlName="MinimumSalary" type="text" id="attbMinimumSalary" name="attbMinimumSalary" class="form-control form-group" 
          placeholder="e.g. 1000" [(ngModel)]="attbJobAlert.MinimumSalary" on-keypress="isNumber($event)" >
          <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['MinimumSalary'].invalid && AttbAlertForm.controls['MinimumSalary'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter min salary
        </div>
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="attbMaximumSalary">Max Salary</label>
          <input numbersOnly min="0" maxlength="7" formControlName="MaximumSalary" type="text" id="attbMaximumSalary" name="attbMaximumSalary" class="form-control form-group" 
          placeholder="e.g. 2000" [(ngModel)]="attbJobAlert.MaximumSalary" on-keypress="isNumber($event)">
      <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['MaximumSalary'].invalid && AttbAlertForm.controls['MaximumSalary'].errors['required']"
          class="alert alert-danger">
          <i class="fas fa-info-circle"></i> Please enter max salary
      </div>
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="RadiuMiles">Radius (miles):</label>
        <input  formControlName="Radius" class="d-none" [(ngModel)]="attbJobAlert.Radius"/>
        <select class="form-control" [disabled]="!attbJobAlert.DesiredLocation" name="exampleFormControlSelect1"
        [(ngModel)]="attbJobAlert.Radius" [ngModelOptions]="{ standalone: true }">
        <option [value]=0>{{!attbJobAlert.DesiredLocation ? 'For Distance, please add location' : 'Within?'}}
        </option>
        <option [value]=1>1 Mile</option>
        <option [value]=2>2 Miles</option>
        <option [value]=5>5 Miles</option>
        <option [value]=10>10 Miles</option>
        <option [value]=15>15 Miles</option>
        <option [value]=20>20 Miles</option>
        <option [value]=25>25 Miles</option>
        <option [value]=30>30 Miles</option>
        <option [value]=40>40 Miles</option>
        <option [value]=50>50 Miles</option>
        <option [value]=70>70 Miles</option>
        <option [value]=100>100 Miles</option>
        <option [value]=1000>Any</option>
      </select>
        <div *ngIf="IsAttbAlertFormSubmitted && AttbAlertForm.controls['Radius'].invalid && AttbAlertForm.controls['Radius'].errors['required']"
          class="alert alert-danger">
          <i class="fas fa-info-circle"></i> Please enter radius
      </div>
      </div>
    </div>
  </form>
  </div>
  <button modal-footer type="button" class="btn btn-primary" (click)="OnSubmitAttbAlertForm()">Submit</button>
</smart-modal>

<!--TotalJob Add/Edit Modal-->
<smart-modal [Id]="'TotalJobAlertsModal'" [Title]="'TotalJob Alert'" [SizeClass]="'modal-lg'">
  <div modal-body style="height: 200px;">
    <form [formGroup]="TotalJobAlertForm" novalidate>
    <div class="row EditProInfo">
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="TotalJobSearchKeyword">Desired Job Title:</label>
        <input type="text" class="d-none" formControlName="SearchKeyword" [(ngModel)]="TotalJobAlert.SearchKeyword" />
        <app-keyword-autocomplete class="formMain" [SearchKeywordTitle]="TotalJobAlert.SearchKeyword"
          (SearchKeywordTitleChanged)="TotalJobAlert.SearchKeyword=$event"
          (IsEnterKeyPressed)="SearchEnterKeyPressed($event)" [HideRecentSearches]="true" [Placeholder]="'Job title'">
        </app-keyword-autocomplete>        
        <div *ngIf="IsTotalJobFormSubmitted && TotalJobAlertForm.controls['SearchKeyword'].invalid && TotalJobAlertForm.controls['SearchKeyword'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter Desired Job Title
        </div>
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="TotalJobLocationText">Desired Location:</label>
        <input type="text" class="d-none" formControlName="LocationText" [(ngModel)]="TotalJobAlert.LocationText"/>
        <app-smart-location-autocomplete [SearchKeywordLocation]="TotalJobAlert.LocationText"
          (SearchKeywordLocationChanged)="OnTotslJobLocationChange($event)">
        </app-smart-location-autocomplete>
        <div *ngIf="IsTotalJobFormSubmitted && TotalJobAlertForm.controls['LocationText'].invalid && TotalJobAlertForm.controls['LocationText'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter desired Location
        </div>
      </div>
    </div>
  </form>
  </div>
  <button modal-footer type="button" class="btn btn-primary" (click)="OnSubmitTotalJobAlertForm()">Submit</button>
</smart-modal>

<!--Jobg8 Add/Edit Modal-->
<smart-modal [Id]="'JobG8AlertsModal'" [Title]="'Jobg8 Alert'" [SizeClass]="'modal-lg'">
  <div modal-body style="height: 200px;">
    <form [formGroup]="JobGateAlertForm" novalidate>
    <div class="row EditProInfo">
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="Jobg8SearchKeyword">Desired Job Title:</label>
        <input type="text" class="d-none" formControlName="SearchKeyWord" [(ngModel)]="JobGateAlert.SearchKeyWord" />
        <app-keyword-autocomplete class="formMain" [SearchKeywordTitle]="JobGateAlert.SearchKeyWord"
          (SearchKeywordTitleChanged)="JobGateAlert.SearchKeyWord=$event"
          (IsEnterKeyPressed)="SearchEnterKeyPressed($event)" [HideRecentSearches]="true" [Placeholder]="'Job title'">
        </app-keyword-autocomplete>        
        <div *ngIf="IsJobGateFormSubmitted && JobGateAlertForm.controls['SearchKeyWord'].invalid && JobGateAlertForm.controls['SearchKeyWord'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter Desired Job Title
        </div>
      </div>
      <div class="col-md-6 col-sm-12 col-xs-12">
        <label for="Jobg8LocationText">Desired Location:</label>
        <input type="text" class="d-none" formControlName="LocationText" [(ngModel)]="JobGateAlert.LocationText"/>
        <app-smart-location-autocomplete [SearchKeywordLocation]="JobGateAlert.LocationText"
          (SearchKeywordLocationChanged)="OnJobGateLocationChange($event)">
        </app-smart-location-autocomplete>
        <div *ngIf="IsJobGateFormSubmitted && JobGateAlertForm.controls['LocationText'].invalid && JobGateAlertForm.controls['LocationText'].errors['required']"
                class="alert alert-danger">
                <i class="fas fa-info-circle"></i> Please enter desired Location
        </div>
      </div>
    </div>
  </form>
  </div>
  <button modal-footer type="button" class="btn btn-primary" (click)="OnSubmitJobGateAlertForm()">Submit</button>
</smart-modal>